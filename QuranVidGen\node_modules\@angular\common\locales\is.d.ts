/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
declare function plural(val: number): number;
declare const _default: (string | number | number[] | (string | undefined)[] | typeof plural | (string[] | undefined)[] | {
    AUD: (string | undefined)[];
    BRL: (string | undefined)[];
    CAD: (string | undefined)[];
    EUR: (string | undefined)[];
    GBP: (string | undefined)[];
    INR: (string | undefined)[];
    JPY: string[];
    KRW: (string | undefined)[];
    MXN: (string | undefined)[];
    NZD: (string | undefined)[];
    PHP: (string | undefined)[];
    TWD: (string | undefined)[];
    USD: (string | undefined)[];
    VND: (string | undefined)[];
} | undefined)[];
export default _default;
