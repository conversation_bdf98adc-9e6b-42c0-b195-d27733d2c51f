/*!
 * (C) Ionic http://ionicframework.com - MIT License
 */
import { expect } from "@playwright/test";
import { configs, test } from "../../../../utils/test/playwright/index";
configs().forEach(({ title, screenshot, config }) => {
    test.describe(title('textarea: item'), () => {
        test('should render correctly in list with no fill', async ({ page }) => {
            await page.setContent(`
        <ion-list>
          <ion-item>
            <ion-textarea
              label="Email"
              value="<EMAIL>"
              helper-text="Enter your email"
              maxlength="20"
              counter="true"
            ></ion-textarea>
          </ion-item>
        </ion-list>
      `, config);
            const list = page.locator('ion-list');
            await expect(list).toHaveScreenshot(screenshot(`textarea-list-no-fill`));
        });
        test('should render correctly in inset list with no fill', async ({ page }) => {
            await page.setContent(`
        <ion-list inset="true">
          <ion-item>
            <ion-textarea
              label="Email"
              value="<EMAIL>"
              helper-text="Enter your email"
              maxlength="20"
              counter="true"
            ></ion-textarea>
          </ion-item>
        </ion-list>
      `, config);
            const list = page.locator('ion-list');
            await expect(list).toHaveScreenshot(screenshot(`textarea-inset-list-no-fill`));
        });
    });
});
