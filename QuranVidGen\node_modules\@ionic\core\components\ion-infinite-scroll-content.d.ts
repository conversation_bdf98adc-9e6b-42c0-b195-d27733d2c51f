import type { Components, JSX } from "../dist/types/components";

interface IonInfiniteScrollContent extends Components.IonInfiniteScrollContent, HTMLElement {}
export const IonInfiniteScrollContent: {
    prototype: IonInfiniteScrollContent;
    new (): IonInfiniteScrollContent;
};
/**
 * Used to define this component and all nested components recursively.
 */
export const defineCustomElement: () => void;
