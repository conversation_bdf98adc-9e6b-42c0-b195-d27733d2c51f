{"ast": null, "code": "import { animation, style, animate, trigger, transition, useAnimation } from '@angular/animations';\nimport * as i2 from '@angular/common';\nimport { isPlatformBrowser, DOCUMENT, CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { forwardRef, EventEmitter, PLATFORM_ID, Component, ChangeDetectionStrategy, ViewEncapsulation, Inject, Input, Output, ContentChildren, ViewChild, NgModule } from '@angular/core';\nimport { NG_VALUE_ACCESSOR } from '@angular/forms';\nimport * as i1 from 'primeng/api';\nimport { PrimeTemplate, SharedModule } from 'primeng/api';\nimport { DomHandler, ConnectedOverlayScrollHandler } from 'primeng/dom';\nimport { ObjectUtils, ZIndexUtils } from 'primeng/utils';\nconst _c0 = [\"overlay\"];\nconst _c1 = [\"content\"];\nfunction Overlay_div_0_div_2_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nconst _c2 = function (a0, a1, a2) {\n  return {\n    showTransitionParams: a0,\n    hideTransitionParams: a1,\n    transform: a2\n  };\n};\nconst _c3 = function (a1) {\n  return {\n    value: \"visible\",\n    params: a1\n  };\n};\nconst _c4 = function (a0) {\n  return {\n    mode: a0\n  };\n};\nconst _c5 = function (a0) {\n  return {\n    $implicit: a0\n  };\n};\nfunction Overlay_div_0_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 1, 3);\n    i0.ɵɵlistener(\"click\", function Overlay_div_0_div_2_Template_div_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r5 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r5.onOverlayContentClick($event));\n    })(\"@overlayContentAnimation.start\", function Overlay_div_0_div_2_Template_div_animation_overlayContentAnimation_start_0_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r7 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r7.onOverlayContentAnimationStart($event));\n    })(\"@overlayContentAnimation.done\", function Overlay_div_0_div_2_Template_div_animation_overlayContentAnimation_done_0_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r8 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r8.onOverlayContentAnimationDone($event));\n    });\n    i0.ɵɵprojection(2);\n    i0.ɵɵtemplate(3, Overlay_div_0_div_2_ng_container_3_Template, 1, 0, \"ng-container\", 4);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassMap(ctx_r2.contentStyleClass);\n    i0.ɵɵproperty(\"ngStyle\", ctx_r2.contentStyle)(\"ngClass\", \"p-overlay-content\")(\"@overlayContentAnimation\", i0.ɵɵpureFunction1(11, _c3, i0.ɵɵpureFunction3(7, _c2, ctx_r2.showTransitionOptions, ctx_r2.hideTransitionOptions, ctx_r2.transformOptions[ctx_r2.modal ? ctx_r2.overlayResponsiveDirection : \"default\"])));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r2.contentTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(15, _c5, i0.ɵɵpureFunction1(13, _c4, ctx_r2.overlayMode)));\n  }\n}\nconst _c6 = function (a1, a2, a3, a4, a5, a6, a7, a8, a9, a10, a11, a12, a13, a14) {\n  return {\n    \"p-overlay p-component\": true,\n    \"p-overlay-modal p-component-overlay p-component-overlay-enter\": a1,\n    \"p-overlay-center\": a2,\n    \"p-overlay-top\": a3,\n    \"p-overlay-top-start\": a4,\n    \"p-overlay-top-end\": a5,\n    \"p-overlay-bottom\": a6,\n    \"p-overlay-bottom-start\": a7,\n    \"p-overlay-bottom-end\": a8,\n    \"p-overlay-left\": a9,\n    \"p-overlay-left-start\": a10,\n    \"p-overlay-left-end\": a11,\n    \"p-overlay-right\": a12,\n    \"p-overlay-right-start\": a13,\n    \"p-overlay-right-end\": a14\n  };\n};\nfunction Overlay_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 1, 2);\n    i0.ɵɵlistener(\"click\", function Overlay_div_0_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r9 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r9.onOverlayClick());\n    });\n    i0.ɵɵtemplate(2, Overlay_div_0_div_2_Template, 4, 17, \"div\", 0);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(ctx_r0.styleClass);\n    i0.ɵɵproperty(\"ngStyle\", ctx_r0.style)(\"ngClass\", i0.ɵɵpureFunctionV(5, _c6, [ctx_r0.modal, ctx_r0.modal && ctx_r0.overlayResponsiveDirection === \"center\", ctx_r0.modal && ctx_r0.overlayResponsiveDirection === \"top\", ctx_r0.modal && ctx_r0.overlayResponsiveDirection === \"top-start\", ctx_r0.modal && ctx_r0.overlayResponsiveDirection === \"top-end\", ctx_r0.modal && ctx_r0.overlayResponsiveDirection === \"bottom\", ctx_r0.modal && ctx_r0.overlayResponsiveDirection === \"bottom-start\", ctx_r0.modal && ctx_r0.overlayResponsiveDirection === \"bottom-end\", ctx_r0.modal && ctx_r0.overlayResponsiveDirection === \"left\", ctx_r0.modal && ctx_r0.overlayResponsiveDirection === \"left-start\", ctx_r0.modal && ctx_r0.overlayResponsiveDirection === \"left-end\", ctx_r0.modal && ctx_r0.overlayResponsiveDirection === \"right\", ctx_r0.modal && ctx_r0.overlayResponsiveDirection === \"right-start\", ctx_r0.modal && ctx_r0.overlayResponsiveDirection === \"right-end\"]));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.visible);\n  }\n}\nconst _c7 = [\"*\"];\nconst OVERLAY_VALUE_ACCESSOR = {\n  provide: NG_VALUE_ACCESSOR,\n  useExisting: forwardRef(() => Overlay),\n  multi: true\n};\nconst showOverlayContentAnimation = animation([style({\n  transform: '{{transform}}',\n  opacity: 0\n}), animate('{{showTransitionParams}}')]);\nconst hideOverlayContentAnimation = animation([animate('{{hideTransitionParams}}', style({\n  transform: '{{transform}}',\n  opacity: 0\n}))]);\n/**\n * This API allows overlay components to be controlled from the PrimeNGConfig. In this way, all overlay components in the application can have the same behavior.\n * @group Components\n */\nclass Overlay {\n  document;\n  platformId;\n  el;\n  renderer;\n  config;\n  overlayService;\n  cd;\n  zone;\n  /**\n   * The visible property is an input that determines the visibility of the component.\n   * @defaultValue false\n   * @group Props\n   */\n  get visible() {\n    return this._visible;\n  }\n  set visible(value) {\n    this._visible = value;\n    if (this._visible && !this.modalVisible) {\n      this.modalVisible = true;\n    }\n  }\n  /**\n   * The mode property is an input that determines the overlay mode type or string.\n   * @defaultValue null\n   * @group Props\n   */\n  get mode() {\n    return this._mode || this.overlayOptions?.mode;\n  }\n  set mode(value) {\n    this._mode = value;\n  }\n  /**\n   * The style property is an input that determines the style object for the component.\n   * @defaultValue null\n   * @group Props\n   */\n  get style() {\n    return ObjectUtils.merge(this._style, this.modal ? this.overlayResponsiveOptions?.style : this.overlayOptions?.style);\n  }\n  set style(value) {\n    this._style = value;\n  }\n  /**\n   * The styleClass property is an input that determines the CSS class(es) for the component.\n   * @defaultValue null\n   * @group Props\n   */\n  get styleClass() {\n    return ObjectUtils.merge(this._styleClass, this.modal ? this.overlayResponsiveOptions?.styleClass : this.overlayOptions?.styleClass);\n  }\n  set styleClass(value) {\n    this._styleClass = value;\n  }\n  /**\n   * The contentStyle property is an input that determines the style object for the content of the component.\n   * @defaultValue null\n   * @group Props\n   */\n  get contentStyle() {\n    return ObjectUtils.merge(this._contentStyle, this.modal ? this.overlayResponsiveOptions?.contentStyle : this.overlayOptions?.contentStyle);\n  }\n  set contentStyle(value) {\n    this._contentStyle = value;\n  }\n  /**\n   * The contentStyleClass property is an input that determines the CSS class(es) for the content of the component.\n   * @defaultValue null\n   * @group Props\n   */\n  get contentStyleClass() {\n    return ObjectUtils.merge(this._contentStyleClass, this.modal ? this.overlayResponsiveOptions?.contentStyleClass : this.overlayOptions?.contentStyleClass);\n  }\n  set contentStyleClass(value) {\n    this._contentStyleClass = value;\n  }\n  /**\n   * The target property is an input that specifies the target element or selector for the component.\n   * @defaultValue null\n   * @group Props\n   */\n  get target() {\n    const value = this._target || this.overlayOptions?.target;\n    return value === undefined ? '@prev' : value;\n  }\n  set target(value) {\n    this._target = value;\n  }\n  /**\n   * Overlay can be mounted into its location, body or DOM element instance using this option.\n   * @defaultValue null\n   * @group Props\n   */\n  get appendTo() {\n    return this._appendTo || this.overlayOptions?.appendTo;\n  }\n  set appendTo(value) {\n    this._appendTo = value;\n  }\n  /**\n   * The autoZIndex determines whether to automatically manage layering. Its default value is 'false'.\n   * @defaultValue false\n   * @group Props\n   */\n  get autoZIndex() {\n    const value = this._autoZIndex || this.overlayOptions?.autoZIndex;\n    return value === undefined ? true : value;\n  }\n  set autoZIndex(value) {\n    this._autoZIndex = value;\n  }\n  /**\n   * The baseZIndex is base zIndex value to use in layering.\n   * @defaultValue null\n   * @group Props\n   */\n  get baseZIndex() {\n    const value = this._baseZIndex || this.overlayOptions?.baseZIndex;\n    return value === undefined ? 0 : value;\n  }\n  set baseZIndex(value) {\n    this._baseZIndex = value;\n  }\n  /**\n   * Transition options of the show or hide animation.\n   * @defaultValue .12s cubic-bezier(0, 0, 0.2, 1)\n   * @group Props\n   */\n  get showTransitionOptions() {\n    const value = this._showTransitionOptions || this.overlayOptions?.showTransitionOptions;\n    return value === undefined ? '.12s cubic-bezier(0, 0, 0.2, 1)' : value;\n  }\n  set showTransitionOptions(value) {\n    this._showTransitionOptions = value;\n  }\n  /**\n   * The hideTransitionOptions property is an input that determines the CSS transition options for hiding the component.\n   * @defaultValue .1s linear\n   * @group Props\n   */\n  get hideTransitionOptions() {\n    const value = this._hideTransitionOptions || this.overlayOptions?.hideTransitionOptions;\n    return value === undefined ? '.1s linear' : value;\n  }\n  set hideTransitionOptions(value) {\n    this._hideTransitionOptions = value;\n  }\n  /**\n   * The listener property is an input that specifies the listener object for the component.\n   * @defaultValue null\n   * @group Props\n   */\n  get listener() {\n    return this._listener || this.overlayOptions?.listener;\n  }\n  set listener(value) {\n    this._listener = value;\n  }\n  /**\n   * It is the option used to determine in which mode it should appear according to the given media or breakpoint.\n   * @defaultValue null\n   * @group Props\n   */\n  get responsive() {\n    return this._responsive || this.overlayOptions?.responsive;\n  }\n  set responsive(val) {\n    this._responsive = val;\n  }\n  /**\n   * The options property is an input that specifies the overlay options for the component.\n   * @defaultValue null\n   * @group Props\n   */\n  get options() {\n    return this._options;\n  }\n  set options(val) {\n    this._options = val;\n  }\n  /**\n   * This EventEmitter is used to notify changes in the visibility state of a component.\n   * @param {Boolean} boolean - Value of visibility as boolean.\n   * @group Emits\n   */\n  visibleChange = new EventEmitter();\n  /**\n   * Callback to invoke before the overlay is shown.\n   * @param {OverlayOnBeforeShowEvent} event - Custom overlay before show event.\n   * @group Emits\n   */\n  onBeforeShow = new EventEmitter();\n  /**\n   * Callback to invoke when the overlay is shown.\n   * @param {OverlayOnShowEvent} event - Custom overlay show event.\n   * @group Emits\n   */\n  onShow = new EventEmitter();\n  /**\n   * Callback to invoke before the overlay is hidden.\n   * @param {OverlayOnBeforeHideEvent} event - Custom overlay before hide event.\n   * @group Emits\n   */\n  onBeforeHide = new EventEmitter();\n  /**\n   * Callback to invoke when the overlay is hidden\n   * @param {OverlayOnHideEvent} event - Custom hide event.\n   * @group Emits\n   */\n  onHide = new EventEmitter();\n  /**\n   * Callback to invoke when the animation is started.\n   * @param {AnimationEvent} event - Animation event.\n   * @group Emits\n   */\n  onAnimationStart = new EventEmitter();\n  /**\n   * Callback to invoke when the animation is done.\n   * @param {AnimationEvent} event - Animation event.\n   * @group Emits\n   */\n  onAnimationDone = new EventEmitter();\n  templates;\n  overlayViewChild;\n  contentViewChild;\n  contentTemplate;\n  _visible = false;\n  _mode;\n  _style;\n  _styleClass;\n  _contentStyle;\n  _contentStyleClass;\n  _target;\n  _appendTo;\n  _autoZIndex;\n  _baseZIndex;\n  _showTransitionOptions;\n  _hideTransitionOptions;\n  _listener;\n  _responsive;\n  _options;\n  modalVisible = false;\n  isOverlayClicked = false;\n  isOverlayContentClicked = false;\n  scrollHandler;\n  documentClickListener;\n  documentResizeListener;\n  documentKeyboardListener;\n  window;\n  transformOptions = {\n    default: 'scaleY(0.8)',\n    center: 'scale(0.7)',\n    top: 'translate3d(0px, -100%, 0px)',\n    'top-start': 'translate3d(0px, -100%, 0px)',\n    'top-end': 'translate3d(0px, -100%, 0px)',\n    bottom: 'translate3d(0px, 100%, 0px)',\n    'bottom-start': 'translate3d(0px, 100%, 0px)',\n    'bottom-end': 'translate3d(0px, 100%, 0px)',\n    left: 'translate3d(-100%, 0px, 0px)',\n    'left-start': 'translate3d(-100%, 0px, 0px)',\n    'left-end': 'translate3d(-100%, 0px, 0px)',\n    right: 'translate3d(100%, 0px, 0px)',\n    'right-start': 'translate3d(100%, 0px, 0px)',\n    'right-end': 'translate3d(100%, 0px, 0px)'\n  };\n  get modal() {\n    if (isPlatformBrowser(this.platformId)) {\n      return this.mode === 'modal' || this.overlayResponsiveOptions && this.window?.matchMedia(this.overlayResponsiveOptions.media?.replace('@media', '') || `(max-width: ${this.overlayResponsiveOptions.breakpoint})`).matches;\n    }\n  }\n  get overlayMode() {\n    return this.mode || (this.modal ? 'modal' : 'overlay');\n  }\n  get overlayOptions() {\n    return {\n      ...this.config?.overlayOptions,\n      ...this.options\n    }; // TODO: Improve performance\n  }\n\n  get overlayResponsiveOptions() {\n    return {\n      ...this.overlayOptions?.responsive,\n      ...this.responsive\n    }; // TODO: Improve performance\n  }\n\n  get overlayResponsiveDirection() {\n    return this.overlayResponsiveOptions?.direction || 'center';\n  }\n  get overlayEl() {\n    return this.overlayViewChild?.nativeElement;\n  }\n  get contentEl() {\n    return this.contentViewChild?.nativeElement;\n  }\n  get targetEl() {\n    return DomHandler.getTargetElement(this.target, this.el?.nativeElement);\n  }\n  constructor(document, platformId, el, renderer, config, overlayService, cd, zone) {\n    this.document = document;\n    this.platformId = platformId;\n    this.el = el;\n    this.renderer = renderer;\n    this.config = config;\n    this.overlayService = overlayService;\n    this.cd = cd;\n    this.zone = zone;\n    this.window = this.document.defaultView;\n  }\n  ngAfterContentInit() {\n    this.templates?.forEach(item => {\n      switch (item.getType()) {\n        case 'content':\n          this.contentTemplate = item.template;\n          break;\n        // TODO: new template types may be added.\n        default:\n          this.contentTemplate = item.template;\n          break;\n      }\n    });\n  }\n  show(overlay, isFocus = false) {\n    this.onVisibleChange(true);\n    this.handleEvents('onShow', {\n      overlay: overlay || this.overlayEl,\n      target: this.targetEl,\n      mode: this.overlayMode\n    });\n    isFocus && DomHandler.focus(this.targetEl);\n    this.modal && DomHandler.addClass(this.document?.body, 'p-overflow-hidden');\n  }\n  hide(overlay, isFocus = false) {\n    if (!this.visible) {\n      return;\n    } else {\n      this.onVisibleChange(false);\n      this.handleEvents('onHide', {\n        overlay: overlay || this.overlayEl,\n        target: this.targetEl,\n        mode: this.overlayMode\n      });\n      isFocus && DomHandler.focus(this.targetEl);\n      this.modal && DomHandler.removeClass(this.document?.body, 'p-overflow-hidden');\n    }\n  }\n  alignOverlay() {\n    !this.modal && DomHandler.alignOverlay(this.overlayEl, this.targetEl, this.appendTo);\n  }\n  onVisibleChange(visible) {\n    this._visible = visible;\n    this.visibleChange.emit(visible);\n  }\n  onOverlayClick() {\n    this.isOverlayClicked = true;\n  }\n  onOverlayContentClick(event) {\n    this.overlayService.add({\n      originalEvent: event,\n      target: this.targetEl\n    });\n    this.isOverlayContentClicked = true;\n  }\n  onOverlayContentAnimationStart(event) {\n    switch (event.toState) {\n      case 'visible':\n        this.handleEvents('onBeforeShow', {\n          overlay: this.overlayEl,\n          target: this.targetEl,\n          mode: this.overlayMode\n        });\n        if (this.autoZIndex) {\n          ZIndexUtils.set(this.overlayMode, this.overlayEl, this.baseZIndex + this.config?.zIndex[this.overlayMode]);\n        }\n        DomHandler.appendOverlay(this.overlayEl, this.appendTo === 'body' ? this.document.body : this.appendTo, this.appendTo);\n        this.alignOverlay();\n        break;\n      case 'void':\n        this.handleEvents('onBeforeHide', {\n          overlay: this.overlayEl,\n          target: this.targetEl,\n          mode: this.overlayMode\n        });\n        this.modal && DomHandler.addClass(this.overlayEl, 'p-component-overlay-leave');\n        break;\n    }\n    this.handleEvents('onAnimationStart', event);\n  }\n  onOverlayContentAnimationDone(event) {\n    const container = this.overlayEl || event.element.parentElement;\n    switch (event.toState) {\n      case 'visible':\n        this.show(container, true);\n        this.bindListeners();\n        break;\n      case 'void':\n        this.hide(container, true);\n        this.unbindListeners();\n        DomHandler.appendOverlay(this.overlayEl, this.targetEl, this.appendTo);\n        ZIndexUtils.clear(container);\n        this.modalVisible = false;\n        this.cd.markForCheck();\n        break;\n    }\n    this.handleEvents('onAnimationDone', event);\n  }\n  handleEvents(name, params) {\n    this[name].emit(params);\n    this.options && this.options[name] && this.options[name](params);\n    this.config?.overlayOptions && (this.config?.overlayOptions)[name] && (this.config?.overlayOptions)[name](params);\n  }\n  bindListeners() {\n    this.bindScrollListener();\n    this.bindDocumentClickListener();\n    this.bindDocumentResizeListener();\n    this.bindDocumentKeyboardListener();\n  }\n  unbindListeners() {\n    this.unbindScrollListener();\n    this.unbindDocumentClickListener();\n    this.unbindDocumentResizeListener();\n    this.unbindDocumentKeyboardListener();\n  }\n  bindScrollListener() {\n    if (!this.scrollHandler) {\n      this.scrollHandler = new ConnectedOverlayScrollHandler(this.targetEl, event => {\n        const valid = this.listener ? this.listener(event, {\n          type: 'scroll',\n          mode: this.overlayMode,\n          valid: true\n        }) : true;\n        valid && this.hide(event, true);\n      });\n    }\n    this.scrollHandler.bindScrollListener();\n  }\n  unbindScrollListener() {\n    if (this.scrollHandler) {\n      this.scrollHandler.unbindScrollListener();\n    }\n  }\n  bindDocumentClickListener() {\n    if (!this.documentClickListener) {\n      this.documentClickListener = this.renderer.listen(this.document, 'click', event => {\n        const isTargetClicked = this.targetEl && (this.targetEl.isSameNode(event.target) || !this.isOverlayClicked && this.targetEl.contains(event.target));\n        const isOutsideClicked = !isTargetClicked && !this.isOverlayContentClicked;\n        const valid = this.listener ? this.listener(event, {\n          type: 'outside',\n          mode: this.overlayMode,\n          valid: event.which !== 3 && isOutsideClicked\n        }) : isOutsideClicked;\n        valid && this.hide(event);\n        this.isOverlayClicked = this.isOverlayContentClicked = false;\n      });\n    }\n  }\n  unbindDocumentClickListener() {\n    if (this.documentClickListener) {\n      this.documentClickListener();\n      this.documentClickListener = null;\n    }\n  }\n  bindDocumentResizeListener() {\n    if (!this.documentResizeListener) {\n      this.documentResizeListener = this.renderer.listen(this.window, 'resize', event => {\n        const valid = this.listener ? this.listener(event, {\n          type: 'resize',\n          mode: this.overlayMode,\n          valid: !DomHandler.isTouchDevice()\n        }) : !DomHandler.isTouchDevice();\n        valid && this.hide(event, true);\n      });\n    }\n  }\n  unbindDocumentResizeListener() {\n    if (this.documentResizeListener) {\n      this.documentResizeListener();\n      this.documentResizeListener = null;\n    }\n  }\n  bindDocumentKeyboardListener() {\n    if (this.documentKeyboardListener) {\n      return;\n    }\n    this.zone.runOutsideAngular(() => {\n      this.documentKeyboardListener = this.renderer.listen(this.window, 'keydown', event => {\n        if (this.overlayOptions.hideOnEscape === false || event.code !== 'Escape') {\n          return;\n        }\n        const valid = this.listener ? this.listener(event, {\n          type: 'keydown',\n          mode: this.overlayMode,\n          valid: !DomHandler.isTouchDevice()\n        }) : !DomHandler.isTouchDevice();\n        if (valid) {\n          this.zone.run(() => {\n            this.hide(event, true);\n          });\n        }\n      });\n    });\n  }\n  unbindDocumentKeyboardListener() {\n    if (this.documentKeyboardListener) {\n      this.documentKeyboardListener();\n      this.documentKeyboardListener = null;\n    }\n  }\n  ngOnDestroy() {\n    this.hide(this.overlayEl, true);\n    if (this.overlayEl) {\n      DomHandler.appendOverlay(this.overlayEl, this.targetEl, this.appendTo);\n      ZIndexUtils.clear(this.overlayEl);\n    }\n    if (this.scrollHandler) {\n      this.scrollHandler.destroy();\n      this.scrollHandler = null;\n    }\n    this.unbindListeners();\n  }\n  static ɵfac = function Overlay_Factory(t) {\n    return new (t || Overlay)(i0.ɵɵdirectiveInject(DOCUMENT), i0.ɵɵdirectiveInject(PLATFORM_ID), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i1.PrimeNGConfig), i0.ɵɵdirectiveInject(i1.OverlayService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.NgZone));\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: Overlay,\n    selectors: [[\"p-overlay\"]],\n    contentQueries: function Overlay_ContentQueries(rf, ctx, dirIndex) {\n      if (rf & 1) {\n        i0.ɵɵcontentQuery(dirIndex, PrimeTemplate, 4);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.templates = _t);\n      }\n    },\n    viewQuery: function Overlay_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 5);\n        i0.ɵɵviewQuery(_c1, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.overlayViewChild = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.contentViewChild = _t.first);\n      }\n    },\n    hostAttrs: [1, \"p-element\"],\n    inputs: {\n      visible: \"visible\",\n      mode: \"mode\",\n      style: \"style\",\n      styleClass: \"styleClass\",\n      contentStyle: \"contentStyle\",\n      contentStyleClass: \"contentStyleClass\",\n      target: \"target\",\n      appendTo: \"appendTo\",\n      autoZIndex: \"autoZIndex\",\n      baseZIndex: \"baseZIndex\",\n      showTransitionOptions: \"showTransitionOptions\",\n      hideTransitionOptions: \"hideTransitionOptions\",\n      listener: \"listener\",\n      responsive: \"responsive\",\n      options: \"options\"\n    },\n    outputs: {\n      visibleChange: \"visibleChange\",\n      onBeforeShow: \"onBeforeShow\",\n      onShow: \"onShow\",\n      onBeforeHide: \"onBeforeHide\",\n      onHide: \"onHide\",\n      onAnimationStart: \"onAnimationStart\",\n      onAnimationDone: \"onAnimationDone\"\n    },\n    features: [i0.ɵɵProvidersFeature([OVERLAY_VALUE_ACCESSOR])],\n    ngContentSelectors: _c7,\n    decls: 1,\n    vars: 1,\n    consts: [[3, \"ngStyle\", \"class\", \"ngClass\", \"click\", 4, \"ngIf\"], [3, \"ngStyle\", \"ngClass\", \"click\"], [\"overlay\", \"\"], [\"content\", \"\"], [4, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"]],\n    template: function Overlay_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵtemplate(0, Overlay_div_0_Template, 3, 20, \"div\", 0);\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"ngIf\", ctx.modalVisible);\n      }\n    },\n    dependencies: [i2.NgClass, i2.NgIf, i2.NgTemplateOutlet, i2.NgStyle],\n    styles: [\"@layer primeng{.p-overlay{position:absolute;top:0;left:0}.p-overlay-modal{display:flex;align-items:center;justify-content:center;position:fixed;top:0;left:0;width:100%;height:100%}.p-overlay-content{transform-origin:inherit}.p-overlay-modal>.p-overlay-content{z-index:1;width:90%}.p-overlay-top{align-items:flex-start}.p-overlay-top-start{align-items:flex-start;justify-content:flex-start}.p-overlay-top-end{align-items:flex-start;justify-content:flex-end}.p-overlay-bottom{align-items:flex-end}.p-overlay-bottom-start{align-items:flex-end;justify-content:flex-start}.p-overlay-bottom-end{align-items:flex-end;justify-content:flex-end}.p-overlay-left{justify-content:flex-start}.p-overlay-left-start{justify-content:flex-start;align-items:flex-start}.p-overlay-left-end{justify-content:flex-start;align-items:flex-end}.p-overlay-right{justify-content:flex-end}.p-overlay-right-start{justify-content:flex-end;align-items:flex-start}.p-overlay-right-end{justify-content:flex-end;align-items:flex-end}}\\n\"],\n    encapsulation: 2,\n    data: {\n      animation: [trigger('overlayContentAnimation', [transition(':enter', [useAnimation(showOverlayContentAnimation)]), transition(':leave', [useAnimation(hideOverlayContentAnimation)])])]\n    },\n    changeDetection: 0\n  });\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Overlay, [{\n    type: Component,\n    args: [{\n      selector: 'p-overlay',\n      template: `\n        <div\n            *ngIf=\"modalVisible\"\n            #overlay\n            [ngStyle]=\"style\"\n            [class]=\"styleClass\"\n            [ngClass]=\"{\n                'p-overlay p-component': true,\n                'p-overlay-modal p-component-overlay p-component-overlay-enter': modal,\n                'p-overlay-center': modal && overlayResponsiveDirection === 'center',\n                'p-overlay-top': modal && overlayResponsiveDirection === 'top',\n                'p-overlay-top-start': modal && overlayResponsiveDirection === 'top-start',\n                'p-overlay-top-end': modal && overlayResponsiveDirection === 'top-end',\n                'p-overlay-bottom': modal && overlayResponsiveDirection === 'bottom',\n                'p-overlay-bottom-start': modal && overlayResponsiveDirection === 'bottom-start',\n                'p-overlay-bottom-end': modal && overlayResponsiveDirection === 'bottom-end',\n                'p-overlay-left': modal && overlayResponsiveDirection === 'left',\n                'p-overlay-left-start': modal && overlayResponsiveDirection === 'left-start',\n                'p-overlay-left-end': modal && overlayResponsiveDirection === 'left-end',\n                'p-overlay-right': modal && overlayResponsiveDirection === 'right',\n                'p-overlay-right-start': modal && overlayResponsiveDirection === 'right-start',\n                'p-overlay-right-end': modal && overlayResponsiveDirection === 'right-end'\n            }\"\n            (click)=\"onOverlayClick()\"\n        >\n            <div\n                *ngIf=\"visible\"\n                #content\n                [ngStyle]=\"contentStyle\"\n                [class]=\"contentStyleClass\"\n                [ngClass]=\"'p-overlay-content'\"\n                (click)=\"onOverlayContentClick($event)\"\n                [@overlayContentAnimation]=\"{ value: 'visible', params: { showTransitionParams: showTransitionOptions, hideTransitionParams: hideTransitionOptions, transform: transformOptions[modal ? overlayResponsiveDirection : 'default'] } }\"\n                (@overlayContentAnimation.start)=\"onOverlayContentAnimationStart($event)\"\n                (@overlayContentAnimation.done)=\"onOverlayContentAnimationDone($event)\"\n            >\n                <ng-content></ng-content>\n                <ng-container *ngTemplateOutlet=\"contentTemplate; context: { $implicit: { mode: overlayMode } }\"></ng-container>\n            </div>\n        </div>\n    `,\n      animations: [trigger('overlayContentAnimation', [transition(':enter', [useAnimation(showOverlayContentAnimation)]), transition(':leave', [useAnimation(hideOverlayContentAnimation)])])],\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      providers: [OVERLAY_VALUE_ACCESSOR],\n      host: {\n        class: 'p-element'\n      },\n      styles: [\"@layer primeng{.p-overlay{position:absolute;top:0;left:0}.p-overlay-modal{display:flex;align-items:center;justify-content:center;position:fixed;top:0;left:0;width:100%;height:100%}.p-overlay-content{transform-origin:inherit}.p-overlay-modal>.p-overlay-content{z-index:1;width:90%}.p-overlay-top{align-items:flex-start}.p-overlay-top-start{align-items:flex-start;justify-content:flex-start}.p-overlay-top-end{align-items:flex-start;justify-content:flex-end}.p-overlay-bottom{align-items:flex-end}.p-overlay-bottom-start{align-items:flex-end;justify-content:flex-start}.p-overlay-bottom-end{align-items:flex-end;justify-content:flex-end}.p-overlay-left{justify-content:flex-start}.p-overlay-left-start{justify-content:flex-start;align-items:flex-start}.p-overlay-left-end{justify-content:flex-start;align-items:flex-end}.p-overlay-right{justify-content:flex-end}.p-overlay-right-start{justify-content:flex-end;align-items:flex-start}.p-overlay-right-end{justify-content:flex-end;align-items:flex-end}}\\n\"]\n    }]\n  }], function () {\n    return [{\n      type: Document,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [PLATFORM_ID]\n      }]\n    }, {\n      type: i0.ElementRef\n    }, {\n      type: i0.Renderer2\n    }, {\n      type: i1.PrimeNGConfig\n    }, {\n      type: i1.OverlayService\n    }, {\n      type: i0.ChangeDetectorRef\n    }, {\n      type: i0.NgZone\n    }];\n  }, {\n    visible: [{\n      type: Input\n    }],\n    mode: [{\n      type: Input\n    }],\n    style: [{\n      type: Input\n    }],\n    styleClass: [{\n      type: Input\n    }],\n    contentStyle: [{\n      type: Input\n    }],\n    contentStyleClass: [{\n      type: Input\n    }],\n    target: [{\n      type: Input\n    }],\n    appendTo: [{\n      type: Input\n    }],\n    autoZIndex: [{\n      type: Input\n    }],\n    baseZIndex: [{\n      type: Input\n    }],\n    showTransitionOptions: [{\n      type: Input\n    }],\n    hideTransitionOptions: [{\n      type: Input\n    }],\n    listener: [{\n      type: Input\n    }],\n    responsive: [{\n      type: Input\n    }],\n    options: [{\n      type: Input\n    }],\n    visibleChange: [{\n      type: Output\n    }],\n    onBeforeShow: [{\n      type: Output\n    }],\n    onShow: [{\n      type: Output\n    }],\n    onBeforeHide: [{\n      type: Output\n    }],\n    onHide: [{\n      type: Output\n    }],\n    onAnimationStart: [{\n      type: Output\n    }],\n    onAnimationDone: [{\n      type: Output\n    }],\n    templates: [{\n      type: ContentChildren,\n      args: [PrimeTemplate]\n    }],\n    overlayViewChild: [{\n      type: ViewChild,\n      args: ['overlay']\n    }],\n    contentViewChild: [{\n      type: ViewChild,\n      args: ['content']\n    }]\n  });\n})();\nclass OverlayModule {\n  static ɵfac = function OverlayModule_Factory(t) {\n    return new (t || OverlayModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: OverlayModule\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [CommonModule, SharedModule, SharedModule]\n  });\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(OverlayModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule, SharedModule],\n      exports: [Overlay, SharedModule],\n      declarations: [Overlay]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { OVERLAY_VALUE_ACCESSOR, Overlay, OverlayModule };", "map": {"version": 3, "names": ["animation", "style", "animate", "trigger", "transition", "useAnimation", "i2", "isPlatformBrowser", "DOCUMENT", "CommonModule", "i0", "forwardRef", "EventEmitter", "PLATFORM_ID", "Component", "ChangeDetectionStrategy", "ViewEncapsulation", "Inject", "Input", "Output", "ContentChildren", "ViewChild", "NgModule", "NG_VALUE_ACCESSOR", "i1", "PrimeTemplate", "SharedModule", "<PERSON><PERSON><PERSON><PERSON>", "ConnectedOverlayScrollHandler", "ObjectUtils", "ZIndexUtils", "_c0", "_c1", "Overlay_div_0_div_2_ng_container_3_Template", "rf", "ctx", "ɵɵelementContainer", "_c2", "a0", "a1", "a2", "showTransitionParams", "hideTransitionParams", "transform", "_c3", "value", "params", "_c4", "mode", "_c5", "$implicit", "Overlay_div_0_div_2_Template", "_r6", "ɵɵgetCurrentView", "ɵɵelementStart", "ɵɵlistener", "Overlay_div_0_div_2_Template_div_click_0_listener", "$event", "ɵɵrestoreView", "ctx_r5", "ɵɵnextContext", "ɵɵresetView", "onOverlayContentClick", "Overlay_div_0_div_2_Template_div_animation_overlayContentAnimation_start_0_listener", "ctx_r7", "onOverlayContentAnimationStart", "Overlay_div_0_div_2_Template_div_animation_overlayContentAnimation_done_0_listener", "ctx_r8", "onOverlayContentAnimationDone", "ɵɵprojection", "ɵɵtemplate", "ɵɵelementEnd", "ctx_r2", "ɵɵclassMap", "contentStyleClass", "ɵɵproperty", "contentStyle", "ɵɵpureFunction1", "ɵɵpureFunction3", "showTransitionOptions", "hideTransitionOptions", "transformOptions", "modal", "overlayResponsiveDirection", "ɵɵadvance", "contentTemplate", "overlayMode", "_c6", "a3", "a4", "a5", "a6", "a7", "a8", "a9", "a10", "a11", "a12", "a13", "a14", "Overlay_div_0_Template", "_r10", "Overlay_div_0_Template_div_click_0_listener", "ctx_r9", "onOverlayClick", "ctx_r0", "styleClass", "ɵɵpureFunctionV", "visible", "_c7", "OVERLAY_VALUE_ACCESSOR", "provide", "useExisting", "Overlay", "multi", "showOverlayContentAnimation", "opacity", "hideOverlayContentAnimation", "document", "platformId", "el", "renderer", "config", "overlayService", "cd", "zone", "_visible", "modalVisible", "_mode", "overlayOptions", "merge", "_style", "overlayResponsiveOptions", "_styleClass", "_contentStyle", "_contentStyleClass", "target", "_target", "undefined", "appendTo", "_appendTo", "autoZIndex", "_autoZIndex", "baseZIndex", "_baseZIndex", "_showTransitionOptions", "_hideTransitionOptions", "listener", "_listener", "responsive", "_responsive", "val", "options", "_options", "visibleChange", "onBeforeShow", "onShow", "onBeforeHide", "onHide", "onAnimationStart", "onAnimationDone", "templates", "overlayViewChild", "contentViewChild", "isOverlayClicked", "isOverlayContentClicked", "<PERSON><PERSON><PERSON><PERSON>", "documentClickListener", "documentResizeListener", "documentKeyboardListener", "window", "default", "center", "top", "bottom", "left", "right", "matchMedia", "media", "replace", "breakpoint", "matches", "direction", "overlayEl", "nativeElement", "contentEl", "targetEl", "getTargetElement", "constructor", "defaultView", "ngAfterContentInit", "for<PERSON>ach", "item", "getType", "template", "show", "overlay", "isFocus", "onVisibleChange", "handleEvents", "focus", "addClass", "body", "hide", "removeClass", "alignOverlay", "emit", "event", "add", "originalEvent", "toState", "set", "zIndex", "appendOverlay", "container", "element", "parentElement", "bindListeners", "unbindListeners", "clear", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "name", "bindScrollListener", "bindDocumentClickListener", "bindDocumentResizeListener", "bindDocumentKeyboardListener", "unbindScrollListener", "unbindDocumentClickListener", "unbindDocumentResizeListener", "unbindDocumentKeyboardListener", "valid", "type", "listen", "isTargetClicked", "isSameNode", "contains", "isOutsideClicked", "which", "isTouchDevice", "runOutsideAngular", "hideOnEscape", "code", "run", "ngOnDestroy", "destroy", "ɵfac", "Overlay_Factory", "t", "ɵɵdirectiveInject", "ElementRef", "Renderer2", "PrimeNGConfig", "OverlayService", "ChangeDetectorRef", "NgZone", "ɵcmp", "ɵɵdefineComponent", "selectors", "contentQueries", "Overlay_ContentQueries", "dirIndex", "ɵɵcontentQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "viewQuery", "Overlay_Query", "ɵɵviewQuery", "first", "hostAttrs", "inputs", "outputs", "features", "ɵɵProvidersFeature", "ngContentSelectors", "decls", "vars", "consts", "Overlay_Template", "ɵɵprojectionDef", "dependencies", "Ng<PERSON><PERSON>", "NgIf", "NgTemplateOutlet", "NgStyle", "styles", "encapsulation", "data", "changeDetection", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "animations", "OnPush", "None", "providers", "host", "class", "Document", "decorators", "OverlayModule", "OverlayModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "imports", "exports", "declarations"], "sources": ["C:/Users/<USER>/Desktop/تطبيق ناشر ايات قئانية/QuranVidGen/node_modules/primeng/fesm2022/primeng-overlay.mjs"], "sourcesContent": ["import { animation, style, animate, trigger, transition, useAnimation } from '@angular/animations';\nimport * as i2 from '@angular/common';\nimport { isPlatformBrowser, DOCUMENT, CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { forwardRef, EventEmitter, PLATFORM_ID, Component, ChangeDetectionStrategy, ViewEncapsulation, Inject, Input, Output, ContentChildren, ViewChild, NgModule } from '@angular/core';\nimport { NG_VALUE_ACCESSOR } from '@angular/forms';\nimport * as i1 from 'primeng/api';\nimport { PrimeTemplate, SharedModule } from 'primeng/api';\nimport { DomHandler, ConnectedOverlayScrollHandler } from 'primeng/dom';\nimport { ObjectUtils, ZIndexUtils } from 'primeng/utils';\n\nconst OVERLAY_VALUE_ACCESSOR = {\n    provide: NG_VALUE_ACCESSOR,\n    useExisting: forwardRef(() => Overlay),\n    multi: true\n};\nconst showOverlayContentAnimation = animation([style({ transform: '{{transform}}', opacity: 0 }), animate('{{showTransitionParams}}')]);\nconst hideOverlayContentAnimation = animation([animate('{{hideTransitionParams}}', style({ transform: '{{transform}}', opacity: 0 }))]);\n/**\n * This API allows overlay components to be controlled from the PrimeNGConfig. In this way, all overlay components in the application can have the same behavior.\n * @group Components\n */\nclass Overlay {\n    document;\n    platformId;\n    el;\n    renderer;\n    config;\n    overlayService;\n    cd;\n    zone;\n    /**\n     * The visible property is an input that determines the visibility of the component.\n     * @defaultValue false\n     * @group Props\n     */\n    get visible() {\n        return this._visible;\n    }\n    set visible(value) {\n        this._visible = value;\n        if (this._visible && !this.modalVisible) {\n            this.modalVisible = true;\n        }\n    }\n    /**\n     * The mode property is an input that determines the overlay mode type or string.\n     * @defaultValue null\n     * @group Props\n     */\n    get mode() {\n        return this._mode || this.overlayOptions?.mode;\n    }\n    set mode(value) {\n        this._mode = value;\n    }\n    /**\n     * The style property is an input that determines the style object for the component.\n     * @defaultValue null\n     * @group Props\n     */\n    get style() {\n        return ObjectUtils.merge(this._style, this.modal ? this.overlayResponsiveOptions?.style : this.overlayOptions?.style);\n    }\n    set style(value) {\n        this._style = value;\n    }\n    /**\n     * The styleClass property is an input that determines the CSS class(es) for the component.\n     * @defaultValue null\n     * @group Props\n     */\n    get styleClass() {\n        return ObjectUtils.merge(this._styleClass, this.modal ? this.overlayResponsiveOptions?.styleClass : this.overlayOptions?.styleClass);\n    }\n    set styleClass(value) {\n        this._styleClass = value;\n    }\n    /**\n     * The contentStyle property is an input that determines the style object for the content of the component.\n     * @defaultValue null\n     * @group Props\n     */\n    get contentStyle() {\n        return ObjectUtils.merge(this._contentStyle, this.modal ? this.overlayResponsiveOptions?.contentStyle : this.overlayOptions?.contentStyle);\n    }\n    set contentStyle(value) {\n        this._contentStyle = value;\n    }\n    /**\n     * The contentStyleClass property is an input that determines the CSS class(es) for the content of the component.\n     * @defaultValue null\n     * @group Props\n     */\n    get contentStyleClass() {\n        return ObjectUtils.merge(this._contentStyleClass, this.modal ? this.overlayResponsiveOptions?.contentStyleClass : this.overlayOptions?.contentStyleClass);\n    }\n    set contentStyleClass(value) {\n        this._contentStyleClass = value;\n    }\n    /**\n     * The target property is an input that specifies the target element or selector for the component.\n     * @defaultValue null\n     * @group Props\n     */\n    get target() {\n        const value = this._target || this.overlayOptions?.target;\n        return value === undefined ? '@prev' : value;\n    }\n    set target(value) {\n        this._target = value;\n    }\n    /**\n     * Overlay can be mounted into its location, body or DOM element instance using this option.\n     * @defaultValue null\n     * @group Props\n     */\n    get appendTo() {\n        return this._appendTo || this.overlayOptions?.appendTo;\n    }\n    set appendTo(value) {\n        this._appendTo = value;\n    }\n    /**\n     * The autoZIndex determines whether to automatically manage layering. Its default value is 'false'.\n     * @defaultValue false\n     * @group Props\n     */\n    get autoZIndex() {\n        const value = this._autoZIndex || this.overlayOptions?.autoZIndex;\n        return value === undefined ? true : value;\n    }\n    set autoZIndex(value) {\n        this._autoZIndex = value;\n    }\n    /**\n     * The baseZIndex is base zIndex value to use in layering.\n     * @defaultValue null\n     * @group Props\n     */\n    get baseZIndex() {\n        const value = this._baseZIndex || this.overlayOptions?.baseZIndex;\n        return value === undefined ? 0 : value;\n    }\n    set baseZIndex(value) {\n        this._baseZIndex = value;\n    }\n    /**\n     * Transition options of the show or hide animation.\n     * @defaultValue .12s cubic-bezier(0, 0, 0.2, 1)\n     * @group Props\n     */\n    get showTransitionOptions() {\n        const value = this._showTransitionOptions || this.overlayOptions?.showTransitionOptions;\n        return value === undefined ? '.12s cubic-bezier(0, 0, 0.2, 1)' : value;\n    }\n    set showTransitionOptions(value) {\n        this._showTransitionOptions = value;\n    }\n    /**\n     * The hideTransitionOptions property is an input that determines the CSS transition options for hiding the component.\n     * @defaultValue .1s linear\n     * @group Props\n     */\n    get hideTransitionOptions() {\n        const value = this._hideTransitionOptions || this.overlayOptions?.hideTransitionOptions;\n        return value === undefined ? '.1s linear' : value;\n    }\n    set hideTransitionOptions(value) {\n        this._hideTransitionOptions = value;\n    }\n    /**\n     * The listener property is an input that specifies the listener object for the component.\n     * @defaultValue null\n     * @group Props\n     */\n    get listener() {\n        return this._listener || this.overlayOptions?.listener;\n    }\n    set listener(value) {\n        this._listener = value;\n    }\n    /**\n     * It is the option used to determine in which mode it should appear according to the given media or breakpoint.\n     * @defaultValue null\n     * @group Props\n     */\n    get responsive() {\n        return this._responsive || this.overlayOptions?.responsive;\n    }\n    set responsive(val) {\n        this._responsive = val;\n    }\n    /**\n     * The options property is an input that specifies the overlay options for the component.\n     * @defaultValue null\n     * @group Props\n     */\n    get options() {\n        return this._options;\n    }\n    set options(val) {\n        this._options = val;\n    }\n    /**\n     * This EventEmitter is used to notify changes in the visibility state of a component.\n     * @param {Boolean} boolean - Value of visibility as boolean.\n     * @group Emits\n     */\n    visibleChange = new EventEmitter();\n    /**\n     * Callback to invoke before the overlay is shown.\n     * @param {OverlayOnBeforeShowEvent} event - Custom overlay before show event.\n     * @group Emits\n     */\n    onBeforeShow = new EventEmitter();\n    /**\n     * Callback to invoke when the overlay is shown.\n     * @param {OverlayOnShowEvent} event - Custom overlay show event.\n     * @group Emits\n     */\n    onShow = new EventEmitter();\n    /**\n     * Callback to invoke before the overlay is hidden.\n     * @param {OverlayOnBeforeHideEvent} event - Custom overlay before hide event.\n     * @group Emits\n     */\n    onBeforeHide = new EventEmitter();\n    /**\n     * Callback to invoke when the overlay is hidden\n     * @param {OverlayOnHideEvent} event - Custom hide event.\n     * @group Emits\n     */\n    onHide = new EventEmitter();\n    /**\n     * Callback to invoke when the animation is started.\n     * @param {AnimationEvent} event - Animation event.\n     * @group Emits\n     */\n    onAnimationStart = new EventEmitter();\n    /**\n     * Callback to invoke when the animation is done.\n     * @param {AnimationEvent} event - Animation event.\n     * @group Emits\n     */\n    onAnimationDone = new EventEmitter();\n    templates;\n    overlayViewChild;\n    contentViewChild;\n    contentTemplate;\n    _visible = false;\n    _mode;\n    _style;\n    _styleClass;\n    _contentStyle;\n    _contentStyleClass;\n    _target;\n    _appendTo;\n    _autoZIndex;\n    _baseZIndex;\n    _showTransitionOptions;\n    _hideTransitionOptions;\n    _listener;\n    _responsive;\n    _options;\n    modalVisible = false;\n    isOverlayClicked = false;\n    isOverlayContentClicked = false;\n    scrollHandler;\n    documentClickListener;\n    documentResizeListener;\n    documentKeyboardListener;\n    window;\n    transformOptions = {\n        default: 'scaleY(0.8)',\n        center: 'scale(0.7)',\n        top: 'translate3d(0px, -100%, 0px)',\n        'top-start': 'translate3d(0px, -100%, 0px)',\n        'top-end': 'translate3d(0px, -100%, 0px)',\n        bottom: 'translate3d(0px, 100%, 0px)',\n        'bottom-start': 'translate3d(0px, 100%, 0px)',\n        'bottom-end': 'translate3d(0px, 100%, 0px)',\n        left: 'translate3d(-100%, 0px, 0px)',\n        'left-start': 'translate3d(-100%, 0px, 0px)',\n        'left-end': 'translate3d(-100%, 0px, 0px)',\n        right: 'translate3d(100%, 0px, 0px)',\n        'right-start': 'translate3d(100%, 0px, 0px)',\n        'right-end': 'translate3d(100%, 0px, 0px)'\n    };\n    get modal() {\n        if (isPlatformBrowser(this.platformId)) {\n            return this.mode === 'modal' || (this.overlayResponsiveOptions && this.window?.matchMedia(this.overlayResponsiveOptions.media?.replace('@media', '') || `(max-width: ${this.overlayResponsiveOptions.breakpoint})`).matches);\n        }\n    }\n    get overlayMode() {\n        return this.mode || (this.modal ? 'modal' : 'overlay');\n    }\n    get overlayOptions() {\n        return { ...this.config?.overlayOptions, ...this.options }; // TODO: Improve performance\n    }\n    get overlayResponsiveOptions() {\n        return { ...this.overlayOptions?.responsive, ...this.responsive }; // TODO: Improve performance\n    }\n    get overlayResponsiveDirection() {\n        return this.overlayResponsiveOptions?.direction || 'center';\n    }\n    get overlayEl() {\n        return this.overlayViewChild?.nativeElement;\n    }\n    get contentEl() {\n        return this.contentViewChild?.nativeElement;\n    }\n    get targetEl() {\n        return DomHandler.getTargetElement(this.target, this.el?.nativeElement);\n    }\n    constructor(document, platformId, el, renderer, config, overlayService, cd, zone) {\n        this.document = document;\n        this.platformId = platformId;\n        this.el = el;\n        this.renderer = renderer;\n        this.config = config;\n        this.overlayService = overlayService;\n        this.cd = cd;\n        this.zone = zone;\n        this.window = this.document.defaultView;\n    }\n    ngAfterContentInit() {\n        this.templates?.forEach((item) => {\n            switch (item.getType()) {\n                case 'content':\n                    this.contentTemplate = item.template;\n                    break;\n                // TODO: new template types may be added.\n                default:\n                    this.contentTemplate = item.template;\n                    break;\n            }\n        });\n    }\n    show(overlay, isFocus = false) {\n        this.onVisibleChange(true);\n        this.handleEvents('onShow', { overlay: overlay || this.overlayEl, target: this.targetEl, mode: this.overlayMode });\n        isFocus && DomHandler.focus(this.targetEl);\n        this.modal && DomHandler.addClass(this.document?.body, 'p-overflow-hidden');\n    }\n    hide(overlay, isFocus = false) {\n        if (!this.visible) {\n            return;\n        }\n        else {\n            this.onVisibleChange(false);\n            this.handleEvents('onHide', { overlay: overlay || this.overlayEl, target: this.targetEl, mode: this.overlayMode });\n            isFocus && DomHandler.focus(this.targetEl);\n            this.modal && DomHandler.removeClass(this.document?.body, 'p-overflow-hidden');\n        }\n    }\n    alignOverlay() {\n        !this.modal && DomHandler.alignOverlay(this.overlayEl, this.targetEl, this.appendTo);\n    }\n    onVisibleChange(visible) {\n        this._visible = visible;\n        this.visibleChange.emit(visible);\n    }\n    onOverlayClick() {\n        this.isOverlayClicked = true;\n    }\n    onOverlayContentClick(event) {\n        this.overlayService.add({\n            originalEvent: event,\n            target: this.targetEl\n        });\n        this.isOverlayContentClicked = true;\n    }\n    onOverlayContentAnimationStart(event) {\n        switch (event.toState) {\n            case 'visible':\n                this.handleEvents('onBeforeShow', { overlay: this.overlayEl, target: this.targetEl, mode: this.overlayMode });\n                if (this.autoZIndex) {\n                    ZIndexUtils.set(this.overlayMode, this.overlayEl, this.baseZIndex + this.config?.zIndex[this.overlayMode]);\n                }\n                DomHandler.appendOverlay(this.overlayEl, this.appendTo === 'body' ? this.document.body : this.appendTo, this.appendTo);\n                this.alignOverlay();\n                break;\n            case 'void':\n                this.handleEvents('onBeforeHide', { overlay: this.overlayEl, target: this.targetEl, mode: this.overlayMode });\n                this.modal && DomHandler.addClass(this.overlayEl, 'p-component-overlay-leave');\n                break;\n        }\n        this.handleEvents('onAnimationStart', event);\n    }\n    onOverlayContentAnimationDone(event) {\n        const container = this.overlayEl || event.element.parentElement;\n        switch (event.toState) {\n            case 'visible':\n                this.show(container, true);\n                this.bindListeners();\n                break;\n            case 'void':\n                this.hide(container, true);\n                this.unbindListeners();\n                DomHandler.appendOverlay(this.overlayEl, this.targetEl, this.appendTo);\n                ZIndexUtils.clear(container);\n                this.modalVisible = false;\n                this.cd.markForCheck();\n                break;\n        }\n        this.handleEvents('onAnimationDone', event);\n    }\n    handleEvents(name, params) {\n        this[name].emit(params);\n        this.options && this.options[name] && this.options[name](params);\n        this.config?.overlayOptions && (this.config?.overlayOptions)[name] && (this.config?.overlayOptions)[name](params);\n    }\n    bindListeners() {\n        this.bindScrollListener();\n        this.bindDocumentClickListener();\n        this.bindDocumentResizeListener();\n        this.bindDocumentKeyboardListener();\n    }\n    unbindListeners() {\n        this.unbindScrollListener();\n        this.unbindDocumentClickListener();\n        this.unbindDocumentResizeListener();\n        this.unbindDocumentKeyboardListener();\n    }\n    bindScrollListener() {\n        if (!this.scrollHandler) {\n            this.scrollHandler = new ConnectedOverlayScrollHandler(this.targetEl, (event) => {\n                const valid = this.listener ? this.listener(event, { type: 'scroll', mode: this.overlayMode, valid: true }) : true;\n                valid && this.hide(event, true);\n            });\n        }\n        this.scrollHandler.bindScrollListener();\n    }\n    unbindScrollListener() {\n        if (this.scrollHandler) {\n            this.scrollHandler.unbindScrollListener();\n        }\n    }\n    bindDocumentClickListener() {\n        if (!this.documentClickListener) {\n            this.documentClickListener = this.renderer.listen(this.document, 'click', (event) => {\n                const isTargetClicked = this.targetEl && (this.targetEl.isSameNode(event.target) || (!this.isOverlayClicked && this.targetEl.contains(event.target)));\n                const isOutsideClicked = !isTargetClicked && !this.isOverlayContentClicked;\n                const valid = this.listener ? this.listener(event, { type: 'outside', mode: this.overlayMode, valid: event.which !== 3 && isOutsideClicked }) : isOutsideClicked;\n                valid && this.hide(event);\n                this.isOverlayClicked = this.isOverlayContentClicked = false;\n            });\n        }\n    }\n    unbindDocumentClickListener() {\n        if (this.documentClickListener) {\n            this.documentClickListener();\n            this.documentClickListener = null;\n        }\n    }\n    bindDocumentResizeListener() {\n        if (!this.documentResizeListener) {\n            this.documentResizeListener = this.renderer.listen(this.window, 'resize', (event) => {\n                const valid = this.listener ? this.listener(event, { type: 'resize', mode: this.overlayMode, valid: !DomHandler.isTouchDevice() }) : !DomHandler.isTouchDevice();\n                valid && this.hide(event, true);\n            });\n        }\n    }\n    unbindDocumentResizeListener() {\n        if (this.documentResizeListener) {\n            this.documentResizeListener();\n            this.documentResizeListener = null;\n        }\n    }\n    bindDocumentKeyboardListener() {\n        if (this.documentKeyboardListener) {\n            return;\n        }\n        this.zone.runOutsideAngular(() => {\n            this.documentKeyboardListener = this.renderer.listen(this.window, 'keydown', (event) => {\n                if (this.overlayOptions.hideOnEscape === false || event.code !== 'Escape') {\n                    return;\n                }\n                const valid = this.listener ? this.listener(event, { type: 'keydown', mode: this.overlayMode, valid: !DomHandler.isTouchDevice() }) : !DomHandler.isTouchDevice();\n                if (valid) {\n                    this.zone.run(() => {\n                        this.hide(event, true);\n                    });\n                }\n            });\n        });\n    }\n    unbindDocumentKeyboardListener() {\n        if (this.documentKeyboardListener) {\n            this.documentKeyboardListener();\n            this.documentKeyboardListener = null;\n        }\n    }\n    ngOnDestroy() {\n        this.hide(this.overlayEl, true);\n        if (this.overlayEl) {\n            DomHandler.appendOverlay(this.overlayEl, this.targetEl, this.appendTo);\n            ZIndexUtils.clear(this.overlayEl);\n        }\n        if (this.scrollHandler) {\n            this.scrollHandler.destroy();\n            this.scrollHandler = null;\n        }\n        this.unbindListeners();\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.0\", ngImport: i0, type: Overlay, deps: [{ token: DOCUMENT }, { token: PLATFORM_ID }, { token: i0.ElementRef }, { token: i0.Renderer2 }, { token: i1.PrimeNGConfig }, { token: i1.OverlayService }, { token: i0.ChangeDetectorRef }, { token: i0.NgZone }], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.2.0\", type: Overlay, selector: \"p-overlay\", inputs: { visible: \"visible\", mode: \"mode\", style: \"style\", styleClass: \"styleClass\", contentStyle: \"contentStyle\", contentStyleClass: \"contentStyleClass\", target: \"target\", appendTo: \"appendTo\", autoZIndex: \"autoZIndex\", baseZIndex: \"baseZIndex\", showTransitionOptions: \"showTransitionOptions\", hideTransitionOptions: \"hideTransitionOptions\", listener: \"listener\", responsive: \"responsive\", options: \"options\" }, outputs: { visibleChange: \"visibleChange\", onBeforeShow: \"onBeforeShow\", onShow: \"onShow\", onBeforeHide: \"onBeforeHide\", onHide: \"onHide\", onAnimationStart: \"onAnimationStart\", onAnimationDone: \"onAnimationDone\" }, host: { classAttribute: \"p-element\" }, providers: [OVERLAY_VALUE_ACCESSOR], queries: [{ propertyName: \"templates\", predicate: PrimeTemplate }], viewQueries: [{ propertyName: \"overlayViewChild\", first: true, predicate: [\"overlay\"], descendants: true }, { propertyName: \"contentViewChild\", first: true, predicate: [\"content\"], descendants: true }], ngImport: i0, template: `\n        <div\n            *ngIf=\"modalVisible\"\n            #overlay\n            [ngStyle]=\"style\"\n            [class]=\"styleClass\"\n            [ngClass]=\"{\n                'p-overlay p-component': true,\n                'p-overlay-modal p-component-overlay p-component-overlay-enter': modal,\n                'p-overlay-center': modal && overlayResponsiveDirection === 'center',\n                'p-overlay-top': modal && overlayResponsiveDirection === 'top',\n                'p-overlay-top-start': modal && overlayResponsiveDirection === 'top-start',\n                'p-overlay-top-end': modal && overlayResponsiveDirection === 'top-end',\n                'p-overlay-bottom': modal && overlayResponsiveDirection === 'bottom',\n                'p-overlay-bottom-start': modal && overlayResponsiveDirection === 'bottom-start',\n                'p-overlay-bottom-end': modal && overlayResponsiveDirection === 'bottom-end',\n                'p-overlay-left': modal && overlayResponsiveDirection === 'left',\n                'p-overlay-left-start': modal && overlayResponsiveDirection === 'left-start',\n                'p-overlay-left-end': modal && overlayResponsiveDirection === 'left-end',\n                'p-overlay-right': modal && overlayResponsiveDirection === 'right',\n                'p-overlay-right-start': modal && overlayResponsiveDirection === 'right-start',\n                'p-overlay-right-end': modal && overlayResponsiveDirection === 'right-end'\n            }\"\n            (click)=\"onOverlayClick()\"\n        >\n            <div\n                *ngIf=\"visible\"\n                #content\n                [ngStyle]=\"contentStyle\"\n                [class]=\"contentStyleClass\"\n                [ngClass]=\"'p-overlay-content'\"\n                (click)=\"onOverlayContentClick($event)\"\n                [@overlayContentAnimation]=\"{ value: 'visible', params: { showTransitionParams: showTransitionOptions, hideTransitionParams: hideTransitionOptions, transform: transformOptions[modal ? overlayResponsiveDirection : 'default'] } }\"\n                (@overlayContentAnimation.start)=\"onOverlayContentAnimationStart($event)\"\n                (@overlayContentAnimation.done)=\"onOverlayContentAnimationDone($event)\"\n            >\n                <ng-content></ng-content>\n                <ng-container *ngTemplateOutlet=\"contentTemplate; context: { $implicit: { mode: overlayMode } }\"></ng-container>\n            </div>\n        </div>\n    `, isInline: true, styles: [\"@layer primeng{.p-overlay{position:absolute;top:0;left:0}.p-overlay-modal{display:flex;align-items:center;justify-content:center;position:fixed;top:0;left:0;width:100%;height:100%}.p-overlay-content{transform-origin:inherit}.p-overlay-modal>.p-overlay-content{z-index:1;width:90%}.p-overlay-top{align-items:flex-start}.p-overlay-top-start{align-items:flex-start;justify-content:flex-start}.p-overlay-top-end{align-items:flex-start;justify-content:flex-end}.p-overlay-bottom{align-items:flex-end}.p-overlay-bottom-start{align-items:flex-end;justify-content:flex-start}.p-overlay-bottom-end{align-items:flex-end;justify-content:flex-end}.p-overlay-left{justify-content:flex-start}.p-overlay-left-start{justify-content:flex-start;align-items:flex-start}.p-overlay-left-end{justify-content:flex-start;align-items:flex-end}.p-overlay-right{justify-content:flex-end}.p-overlay-right-start{justify-content:flex-end;align-items:flex-start}.p-overlay-right-end{justify-content:flex-end;align-items:flex-end}}\\n\"], dependencies: [{ kind: \"directive\", type: i2.NgClass, selector: \"[ngClass]\", inputs: [\"class\", \"ngClass\"] }, { kind: \"directive\", type: i2.NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"directive\", type: i2.NgTemplateOutlet, selector: \"[ngTemplateOutlet]\", inputs: [\"ngTemplateOutletContext\", \"ngTemplateOutlet\", \"ngTemplateOutletInjector\"] }, { kind: \"directive\", type: i2.NgStyle, selector: \"[ngStyle]\", inputs: [\"ngStyle\"] }], animations: [trigger('overlayContentAnimation', [transition(':enter', [useAnimation(showOverlayContentAnimation)]), transition(':leave', [useAnimation(hideOverlayContentAnimation)])])], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.0\", ngImport: i0, type: Overlay, decorators: [{\n            type: Component,\n            args: [{ selector: 'p-overlay', template: `\n        <div\n            *ngIf=\"modalVisible\"\n            #overlay\n            [ngStyle]=\"style\"\n            [class]=\"styleClass\"\n            [ngClass]=\"{\n                'p-overlay p-component': true,\n                'p-overlay-modal p-component-overlay p-component-overlay-enter': modal,\n                'p-overlay-center': modal && overlayResponsiveDirection === 'center',\n                'p-overlay-top': modal && overlayResponsiveDirection === 'top',\n                'p-overlay-top-start': modal && overlayResponsiveDirection === 'top-start',\n                'p-overlay-top-end': modal && overlayResponsiveDirection === 'top-end',\n                'p-overlay-bottom': modal && overlayResponsiveDirection === 'bottom',\n                'p-overlay-bottom-start': modal && overlayResponsiveDirection === 'bottom-start',\n                'p-overlay-bottom-end': modal && overlayResponsiveDirection === 'bottom-end',\n                'p-overlay-left': modal && overlayResponsiveDirection === 'left',\n                'p-overlay-left-start': modal && overlayResponsiveDirection === 'left-start',\n                'p-overlay-left-end': modal && overlayResponsiveDirection === 'left-end',\n                'p-overlay-right': modal && overlayResponsiveDirection === 'right',\n                'p-overlay-right-start': modal && overlayResponsiveDirection === 'right-start',\n                'p-overlay-right-end': modal && overlayResponsiveDirection === 'right-end'\n            }\"\n            (click)=\"onOverlayClick()\"\n        >\n            <div\n                *ngIf=\"visible\"\n                #content\n                [ngStyle]=\"contentStyle\"\n                [class]=\"contentStyleClass\"\n                [ngClass]=\"'p-overlay-content'\"\n                (click)=\"onOverlayContentClick($event)\"\n                [@overlayContentAnimation]=\"{ value: 'visible', params: { showTransitionParams: showTransitionOptions, hideTransitionParams: hideTransitionOptions, transform: transformOptions[modal ? overlayResponsiveDirection : 'default'] } }\"\n                (@overlayContentAnimation.start)=\"onOverlayContentAnimationStart($event)\"\n                (@overlayContentAnimation.done)=\"onOverlayContentAnimationDone($event)\"\n            >\n                <ng-content></ng-content>\n                <ng-container *ngTemplateOutlet=\"contentTemplate; context: { $implicit: { mode: overlayMode } }\"></ng-container>\n            </div>\n        </div>\n    `, animations: [trigger('overlayContentAnimation', [transition(':enter', [useAnimation(showOverlayContentAnimation)]), transition(':leave', [useAnimation(hideOverlayContentAnimation)])])], changeDetection: ChangeDetectionStrategy.OnPush, encapsulation: ViewEncapsulation.None, providers: [OVERLAY_VALUE_ACCESSOR], host: {\n                        class: 'p-element'\n                    }, styles: [\"@layer primeng{.p-overlay{position:absolute;top:0;left:0}.p-overlay-modal{display:flex;align-items:center;justify-content:center;position:fixed;top:0;left:0;width:100%;height:100%}.p-overlay-content{transform-origin:inherit}.p-overlay-modal>.p-overlay-content{z-index:1;width:90%}.p-overlay-top{align-items:flex-start}.p-overlay-top-start{align-items:flex-start;justify-content:flex-start}.p-overlay-top-end{align-items:flex-start;justify-content:flex-end}.p-overlay-bottom{align-items:flex-end}.p-overlay-bottom-start{align-items:flex-end;justify-content:flex-start}.p-overlay-bottom-end{align-items:flex-end;justify-content:flex-end}.p-overlay-left{justify-content:flex-start}.p-overlay-left-start{justify-content:flex-start;align-items:flex-start}.p-overlay-left-end{justify-content:flex-start;align-items:flex-end}.p-overlay-right{justify-content:flex-end}.p-overlay-right-start{justify-content:flex-end;align-items:flex-start}.p-overlay-right-end{justify-content:flex-end;align-items:flex-end}}\\n\"] }]\n        }], ctorParameters: function () { return [{ type: Document, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [PLATFORM_ID]\n                }] }, { type: i0.ElementRef }, { type: i0.Renderer2 }, { type: i1.PrimeNGConfig }, { type: i1.OverlayService }, { type: i0.ChangeDetectorRef }, { type: i0.NgZone }]; }, propDecorators: { visible: [{\n                type: Input\n            }], mode: [{\n                type: Input\n            }], style: [{\n                type: Input\n            }], styleClass: [{\n                type: Input\n            }], contentStyle: [{\n                type: Input\n            }], contentStyleClass: [{\n                type: Input\n            }], target: [{\n                type: Input\n            }], appendTo: [{\n                type: Input\n            }], autoZIndex: [{\n                type: Input\n            }], baseZIndex: [{\n                type: Input\n            }], showTransitionOptions: [{\n                type: Input\n            }], hideTransitionOptions: [{\n                type: Input\n            }], listener: [{\n                type: Input\n            }], responsive: [{\n                type: Input\n            }], options: [{\n                type: Input\n            }], visibleChange: [{\n                type: Output\n            }], onBeforeShow: [{\n                type: Output\n            }], onShow: [{\n                type: Output\n            }], onBeforeHide: [{\n                type: Output\n            }], onHide: [{\n                type: Output\n            }], onAnimationStart: [{\n                type: Output\n            }], onAnimationDone: [{\n                type: Output\n            }], templates: [{\n                type: ContentChildren,\n                args: [PrimeTemplate]\n            }], overlayViewChild: [{\n                type: ViewChild,\n                args: ['overlay']\n            }], contentViewChild: [{\n                type: ViewChild,\n                args: ['content']\n            }] } });\nclass OverlayModule {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.0\", ngImport: i0, type: OverlayModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"16.2.0\", ngImport: i0, type: OverlayModule, declarations: [Overlay], imports: [CommonModule, SharedModule], exports: [Overlay, SharedModule] });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"16.2.0\", ngImport: i0, type: OverlayModule, imports: [CommonModule, SharedModule, SharedModule] });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.0\", ngImport: i0, type: OverlayModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [CommonModule, SharedModule],\n                    exports: [Overlay, SharedModule],\n                    declarations: [Overlay]\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { OVERLAY_VALUE_ACCESSOR, Overlay, OverlayModule };\n"], "mappings": "AAAA,SAASA,SAAS,EAAEC,KAAK,EAAEC,OAAO,EAAEC,OAAO,EAAEC,UAAU,EAAEC,YAAY,QAAQ,qBAAqB;AAClG,OAAO,KAAKC,EAAE,MAAM,iBAAiB;AACrC,SAASC,iBAAiB,EAAEC,QAAQ,EAAEC,YAAY,QAAQ,iBAAiB;AAC3E,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,UAAU,EAAEC,YAAY,EAAEC,WAAW,EAAEC,SAAS,EAAEC,uBAAuB,EAAEC,iBAAiB,EAAEC,MAAM,EAAEC,KAAK,EAAEC,MAAM,EAAEC,eAAe,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,eAAe;AACzL,SAASC,iBAAiB,QAAQ,gBAAgB;AAClD,OAAO,KAAKC,EAAE,MAAM,aAAa;AACjC,SAASC,aAAa,EAAEC,YAAY,QAAQ,aAAa;AACzD,SAASC,UAAU,EAAEC,6BAA6B,QAAQ,aAAa;AACvE,SAASC,WAAW,EAAEC,WAAW,QAAQ,eAAe;AAAC,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,SAAAC,4CAAAC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAifoCxB,EAAE,CAAA0B,kBAAA,EAsCgC,CAAC;EAAA;AAAA;AAAA,MAAAC,GAAA,YAAAA,CAAAC,EAAA,EAAAC,EAAA,EAAAC,EAAA;EAAA;IAAAC,oBAAA,EAAAH,EAAA;IAAAI,oBAAA,EAAAH,EAAA;IAAAI,SAAA,EAAAH;EAAA;AAAA;AAAA,MAAAI,GAAA,YAAAA,CAAAL,EAAA;EAAA;IAAAM,KAAA;IAAAC,MAAA,EAAAP;EAAA;AAAA;AAAA,MAAAQ,GAAA,YAAAA,CAAAT,EAAA;EAAA;IAAAU,IAAA,EAAAV;EAAA;AAAA;AAAA,MAAAW,GAAA,YAAAA,CAAAX,EAAA;EAAA;IAAAY,SAAA,EAAAZ;EAAA;AAAA;AAAA,SAAAa,6BAAAjB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAkB,GAAA,GAtCnC1C,EAAE,CAAA2C,gBAAA;IAAF3C,EAAE,CAAA4C,cAAA,eAoCnF,CAAC;IApCgF5C,EAAE,CAAA6C,UAAA,mBAAAC,kDAAAC,MAAA;MAAF/C,EAAE,CAAAgD,aAAA,CAAAN,GAAA;MAAA,MAAAO,MAAA,GAAFjD,EAAE,CAAAkD,aAAA;MAAA,OAAFlD,EAAE,CAAAmD,WAAA,CAgCtEF,MAAA,CAAAG,qBAAA,CAAAL,MAA4B,EAAC;IAAA,EAAC,4CAAAM,oFAAAN,MAAA;MAhCsC/C,EAAE,CAAAgD,aAAA,CAAAN,GAAA;MAAA,MAAAY,MAAA,GAAFtD,EAAE,CAAAkD,aAAA;MAAA,OAAFlD,EAAE,CAAAmD,WAAA,CAkC7CG,MAAA,CAAAC,8BAAA,CAAAR,MAAqC,EAAC;IAAA,CAFlC,CAAC,2CAAAS,mFAAAT,MAAA;MAhCsC/C,EAAE,CAAAgD,aAAA,CAAAN,GAAA;MAAA,MAAAe,MAAA,GAAFzD,EAAE,CAAAkD,aAAA;MAAA,OAAFlD,EAAE,CAAAmD,WAAA,CAmC9CM,MAAA,CAAAC,6BAAA,CAAAX,MAAoC,EAAC;IAAA,CAHhC,CAAC;IAhCsC/C,EAAE,CAAA2D,YAAA,EAqCvD,CAAC;IArCoD3D,EAAE,CAAA4D,UAAA,IAAArC,2CAAA,yBAsCgC,CAAC;IAtCnCvB,EAAE,CAAA6D,YAAA,CAuC9E,CAAC;EAAA;EAAA,IAAArC,EAAA;IAAA,MAAAsC,MAAA,GAvC2E9D,EAAE,CAAAkD,aAAA;IAAFlD,EAAE,CAAA+D,UAAA,CAAAD,MAAA,CAAAE,iBA8BrD,CAAC;IA9BkDhE,EAAE,CAAAiE,UAAA,YAAAH,MAAA,CAAAI,YA6BxD,CAAC,+BAAD,CAAC,6BA7BqDlE,EAAE,CAAAmE,eAAA,KAAAjC,GAAA,EAAFlC,EAAE,CAAAoE,eAAA,IAAAzC,GAAA,EAAAmC,MAAA,CAAAO,qBAAA,EAAAP,MAAA,CAAAQ,qBAAA,EAAAR,MAAA,CAAAS,gBAAA,CAAAT,MAAA,CAAAU,KAAA,GAAAV,MAAA,CAAAW,0BAAA,eA6BxD,CAAC;IA7BqDzE,EAAE,CAAA0E,SAAA,EAsC9B,CAAC;IAtC2B1E,EAAE,CAAAiE,UAAA,qBAAAH,MAAA,CAAAa,eAsC9B,CAAC,4BAtC2B3E,EAAE,CAAAmE,eAAA,KAAA5B,GAAA,EAAFvC,EAAE,CAAAmE,eAAA,KAAA9B,GAAA,EAAAyB,MAAA,CAAAc,WAAA,EAsC9B,CAAC;EAAA;AAAA;AAAA,MAAAC,GAAA,YAAAA,CAAAhD,EAAA,EAAAC,EAAA,EAAAgD,EAAA,EAAAC,EAAA,EAAAC,EAAA,EAAAC,EAAA,EAAAC,EAAA,EAAAC,EAAA,EAAAC,EAAA,EAAAC,GAAA,EAAAC,GAAA,EAAAC,GAAA,EAAAC,GAAA,EAAAC,GAAA;EAAA;IAAA;IAAA,iEAAA5D,EAAA;IAAA,oBAAAC,EAAA;IAAA,iBAAAgD,EAAA;IAAA,uBAAAC,EAAA;IAAA,qBAAAC,EAAA;IAAA,oBAAAC,EAAA;IAAA,0BAAAC,EAAA;IAAA,wBAAAC,EAAA;IAAA,kBAAAC,EAAA;IAAA,wBAAAC,GAAA;IAAA,sBAAAC,GAAA;IAAA,mBAAAC,GAAA;IAAA,yBAAAC,GAAA;IAAA,uBAAAC;EAAA;AAAA;AAAA,SAAAC,uBAAAlE,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAmE,IAAA,GAtC2B3F,EAAE,CAAA2C,gBAAA;IAAF3C,EAAE,CAAA4C,cAAA,eAyBvF,CAAC;IAzBoF5C,EAAE,CAAA6C,UAAA,mBAAA+C,4CAAA;MAAF5F,EAAE,CAAAgD,aAAA,CAAA2C,IAAA;MAAA,MAAAE,MAAA,GAAF7F,EAAE,CAAAkD,aAAA;MAAA,OAAFlD,EAAE,CAAAmD,WAAA,CAwB1E0C,MAAA,CAAAC,cAAA,CAAe,EAAC;IAAA,EAAC;IAxBuD9F,EAAE,CAAA4D,UAAA,IAAAnB,4BAAA,iBAuC9E,CAAC;IAvC2EzC,EAAE,CAAA6D,YAAA,CAwClF,CAAC;EAAA;EAAA,IAAArC,EAAA;IAAA,MAAAuE,MAAA,GAxC+E/F,EAAE,CAAAkD,aAAA;IAAFlD,EAAE,CAAA+D,UAAA,CAAAgC,MAAA,CAAAC,UAMhE,CAAC;IAN6DhG,EAAE,CAAAiE,UAAA,YAAA8B,MAAA,CAAAxG,KAKnE,CAAC,YALgES,EAAE,CAAAiG,eAAA,IAAApB,GAAA,GAAAkB,MAAA,CAAAvB,KAAA,EAAAuB,MAAA,CAAAvB,KAAA,IAAAuB,MAAA,CAAAtB,0BAAA,eAAAsB,MAAA,CAAAvB,KAAA,IAAAuB,MAAA,CAAAtB,0BAAA,YAAAsB,MAAA,CAAAvB,KAAA,IAAAuB,MAAA,CAAAtB,0BAAA,kBAAAsB,MAAA,CAAAvB,KAAA,IAAAuB,MAAA,CAAAtB,0BAAA,gBAAAsB,MAAA,CAAAvB,KAAA,IAAAuB,MAAA,CAAAtB,0BAAA,eAAAsB,MAAA,CAAAvB,KAAA,IAAAuB,MAAA,CAAAtB,0BAAA,qBAAAsB,MAAA,CAAAvB,KAAA,IAAAuB,MAAA,CAAAtB,0BAAA,mBAAAsB,MAAA,CAAAvB,KAAA,IAAAuB,MAAA,CAAAtB,0BAAA,aAAAsB,MAAA,CAAAvB,KAAA,IAAAuB,MAAA,CAAAtB,0BAAA,mBAAAsB,MAAA,CAAAvB,KAAA,IAAAuB,MAAA,CAAAtB,0BAAA,iBAAAsB,MAAA,CAAAvB,KAAA,IAAAuB,MAAA,CAAAtB,0BAAA,cAAAsB,MAAA,CAAAvB,KAAA,IAAAuB,MAAA,CAAAtB,0BAAA,oBAAAsB,MAAA,CAAAvB,KAAA,IAAAuB,MAAA,CAAAtB,0BAAA,kBAKnE,CAAC;IALgEzE,EAAE,CAAA0E,SAAA,EA2BlE,CAAC;IA3B+D1E,EAAE,CAAAiE,UAAA,SAAA8B,MAAA,CAAAG,OA2BlE,CAAC;EAAA;AAAA;AAAA,MAAAC,GAAA;AA1gB9B,MAAMC,sBAAsB,GAAG;EAC3BC,OAAO,EAAExF,iBAAiB;EAC1ByF,WAAW,EAAErG,UAAU,CAAC,MAAMsG,OAAO,CAAC;EACtCC,KAAK,EAAE;AACX,CAAC;AACD,MAAMC,2BAA2B,GAAGnH,SAAS,CAAC,CAACC,KAAK,CAAC;EAAE0C,SAAS,EAAE,eAAe;EAAEyE,OAAO,EAAE;AAAE,CAAC,CAAC,EAAElH,OAAO,CAAC,0BAA0B,CAAC,CAAC,CAAC;AACvI,MAAMmH,2BAA2B,GAAGrH,SAAS,CAAC,CAACE,OAAO,CAAC,0BAA0B,EAAED,KAAK,CAAC;EAAE0C,SAAS,EAAE,eAAe;EAAEyE,OAAO,EAAE;AAAE,CAAC,CAAC,CAAC,CAAC,CAAC;AACvI;AACA;AACA;AACA;AACA,MAAMH,OAAO,CAAC;EACVK,QAAQ;EACRC,UAAU;EACVC,EAAE;EACFC,QAAQ;EACRC,MAAM;EACNC,cAAc;EACdC,EAAE;EACFC,IAAI;EACJ;AACJ;AACA;AACA;AACA;EACI,IAAIjB,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAACkB,QAAQ;EACxB;EACA,IAAIlB,OAAOA,CAAC/D,KAAK,EAAE;IACf,IAAI,CAACiF,QAAQ,GAAGjF,KAAK;IACrB,IAAI,IAAI,CAACiF,QAAQ,IAAI,CAAC,IAAI,CAACC,YAAY,EAAE;MACrC,IAAI,CAACA,YAAY,GAAG,IAAI;IAC5B;EACJ;EACA;AACJ;AACA;AACA;AACA;EACI,IAAI/E,IAAIA,CAAA,EAAG;IACP,OAAO,IAAI,CAACgF,KAAK,IAAI,IAAI,CAACC,cAAc,EAAEjF,IAAI;EAClD;EACA,IAAIA,IAAIA,CAACH,KAAK,EAAE;IACZ,IAAI,CAACmF,KAAK,GAAGnF,KAAK;EACtB;EACA;AACJ;AACA;AACA;AACA;EACI,IAAI5C,KAAKA,CAAA,EAAG;IACR,OAAO4B,WAAW,CAACqG,KAAK,CAAC,IAAI,CAACC,MAAM,EAAE,IAAI,CAACjD,KAAK,GAAG,IAAI,CAACkD,wBAAwB,EAAEnI,KAAK,GAAG,IAAI,CAACgI,cAAc,EAAEhI,KAAK,CAAC;EACzH;EACA,IAAIA,KAAKA,CAAC4C,KAAK,EAAE;IACb,IAAI,CAACsF,MAAM,GAAGtF,KAAK;EACvB;EACA;AACJ;AACA;AACA;AACA;EACI,IAAI6D,UAAUA,CAAA,EAAG;IACb,OAAO7E,WAAW,CAACqG,KAAK,CAAC,IAAI,CAACG,WAAW,EAAE,IAAI,CAACnD,KAAK,GAAG,IAAI,CAACkD,wBAAwB,EAAE1B,UAAU,GAAG,IAAI,CAACuB,cAAc,EAAEvB,UAAU,CAAC;EACxI;EACA,IAAIA,UAAUA,CAAC7D,KAAK,EAAE;IAClB,IAAI,CAACwF,WAAW,GAAGxF,KAAK;EAC5B;EACA;AACJ;AACA;AACA;AACA;EACI,IAAI+B,YAAYA,CAAA,EAAG;IACf,OAAO/C,WAAW,CAACqG,KAAK,CAAC,IAAI,CAACI,aAAa,EAAE,IAAI,CAACpD,KAAK,GAAG,IAAI,CAACkD,wBAAwB,EAAExD,YAAY,GAAG,IAAI,CAACqD,cAAc,EAAErD,YAAY,CAAC;EAC9I;EACA,IAAIA,YAAYA,CAAC/B,KAAK,EAAE;IACpB,IAAI,CAACyF,aAAa,GAAGzF,KAAK;EAC9B;EACA;AACJ;AACA;AACA;AACA;EACI,IAAI6B,iBAAiBA,CAAA,EAAG;IACpB,OAAO7C,WAAW,CAACqG,KAAK,CAAC,IAAI,CAACK,kBAAkB,EAAE,IAAI,CAACrD,KAAK,GAAG,IAAI,CAACkD,wBAAwB,EAAE1D,iBAAiB,GAAG,IAAI,CAACuD,cAAc,EAAEvD,iBAAiB,CAAC;EAC7J;EACA,IAAIA,iBAAiBA,CAAC7B,KAAK,EAAE;IACzB,IAAI,CAAC0F,kBAAkB,GAAG1F,KAAK;EACnC;EACA;AACJ;AACA;AACA;AACA;EACI,IAAI2F,MAAMA,CAAA,EAAG;IACT,MAAM3F,KAAK,GAAG,IAAI,CAAC4F,OAAO,IAAI,IAAI,CAACR,cAAc,EAAEO,MAAM;IACzD,OAAO3F,KAAK,KAAK6F,SAAS,GAAG,OAAO,GAAG7F,KAAK;EAChD;EACA,IAAI2F,MAAMA,CAAC3F,KAAK,EAAE;IACd,IAAI,CAAC4F,OAAO,GAAG5F,KAAK;EACxB;EACA;AACJ;AACA;AACA;AACA;EACI,IAAI8F,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACC,SAAS,IAAI,IAAI,CAACX,cAAc,EAAEU,QAAQ;EAC1D;EACA,IAAIA,QAAQA,CAAC9F,KAAK,EAAE;IAChB,IAAI,CAAC+F,SAAS,GAAG/F,KAAK;EAC1B;EACA;AACJ;AACA;AACA;AACA;EACI,IAAIgG,UAAUA,CAAA,EAAG;IACb,MAAMhG,KAAK,GAAG,IAAI,CAACiG,WAAW,IAAI,IAAI,CAACb,cAAc,EAAEY,UAAU;IACjE,OAAOhG,KAAK,KAAK6F,SAAS,GAAG,IAAI,GAAG7F,KAAK;EAC7C;EACA,IAAIgG,UAAUA,CAAChG,KAAK,EAAE;IAClB,IAAI,CAACiG,WAAW,GAAGjG,KAAK;EAC5B;EACA;AACJ;AACA;AACA;AACA;EACI,IAAIkG,UAAUA,CAAA,EAAG;IACb,MAAMlG,KAAK,GAAG,IAAI,CAACmG,WAAW,IAAI,IAAI,CAACf,cAAc,EAAEc,UAAU;IACjE,OAAOlG,KAAK,KAAK6F,SAAS,GAAG,CAAC,GAAG7F,KAAK;EAC1C;EACA,IAAIkG,UAAUA,CAAClG,KAAK,EAAE;IAClB,IAAI,CAACmG,WAAW,GAAGnG,KAAK;EAC5B;EACA;AACJ;AACA;AACA;AACA;EACI,IAAIkC,qBAAqBA,CAAA,EAAG;IACxB,MAAMlC,KAAK,GAAG,IAAI,CAACoG,sBAAsB,IAAI,IAAI,CAAChB,cAAc,EAAElD,qBAAqB;IACvF,OAAOlC,KAAK,KAAK6F,SAAS,GAAG,iCAAiC,GAAG7F,KAAK;EAC1E;EACA,IAAIkC,qBAAqBA,CAAClC,KAAK,EAAE;IAC7B,IAAI,CAACoG,sBAAsB,GAAGpG,KAAK;EACvC;EACA;AACJ;AACA;AACA;AACA;EACI,IAAImC,qBAAqBA,CAAA,EAAG;IACxB,MAAMnC,KAAK,GAAG,IAAI,CAACqG,sBAAsB,IAAI,IAAI,CAACjB,cAAc,EAAEjD,qBAAqB;IACvF,OAAOnC,KAAK,KAAK6F,SAAS,GAAG,YAAY,GAAG7F,KAAK;EACrD;EACA,IAAImC,qBAAqBA,CAACnC,KAAK,EAAE;IAC7B,IAAI,CAACqG,sBAAsB,GAAGrG,KAAK;EACvC;EACA;AACJ;AACA;AACA;AACA;EACI,IAAIsG,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACC,SAAS,IAAI,IAAI,CAACnB,cAAc,EAAEkB,QAAQ;EAC1D;EACA,IAAIA,QAAQA,CAACtG,KAAK,EAAE;IAChB,IAAI,CAACuG,SAAS,GAAGvG,KAAK;EAC1B;EACA;AACJ;AACA;AACA;AACA;EACI,IAAIwG,UAAUA,CAAA,EAAG;IACb,OAAO,IAAI,CAACC,WAAW,IAAI,IAAI,CAACrB,cAAc,EAAEoB,UAAU;EAC9D;EACA,IAAIA,UAAUA,CAACE,GAAG,EAAE;IAChB,IAAI,CAACD,WAAW,GAAGC,GAAG;EAC1B;EACA;AACJ;AACA;AACA;AACA;EACI,IAAIC,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAACC,QAAQ;EACxB;EACA,IAAID,OAAOA,CAACD,GAAG,EAAE;IACb,IAAI,CAACE,QAAQ,GAAGF,GAAG;EACvB;EACA;AACJ;AACA;AACA;AACA;EACIG,aAAa,GAAG,IAAI9I,YAAY,CAAC,CAAC;EAClC;AACJ;AACA;AACA;AACA;EACI+I,YAAY,GAAG,IAAI/I,YAAY,CAAC,CAAC;EACjC;AACJ;AACA;AACA;AACA;EACIgJ,MAAM,GAAG,IAAIhJ,YAAY,CAAC,CAAC;EAC3B;AACJ;AACA;AACA;AACA;EACIiJ,YAAY,GAAG,IAAIjJ,YAAY,CAAC,CAAC;EACjC;AACJ;AACA;AACA;AACA;EACIkJ,MAAM,GAAG,IAAIlJ,YAAY,CAAC,CAAC;EAC3B;AACJ;AACA;AACA;AACA;EACImJ,gBAAgB,GAAG,IAAInJ,YAAY,CAAC,CAAC;EACrC;AACJ;AACA;AACA;AACA;EACIoJ,eAAe,GAAG,IAAIpJ,YAAY,CAAC,CAAC;EACpCqJ,SAAS;EACTC,gBAAgB;EAChBC,gBAAgB;EAChB9E,eAAe;EACfyC,QAAQ,GAAG,KAAK;EAChBE,KAAK;EACLG,MAAM;EACNE,WAAW;EACXC,aAAa;EACbC,kBAAkB;EAClBE,OAAO;EACPG,SAAS;EACTE,WAAW;EACXE,WAAW;EACXC,sBAAsB;EACtBC,sBAAsB;EACtBE,SAAS;EACTE,WAAW;EACXG,QAAQ;EACR1B,YAAY,GAAG,KAAK;EACpBqC,gBAAgB,GAAG,KAAK;EACxBC,uBAAuB,GAAG,KAAK;EAC/BC,aAAa;EACbC,qBAAqB;EACrBC,sBAAsB;EACtBC,wBAAwB;EACxBC,MAAM;EACNzF,gBAAgB,GAAG;IACf0F,OAAO,EAAE,aAAa;IACtBC,MAAM,EAAE,YAAY;IACpBC,GAAG,EAAE,8BAA8B;IACnC,WAAW,EAAE,8BAA8B;IAC3C,SAAS,EAAE,8BAA8B;IACzCC,MAAM,EAAE,6BAA6B;IACrC,cAAc,EAAE,6BAA6B;IAC7C,YAAY,EAAE,6BAA6B;IAC3CC,IAAI,EAAE,8BAA8B;IACpC,YAAY,EAAE,8BAA8B;IAC5C,UAAU,EAAE,8BAA8B;IAC1CC,KAAK,EAAE,6BAA6B;IACpC,aAAa,EAAE,6BAA6B;IAC5C,WAAW,EAAE;EACjB,CAAC;EACD,IAAI9F,KAAKA,CAAA,EAAG;IACR,IAAI3E,iBAAiB,CAAC,IAAI,CAACgH,UAAU,CAAC,EAAE;MACpC,OAAO,IAAI,CAACvE,IAAI,KAAK,OAAO,IAAK,IAAI,CAACoF,wBAAwB,IAAI,IAAI,CAACsC,MAAM,EAAEO,UAAU,CAAC,IAAI,CAAC7C,wBAAwB,CAAC8C,KAAK,EAAEC,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC,IAAK,eAAc,IAAI,CAAC/C,wBAAwB,CAACgD,UAAW,GAAE,CAAC,CAACC,OAAQ;IAChO;EACJ;EACA,IAAI/F,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAACtC,IAAI,KAAK,IAAI,CAACkC,KAAK,GAAG,OAAO,GAAG,SAAS,CAAC;EAC1D;EACA,IAAI+C,cAAcA,CAAA,EAAG;IACjB,OAAO;MAAE,GAAG,IAAI,CAACP,MAAM,EAAEO,cAAc;MAAE,GAAG,IAAI,CAACuB;IAAQ,CAAC,CAAC,CAAC;EAChE;;EACA,IAAIpB,wBAAwBA,CAAA,EAAG;IAC3B,OAAO;MAAE,GAAG,IAAI,CAACH,cAAc,EAAEoB,UAAU;MAAE,GAAG,IAAI,CAACA;IAAW,CAAC,CAAC,CAAC;EACvE;;EACA,IAAIlE,0BAA0BA,CAAA,EAAG;IAC7B,OAAO,IAAI,CAACiD,wBAAwB,EAAEkD,SAAS,IAAI,QAAQ;EAC/D;EACA,IAAIC,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACrB,gBAAgB,EAAEsB,aAAa;EAC/C;EACA,IAAIC,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACtB,gBAAgB,EAAEqB,aAAa;EAC/C;EACA,IAAIE,QAAQA,CAAA,EAAG;IACX,OAAO/J,UAAU,CAACgK,gBAAgB,CAAC,IAAI,CAACnD,MAAM,EAAE,IAAI,CAAChB,EAAE,EAAEgE,aAAa,CAAC;EAC3E;EACAI,WAAWA,CAACtE,QAAQ,EAAEC,UAAU,EAAEC,EAAE,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,cAAc,EAAEC,EAAE,EAAEC,IAAI,EAAE;IAC9E,IAAI,CAACP,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACC,EAAE,GAAGA,EAAE;IACZ,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACC,EAAE,GAAGA,EAAE;IACZ,IAAI,CAACC,IAAI,GAAGA,IAAI;IAChB,IAAI,CAAC6C,MAAM,GAAG,IAAI,CAACpD,QAAQ,CAACuE,WAAW;EAC3C;EACAC,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAAC7B,SAAS,EAAE8B,OAAO,CAAEC,IAAI,IAAK;MAC9B,QAAQA,IAAI,CAACC,OAAO,CAAC,CAAC;QAClB,KAAK,SAAS;UACV,IAAI,CAAC5G,eAAe,GAAG2G,IAAI,CAACE,QAAQ;UACpC;QACJ;QACA;UACI,IAAI,CAAC7G,eAAe,GAAG2G,IAAI,CAACE,QAAQ;UACpC;MACR;IACJ,CAAC,CAAC;EACN;EACAC,IAAIA,CAACC,OAAO,EAAEC,OAAO,GAAG,KAAK,EAAE;IAC3B,IAAI,CAACC,eAAe,CAAC,IAAI,CAAC;IAC1B,IAAI,CAACC,YAAY,CAAC,QAAQ,EAAE;MAAEH,OAAO,EAAEA,OAAO,IAAI,IAAI,CAACb,SAAS;MAAE/C,MAAM,EAAE,IAAI,CAACkD,QAAQ;MAAE1I,IAAI,EAAE,IAAI,CAACsC;IAAY,CAAC,CAAC;IAClH+G,OAAO,IAAI1K,UAAU,CAAC6K,KAAK,CAAC,IAAI,CAACd,QAAQ,CAAC;IAC1C,IAAI,CAACxG,KAAK,IAAIvD,UAAU,CAAC8K,QAAQ,CAAC,IAAI,CAACnF,QAAQ,EAAEoF,IAAI,EAAE,mBAAmB,CAAC;EAC/E;EACAC,IAAIA,CAACP,OAAO,EAAEC,OAAO,GAAG,KAAK,EAAE;IAC3B,IAAI,CAAC,IAAI,CAACzF,OAAO,EAAE;MACf;IACJ,CAAC,MACI;MACD,IAAI,CAAC0F,eAAe,CAAC,KAAK,CAAC;MAC3B,IAAI,CAACC,YAAY,CAAC,QAAQ,EAAE;QAAEH,OAAO,EAAEA,OAAO,IAAI,IAAI,CAACb,SAAS;QAAE/C,MAAM,EAAE,IAAI,CAACkD,QAAQ;QAAE1I,IAAI,EAAE,IAAI,CAACsC;MAAY,CAAC,CAAC;MAClH+G,OAAO,IAAI1K,UAAU,CAAC6K,KAAK,CAAC,IAAI,CAACd,QAAQ,CAAC;MAC1C,IAAI,CAACxG,KAAK,IAAIvD,UAAU,CAACiL,WAAW,CAAC,IAAI,CAACtF,QAAQ,EAAEoF,IAAI,EAAE,mBAAmB,CAAC;IAClF;EACJ;EACAG,YAAYA,CAAA,EAAG;IACX,CAAC,IAAI,CAAC3H,KAAK,IAAIvD,UAAU,CAACkL,YAAY,CAAC,IAAI,CAACtB,SAAS,EAAE,IAAI,CAACG,QAAQ,EAAE,IAAI,CAAC/C,QAAQ,CAAC;EACxF;EACA2D,eAAeA,CAAC1F,OAAO,EAAE;IACrB,IAAI,CAACkB,QAAQ,GAAGlB,OAAO;IACvB,IAAI,CAAC8C,aAAa,CAACoD,IAAI,CAAClG,OAAO,CAAC;EACpC;EACAJ,cAAcA,CAAA,EAAG;IACb,IAAI,CAAC4D,gBAAgB,GAAG,IAAI;EAChC;EACAtG,qBAAqBA,CAACiJ,KAAK,EAAE;IACzB,IAAI,CAACpF,cAAc,CAACqF,GAAG,CAAC;MACpBC,aAAa,EAAEF,KAAK;MACpBvE,MAAM,EAAE,IAAI,CAACkD;IACjB,CAAC,CAAC;IACF,IAAI,CAACrB,uBAAuB,GAAG,IAAI;EACvC;EACApG,8BAA8BA,CAAC8I,KAAK,EAAE;IAClC,QAAQA,KAAK,CAACG,OAAO;MACjB,KAAK,SAAS;QACV,IAAI,CAACX,YAAY,CAAC,cAAc,EAAE;UAAEH,OAAO,EAAE,IAAI,CAACb,SAAS;UAAE/C,MAAM,EAAE,IAAI,CAACkD,QAAQ;UAAE1I,IAAI,EAAE,IAAI,CAACsC;QAAY,CAAC,CAAC;QAC7G,IAAI,IAAI,CAACuD,UAAU,EAAE;UACjB/G,WAAW,CAACqL,GAAG,CAAC,IAAI,CAAC7H,WAAW,EAAE,IAAI,CAACiG,SAAS,EAAE,IAAI,CAACxC,UAAU,GAAG,IAAI,CAACrB,MAAM,EAAE0F,MAAM,CAAC,IAAI,CAAC9H,WAAW,CAAC,CAAC;QAC9G;QACA3D,UAAU,CAAC0L,aAAa,CAAC,IAAI,CAAC9B,SAAS,EAAE,IAAI,CAAC5C,QAAQ,KAAK,MAAM,GAAG,IAAI,CAACrB,QAAQ,CAACoF,IAAI,GAAG,IAAI,CAAC/D,QAAQ,EAAE,IAAI,CAACA,QAAQ,CAAC;QACtH,IAAI,CAACkE,YAAY,CAAC,CAAC;QACnB;MACJ,KAAK,MAAM;QACP,IAAI,CAACN,YAAY,CAAC,cAAc,EAAE;UAAEH,OAAO,EAAE,IAAI,CAACb,SAAS;UAAE/C,MAAM,EAAE,IAAI,CAACkD,QAAQ;UAAE1I,IAAI,EAAE,IAAI,CAACsC;QAAY,CAAC,CAAC;QAC7G,IAAI,CAACJ,KAAK,IAAIvD,UAAU,CAAC8K,QAAQ,CAAC,IAAI,CAAClB,SAAS,EAAE,2BAA2B,CAAC;QAC9E;IACR;IACA,IAAI,CAACgB,YAAY,CAAC,kBAAkB,EAAEQ,KAAK,CAAC;EAChD;EACA3I,6BAA6BA,CAAC2I,KAAK,EAAE;IACjC,MAAMO,SAAS,GAAG,IAAI,CAAC/B,SAAS,IAAIwB,KAAK,CAACQ,OAAO,CAACC,aAAa;IAC/D,QAAQT,KAAK,CAACG,OAAO;MACjB,KAAK,SAAS;QACV,IAAI,CAACf,IAAI,CAACmB,SAAS,EAAE,IAAI,CAAC;QAC1B,IAAI,CAACG,aAAa,CAAC,CAAC;QACpB;MACJ,KAAK,MAAM;QACP,IAAI,CAACd,IAAI,CAACW,SAAS,EAAE,IAAI,CAAC;QAC1B,IAAI,CAACI,eAAe,CAAC,CAAC;QACtB/L,UAAU,CAAC0L,aAAa,CAAC,IAAI,CAAC9B,SAAS,EAAE,IAAI,CAACG,QAAQ,EAAE,IAAI,CAAC/C,QAAQ,CAAC;QACtE7G,WAAW,CAAC6L,KAAK,CAACL,SAAS,CAAC;QAC5B,IAAI,CAACvF,YAAY,GAAG,KAAK;QACzB,IAAI,CAACH,EAAE,CAACgG,YAAY,CAAC,CAAC;QACtB;IACR;IACA,IAAI,CAACrB,YAAY,CAAC,iBAAiB,EAAEQ,KAAK,CAAC;EAC/C;EACAR,YAAYA,CAACsB,IAAI,EAAE/K,MAAM,EAAE;IACvB,IAAI,CAAC+K,IAAI,CAAC,CAACf,IAAI,CAAChK,MAAM,CAAC;IACvB,IAAI,CAAC0G,OAAO,IAAI,IAAI,CAACA,OAAO,CAACqE,IAAI,CAAC,IAAI,IAAI,CAACrE,OAAO,CAACqE,IAAI,CAAC,CAAC/K,MAAM,CAAC;IAChE,IAAI,CAAC4E,MAAM,EAAEO,cAAc,IAAI,CAAC,IAAI,CAACP,MAAM,EAAEO,cAAc,EAAE4F,IAAI,CAAC,IAAI,CAAC,IAAI,CAACnG,MAAM,EAAEO,cAAc,EAAE4F,IAAI,CAAC,CAAC/K,MAAM,CAAC;EACrH;EACA2K,aAAaA,CAAA,EAAG;IACZ,IAAI,CAACK,kBAAkB,CAAC,CAAC;IACzB,IAAI,CAACC,yBAAyB,CAAC,CAAC;IAChC,IAAI,CAACC,0BAA0B,CAAC,CAAC;IACjC,IAAI,CAACC,4BAA4B,CAAC,CAAC;EACvC;EACAP,eAAeA,CAAA,EAAG;IACd,IAAI,CAACQ,oBAAoB,CAAC,CAAC;IAC3B,IAAI,CAACC,2BAA2B,CAAC,CAAC;IAClC,IAAI,CAACC,4BAA4B,CAAC,CAAC;IACnC,IAAI,CAACC,8BAA8B,CAAC,CAAC;EACzC;EACAP,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAAC,IAAI,CAACxD,aAAa,EAAE;MACrB,IAAI,CAACA,aAAa,GAAG,IAAI1I,6BAA6B,CAAC,IAAI,CAAC8J,QAAQ,EAAGqB,KAAK,IAAK;QAC7E,MAAMuB,KAAK,GAAG,IAAI,CAACnF,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAAC4D,KAAK,EAAE;UAAEwB,IAAI,EAAE,QAAQ;UAAEvL,IAAI,EAAE,IAAI,CAACsC,WAAW;UAAEgJ,KAAK,EAAE;QAAK,CAAC,CAAC,GAAG,IAAI;QAClHA,KAAK,IAAI,IAAI,CAAC3B,IAAI,CAACI,KAAK,EAAE,IAAI,CAAC;MACnC,CAAC,CAAC;IACN;IACA,IAAI,CAACzC,aAAa,CAACwD,kBAAkB,CAAC,CAAC;EAC3C;EACAI,oBAAoBA,CAAA,EAAG;IACnB,IAAI,IAAI,CAAC5D,aAAa,EAAE;MACpB,IAAI,CAACA,aAAa,CAAC4D,oBAAoB,CAAC,CAAC;IAC7C;EACJ;EACAH,yBAAyBA,CAAA,EAAG;IACxB,IAAI,CAAC,IAAI,CAACxD,qBAAqB,EAAE;MAC7B,IAAI,CAACA,qBAAqB,GAAG,IAAI,CAAC9C,QAAQ,CAAC+G,MAAM,CAAC,IAAI,CAAClH,QAAQ,EAAE,OAAO,EAAGyF,KAAK,IAAK;QACjF,MAAM0B,eAAe,GAAG,IAAI,CAAC/C,QAAQ,KAAK,IAAI,CAACA,QAAQ,CAACgD,UAAU,CAAC3B,KAAK,CAACvE,MAAM,CAAC,IAAK,CAAC,IAAI,CAAC4B,gBAAgB,IAAI,IAAI,CAACsB,QAAQ,CAACiD,QAAQ,CAAC5B,KAAK,CAACvE,MAAM,CAAE,CAAC;QACrJ,MAAMoG,gBAAgB,GAAG,CAACH,eAAe,IAAI,CAAC,IAAI,CAACpE,uBAAuB;QAC1E,MAAMiE,KAAK,GAAG,IAAI,CAACnF,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAAC4D,KAAK,EAAE;UAAEwB,IAAI,EAAE,SAAS;UAAEvL,IAAI,EAAE,IAAI,CAACsC,WAAW;UAAEgJ,KAAK,EAAEvB,KAAK,CAAC8B,KAAK,KAAK,CAAC,IAAID;QAAiB,CAAC,CAAC,GAAGA,gBAAgB;QAChKN,KAAK,IAAI,IAAI,CAAC3B,IAAI,CAACI,KAAK,CAAC;QACzB,IAAI,CAAC3C,gBAAgB,GAAG,IAAI,CAACC,uBAAuB,GAAG,KAAK;MAChE,CAAC,CAAC;IACN;EACJ;EACA8D,2BAA2BA,CAAA,EAAG;IAC1B,IAAI,IAAI,CAAC5D,qBAAqB,EAAE;MAC5B,IAAI,CAACA,qBAAqB,CAAC,CAAC;MAC5B,IAAI,CAACA,qBAAqB,GAAG,IAAI;IACrC;EACJ;EACAyD,0BAA0BA,CAAA,EAAG;IACzB,IAAI,CAAC,IAAI,CAACxD,sBAAsB,EAAE;MAC9B,IAAI,CAACA,sBAAsB,GAAG,IAAI,CAAC/C,QAAQ,CAAC+G,MAAM,CAAC,IAAI,CAAC9D,MAAM,EAAE,QAAQ,EAAGqC,KAAK,IAAK;QACjF,MAAMuB,KAAK,GAAG,IAAI,CAACnF,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAAC4D,KAAK,EAAE;UAAEwB,IAAI,EAAE,QAAQ;UAAEvL,IAAI,EAAE,IAAI,CAACsC,WAAW;UAAEgJ,KAAK,EAAE,CAAC3M,UAAU,CAACmN,aAAa,CAAC;QAAE,CAAC,CAAC,GAAG,CAACnN,UAAU,CAACmN,aAAa,CAAC,CAAC;QAChKR,KAAK,IAAI,IAAI,CAAC3B,IAAI,CAACI,KAAK,EAAE,IAAI,CAAC;MACnC,CAAC,CAAC;IACN;EACJ;EACAqB,4BAA4BA,CAAA,EAAG;IAC3B,IAAI,IAAI,CAAC5D,sBAAsB,EAAE;MAC7B,IAAI,CAACA,sBAAsB,CAAC,CAAC;MAC7B,IAAI,CAACA,sBAAsB,GAAG,IAAI;IACtC;EACJ;EACAyD,4BAA4BA,CAAA,EAAG;IAC3B,IAAI,IAAI,CAACxD,wBAAwB,EAAE;MAC/B;IACJ;IACA,IAAI,CAAC5C,IAAI,CAACkH,iBAAiB,CAAC,MAAM;MAC9B,IAAI,CAACtE,wBAAwB,GAAG,IAAI,CAAChD,QAAQ,CAAC+G,MAAM,CAAC,IAAI,CAAC9D,MAAM,EAAE,SAAS,EAAGqC,KAAK,IAAK;QACpF,IAAI,IAAI,CAAC9E,cAAc,CAAC+G,YAAY,KAAK,KAAK,IAAIjC,KAAK,CAACkC,IAAI,KAAK,QAAQ,EAAE;UACvE;QACJ;QACA,MAAMX,KAAK,GAAG,IAAI,CAACnF,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAAC4D,KAAK,EAAE;UAAEwB,IAAI,EAAE,SAAS;UAAEvL,IAAI,EAAE,IAAI,CAACsC,WAAW;UAAEgJ,KAAK,EAAE,CAAC3M,UAAU,CAACmN,aAAa,CAAC;QAAE,CAAC,CAAC,GAAG,CAACnN,UAAU,CAACmN,aAAa,CAAC,CAAC;QACjK,IAAIR,KAAK,EAAE;UACP,IAAI,CAACzG,IAAI,CAACqH,GAAG,CAAC,MAAM;YAChB,IAAI,CAACvC,IAAI,CAACI,KAAK,EAAE,IAAI,CAAC;UAC1B,CAAC,CAAC;QACN;MACJ,CAAC,CAAC;IACN,CAAC,CAAC;EACN;EACAsB,8BAA8BA,CAAA,EAAG;IAC7B,IAAI,IAAI,CAAC5D,wBAAwB,EAAE;MAC/B,IAAI,CAACA,wBAAwB,CAAC,CAAC;MAC/B,IAAI,CAACA,wBAAwB,GAAG,IAAI;IACxC;EACJ;EACA0E,WAAWA,CAAA,EAAG;IACV,IAAI,CAACxC,IAAI,CAAC,IAAI,CAACpB,SAAS,EAAE,IAAI,CAAC;IAC/B,IAAI,IAAI,CAACA,SAAS,EAAE;MAChB5J,UAAU,CAAC0L,aAAa,CAAC,IAAI,CAAC9B,SAAS,EAAE,IAAI,CAACG,QAAQ,EAAE,IAAI,CAAC/C,QAAQ,CAAC;MACtE7G,WAAW,CAAC6L,KAAK,CAAC,IAAI,CAACpC,SAAS,CAAC;IACrC;IACA,IAAI,IAAI,CAACjB,aAAa,EAAE;MACpB,IAAI,CAACA,aAAa,CAAC8E,OAAO,CAAC,CAAC;MAC5B,IAAI,CAAC9E,aAAa,GAAG,IAAI;IAC7B;IACA,IAAI,CAACoD,eAAe,CAAC,CAAC;EAC1B;EACA,OAAO2B,IAAI,YAAAC,gBAAAC,CAAA;IAAA,YAAAA,CAAA,IAAwFtI,OAAO,EAAjBvG,EAAE,CAAA8O,iBAAA,CAAiChP,QAAQ,GAA3CE,EAAE,CAAA8O,iBAAA,CAAsD3O,WAAW,GAAnEH,EAAE,CAAA8O,iBAAA,CAA8E9O,EAAE,CAAC+O,UAAU,GAA7F/O,EAAE,CAAA8O,iBAAA,CAAwG9O,EAAE,CAACgP,SAAS,GAAtHhP,EAAE,CAAA8O,iBAAA,CAAiIhO,EAAE,CAACmO,aAAa,GAAnJjP,EAAE,CAAA8O,iBAAA,CAA8JhO,EAAE,CAACoO,cAAc,GAAjLlP,EAAE,CAAA8O,iBAAA,CAA4L9O,EAAE,CAACmP,iBAAiB,GAAlNnP,EAAE,CAAA8O,iBAAA,CAA6N9O,EAAE,CAACoP,MAAM;EAAA;EACjU,OAAOC,IAAI,kBAD8ErP,EAAE,CAAAsP,iBAAA;IAAAzB,IAAA,EACJtH,OAAO;IAAAgJ,SAAA;IAAAC,cAAA,WAAAC,uBAAAjO,EAAA,EAAAC,GAAA,EAAAiO,QAAA;MAAA,IAAAlO,EAAA;QADLxB,EAAE,CAAA2P,cAAA,CAAAD,QAAA,EAC+wB3O,aAAa;MAAA;MAAA,IAAAS,EAAA;QAAA,IAAAoO,EAAA;QAD9xB5P,EAAE,CAAA6P,cAAA,CAAAD,EAAA,GAAF5P,EAAE,CAAA8P,WAAA,QAAArO,GAAA,CAAA8H,SAAA,GAAAqG,EAAA;MAAA;IAAA;IAAAG,SAAA,WAAAC,cAAAxO,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAAFxB,EAAE,CAAAiQ,WAAA,CAAA5O,GAAA;QAAFrB,EAAE,CAAAiQ,WAAA,CAAA3O,GAAA;MAAA;MAAA,IAAAE,EAAA;QAAA,IAAAoO,EAAA;QAAF5P,EAAE,CAAA6P,cAAA,CAAAD,EAAA,GAAF5P,EAAE,CAAA8P,WAAA,QAAArO,GAAA,CAAA+H,gBAAA,GAAAoG,EAAA,CAAAM,KAAA;QAAFlQ,EAAE,CAAA6P,cAAA,CAAAD,EAAA,GAAF5P,EAAE,CAAA8P,WAAA,QAAArO,GAAA,CAAAgI,gBAAA,GAAAmG,EAAA,CAAAM,KAAA;MAAA;IAAA;IAAAC,SAAA;IAAAC,MAAA;MAAAlK,OAAA;MAAA5D,IAAA;MAAA/C,KAAA;MAAAyG,UAAA;MAAA9B,YAAA;MAAAF,iBAAA;MAAA8D,MAAA;MAAAG,QAAA;MAAAE,UAAA;MAAAE,UAAA;MAAAhE,qBAAA;MAAAC,qBAAA;MAAAmE,QAAA;MAAAE,UAAA;MAAAG,OAAA;IAAA;IAAAuH,OAAA;MAAArH,aAAA;MAAAC,YAAA;MAAAC,MAAA;MAAAC,YAAA;MAAAC,MAAA;MAAAC,gBAAA;MAAAC,eAAA;IAAA;IAAAgH,QAAA,GAAFtQ,EAAE,CAAAuQ,kBAAA,CACmsB,CAACnK,sBAAsB,CAAC;IAAAoK,kBAAA,EAAArK,GAAA;IAAAsK,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAnF,QAAA,WAAAoF,iBAAApP,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAD7tBxB,EAAE,CAAA6Q,eAAA;QAAF7Q,EAAE,CAAA4D,UAAA,IAAA8B,sBAAA,iBAwClF,CAAC;MAAA;MAAA,IAAAlE,EAAA;QAxC+ExB,EAAE,CAAAiE,UAAA,SAAAxC,GAAA,CAAA4F,YAGjE,CAAC;MAAA;IAAA;IAAAyJ,YAAA,GAsCyhClR,EAAE,CAACmR,OAAO,EAAoFnR,EAAE,CAACoR,IAAI,EAA6FpR,EAAE,CAACqR,gBAAgB,EAAoJrR,EAAE,CAACsR,OAAO;IAAAC,MAAA;IAAAC,aAAA;IAAAC,IAAA;MAAA/R,SAAA,EAA6D,CAACG,OAAO,CAAC,yBAAyB,EAAE,CAACC,UAAU,CAAC,QAAQ,EAAE,CAACC,YAAY,CAAC8G,2BAA2B,CAAC,CAAC,CAAC,EAAE/G,UAAU,CAAC,QAAQ,EAAE,CAACC,YAAY,CAACgH,2BAA2B,CAAC,CAAC,CAAC,CAAC,CAAC;IAAC;IAAA2K,eAAA;EAAA;AACppD;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KA3C6FvR,EAAE,CAAAwR,iBAAA,CA2CJjL,OAAO,EAAc,CAAC;IACrGsH,IAAI,EAAEzN,SAAS;IACfqR,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,WAAW;MAAElG,QAAQ,EAAG;AACvD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;MAAEmG,UAAU,EAAE,CAAClS,OAAO,CAAC,yBAAyB,EAAE,CAACC,UAAU,CAAC,QAAQ,EAAE,CAACC,YAAY,CAAC8G,2BAA2B,CAAC,CAAC,CAAC,EAAE/G,UAAU,CAAC,QAAQ,EAAE,CAACC,YAAY,CAACgH,2BAA2B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAAE2K,eAAe,EAAEjR,uBAAuB,CAACuR,MAAM;MAAER,aAAa,EAAE9Q,iBAAiB,CAACuR,IAAI;MAAEC,SAAS,EAAE,CAAC1L,sBAAsB,CAAC;MAAE2L,IAAI,EAAE;QAC5SC,KAAK,EAAE;MACX,CAAC;MAAEb,MAAM,EAAE,CAAC,2+BAA2+B;IAAE,CAAC;EACtgC,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEtD,IAAI,EAAEoE,QAAQ;MAAEC,UAAU,EAAE,CAAC;QAC7DrE,IAAI,EAAEtN,MAAM;QACZkR,IAAI,EAAE,CAAC3R,QAAQ;MACnB,CAAC;IAAE,CAAC,EAAE;MAAE+N,IAAI,EAAE7F,SAAS;MAAEkK,UAAU,EAAE,CAAC;QAClCrE,IAAI,EAAEtN,MAAM;QACZkR,IAAI,EAAE,CAACtR,WAAW;MACtB,CAAC;IAAE,CAAC,EAAE;MAAE0N,IAAI,EAAE7N,EAAE,CAAC+O;IAAW,CAAC,EAAE;MAAElB,IAAI,EAAE7N,EAAE,CAACgP;IAAU,CAAC,EAAE;MAAEnB,IAAI,EAAE/M,EAAE,CAACmO;IAAc,CAAC,EAAE;MAAEpB,IAAI,EAAE/M,EAAE,CAACoO;IAAe,CAAC,EAAE;MAAErB,IAAI,EAAE7N,EAAE,CAACmP;IAAkB,CAAC,EAAE;MAAEtB,IAAI,EAAE7N,EAAE,CAACoP;IAAO,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAElJ,OAAO,EAAE,CAAC;MACrM2H,IAAI,EAAErN;IACV,CAAC,CAAC;IAAE8B,IAAI,EAAE,CAAC;MACPuL,IAAI,EAAErN;IACV,CAAC,CAAC;IAAEjB,KAAK,EAAE,CAAC;MACRsO,IAAI,EAAErN;IACV,CAAC,CAAC;IAAEwF,UAAU,EAAE,CAAC;MACb6H,IAAI,EAAErN;IACV,CAAC,CAAC;IAAE0D,YAAY,EAAE,CAAC;MACf2J,IAAI,EAAErN;IACV,CAAC,CAAC;IAAEwD,iBAAiB,EAAE,CAAC;MACpB6J,IAAI,EAAErN;IACV,CAAC,CAAC;IAAEsH,MAAM,EAAE,CAAC;MACT+F,IAAI,EAAErN;IACV,CAAC,CAAC;IAAEyH,QAAQ,EAAE,CAAC;MACX4F,IAAI,EAAErN;IACV,CAAC,CAAC;IAAE2H,UAAU,EAAE,CAAC;MACb0F,IAAI,EAAErN;IACV,CAAC,CAAC;IAAE6H,UAAU,EAAE,CAAC;MACbwF,IAAI,EAAErN;IACV,CAAC,CAAC;IAAE6D,qBAAqB,EAAE,CAAC;MACxBwJ,IAAI,EAAErN;IACV,CAAC,CAAC;IAAE8D,qBAAqB,EAAE,CAAC;MACxBuJ,IAAI,EAAErN;IACV,CAAC,CAAC;IAAEiI,QAAQ,EAAE,CAAC;MACXoF,IAAI,EAAErN;IACV,CAAC,CAAC;IAAEmI,UAAU,EAAE,CAAC;MACbkF,IAAI,EAAErN;IACV,CAAC,CAAC;IAAEsI,OAAO,EAAE,CAAC;MACV+E,IAAI,EAAErN;IACV,CAAC,CAAC;IAAEwI,aAAa,EAAE,CAAC;MAChB6E,IAAI,EAAEpN;IACV,CAAC,CAAC;IAAEwI,YAAY,EAAE,CAAC;MACf4E,IAAI,EAAEpN;IACV,CAAC,CAAC;IAAEyI,MAAM,EAAE,CAAC;MACT2E,IAAI,EAAEpN;IACV,CAAC,CAAC;IAAE0I,YAAY,EAAE,CAAC;MACf0E,IAAI,EAAEpN;IACV,CAAC,CAAC;IAAE2I,MAAM,EAAE,CAAC;MACTyE,IAAI,EAAEpN;IACV,CAAC,CAAC;IAAE4I,gBAAgB,EAAE,CAAC;MACnBwE,IAAI,EAAEpN;IACV,CAAC,CAAC;IAAE6I,eAAe,EAAE,CAAC;MAClBuE,IAAI,EAAEpN;IACV,CAAC,CAAC;IAAE8I,SAAS,EAAE,CAAC;MACZsE,IAAI,EAAEnN,eAAe;MACrB+Q,IAAI,EAAE,CAAC1Q,aAAa;IACxB,CAAC,CAAC;IAAEyI,gBAAgB,EAAE,CAAC;MACnBqE,IAAI,EAAElN,SAAS;MACf8Q,IAAI,EAAE,CAAC,SAAS;IACpB,CAAC,CAAC;IAAEhI,gBAAgB,EAAE,CAAC;MACnBoE,IAAI,EAAElN,SAAS;MACf8Q,IAAI,EAAE,CAAC,SAAS;IACpB,CAAC;EAAE,CAAC;AAAA;AAChB,MAAMU,aAAa,CAAC;EAChB,OAAOxD,IAAI,YAAAyD,sBAAAvD,CAAA;IAAA,YAAAA,CAAA,IAAwFsD,aAAa;EAAA;EAChH,OAAOE,IAAI,kBAtJ8ErS,EAAE,CAAAsS,gBAAA;IAAAzE,IAAA,EAsJSsE;EAAa;EACjH,OAAOI,IAAI,kBAvJ8EvS,EAAE,CAAAwS,gBAAA;IAAAC,OAAA,GAuJkC1S,YAAY,EAAEiB,YAAY,EAAEA,YAAY;EAAA;AACzK;AACA;EAAA,QAAAuQ,SAAA,oBAAAA,SAAA,KAzJ6FvR,EAAE,CAAAwR,iBAAA,CAyJJW,aAAa,EAAc,CAAC;IAC3GtE,IAAI,EAAEjN,QAAQ;IACd6Q,IAAI,EAAE,CAAC;MACCgB,OAAO,EAAE,CAAC1S,YAAY,EAAEiB,YAAY,CAAC;MACrC0R,OAAO,EAAE,CAACnM,OAAO,EAAEvF,YAAY,CAAC;MAChC2R,YAAY,EAAE,CAACpM,OAAO;IAC1B,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA,SAASH,sBAAsB,EAAEG,OAAO,EAAE4L,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}