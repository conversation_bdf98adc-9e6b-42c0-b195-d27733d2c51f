/*!
 * (C) Ionic http://ionicframework.com - MIT License
 */
import { expect } from "@playwright/test";
import { configs, test } from "../../../../utils/test/playwright/index";
configs({ directions: ['ltr'] }).forEach(({ title, screenshot, config }) => {
    test.describe(title('textarea: states'), () => {
        test('should render readonly textarea correctly', async ({ page }) => {
            await page.setContent(`
        <ion-textarea label="Email" value="<EMAIL>" readonly="true"></ion-textarea>
      `, config);
            const textarea = page.locator('ion-textarea');
            await expect(textarea).toHaveScreenshot(screenshot(`textarea-readonly`));
        });
        test('should render disabled textarea correctly', async ({ page }) => {
            await page.setContent(`
        <ion-textarea label="Email" value="<EMAIL>" disabled="true"></ion-textarea>
      `, config);
            const textarea = page.locator('ion-textarea');
            await expect(textarea).toHaveScreenshot(screenshot(`textarea-disabled`));
        });
    });
});
