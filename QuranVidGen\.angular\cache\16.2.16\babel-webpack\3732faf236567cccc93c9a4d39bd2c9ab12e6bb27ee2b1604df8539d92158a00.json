{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nfunction VideosDialogComponent_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 5);\n    i0.ɵɵlistener(\"click\", function VideosDialogComponent_ng_container_1_Template_div_click_1_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r4);\n      const number_r1 = restoredCtx.$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.pickVideo(number_r1));\n    })(\"mouseover\", function VideosDialogComponent_ng_container_1_Template_div_mouseover_1_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const _r2 = i0.ɵɵreference(3);\n      return i0.ɵɵresetView(_r2.play());\n    })(\"mouseleave\", function VideosDialogComponent_ng_container_1_Template_div_mouseleave_1_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const _r2 = i0.ɵɵreference(3);\n      return i0.ɵɵresetView(_r2.pause());\n    });\n    i0.ɵɵelement(2, \"video\", 6, 7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const number_r1 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", \"assets/videos/\" + number_r1 + \".mp4\", i0.ɵɵsanitizeUrl);\n  }\n}\nexport class VideosDialogComponent {\n  constructor() {\n    this.videoNumbers = Array.from({\n      length: 15\n    }, (_, i) => i + 1);\n    this.pickedVideo = new EventEmitter();\n  }\n  pickVideo(number) {\n    this.pickedVideo.emit(number);\n  }\n  static {\n    this.ɵfac = function VideosDialogComponent_Factory(t) {\n      return new (t || VideosDialogComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: VideosDialogComponent,\n      selectors: [[\"app-videos-dialog\"]],\n      outputs: {\n        pickedVideo: \"pickedVideo\"\n      },\n      decls: 5,\n      vars: 1,\n      consts: [[1, \"row\", \"w-100\", \"object-fit-contain\", \"gap-1\", \"row-gap-3\", \"px-4\", \"my-4\", \"justify-content-around\"], [4, \"ngFor\", \"ngForOf\"], [1, \"col-5\", \"col-md-3\", \"col-lg-2\", \"p-0\", \"m-0\", \"d-flex\", \"justify-content-center\", \"align-items-center\", 2, \"cursor\", \"pointer\", 3, \"click\"], [1, \"video-item\", \"bg-danger\", \"hover-effect\", \"d-flex\", \"justify-content-center\", \"align-items-center\"], [\"width\", \"80px\", \"height\", \"80px\", \"src\", \"assets/images/random.svg\"], [1, \"col-5\", \"aftero\", \"col-md-3\", \"col-lg-2\", \"p-0\", \"m-0\", \"d-flex\", \"justify-content-center\", \"align-items-center\", 2, \"cursor\", \"pointer\", 3, \"click\", \"mouseover\", \"mouseleave\"], [1, \"video-item\", 3, \"src\"], [\"vid\", \"\"]],\n      template: function VideosDialogComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵtemplate(1, VideosDialogComponent_ng_container_1_Template, 4, 1, \"ng-container\", 1);\n          i0.ɵɵelementStart(2, \"div\", 2);\n          i0.ɵɵlistener(\"click\", function VideosDialogComponent_Template_div_click_2_listener() {\n            return ctx.pickVideo(undefined);\n          });\n          i0.ɵɵelementStart(3, \"div\", 3);\n          i0.ɵɵelement(4, \"img\", 4);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngForOf\", ctx.videoNumbers);\n        }\n      },\n      dependencies: [i1.NgForOf],\n      styles: [\".video-item[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  padding: 0;\\n  margin: 0;\\n  object-fit: cover;\\n  border-radius: 12px;\\n  position: relative;\\n  z-index: 1;\\n}\\n\\n.aftero[_ngcontent-%COMP%] {\\n  position: relative;\\n}\\n\\n.aftero[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  left: 50%;\\n  top: 50%;\\n  width: 100%;\\n  height: 100%;\\n  border-radius: 12px;\\n  transform: translate(-50%, -50%); \\n\\n  z-index: 5;\\n  display: block;\\n  background-color: rgba(0, 0, 0, 0.4);\\n  transition: all 0.2s ease-in-out;\\n}\\n\\n.aftero[_ngcontent-%COMP%]:hover::after {\\n  background-color: transparent;\\n}\\n\\n.hover-effect[_ngcontent-%COMP%]:hover   img[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_rotateOnHover 2s infinite ease-in-out;\\n}\\n\\n@keyframes _ngcontent-%COMP%_rotateOnHover {\\n  50% {\\n    transform: rotate(360deg);\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["EventEmitter", "i0", "ɵɵelementContainerStart", "ɵɵelementStart", "ɵɵlistener", "VideosDialogComponent_ng_container_1_Template_div_click_1_listener", "restoredCtx", "ɵɵrestoreView", "_r4", "number_r1", "$implicit", "ctx_r3", "ɵɵnextContext", "ɵɵresetView", "pickVideo", "VideosDialogComponent_ng_container_1_Template_div_mouseover_1_listener", "_r2", "ɵɵreference", "play", "VideosDialogComponent_ng_container_1_Template_div_mouseleave_1_listener", "pause", "ɵɵelement", "ɵɵelementEnd", "ɵɵelementContainerEnd", "ɵɵadvance", "ɵɵproperty", "ɵɵsanitizeUrl", "VideosDialogComponent", "constructor", "videoNumbers", "Array", "from", "length", "_", "i", "pickedVideo", "number", "emit", "selectors", "outputs", "decls", "vars", "consts", "template", "VideosDialogComponent_Template", "rf", "ctx", "ɵɵtemplate", "VideosDialogComponent_ng_container_1_Template", "VideosDialogComponent_Template_div_click_2_listener", "undefined"], "sources": ["C:\\Users\\<USER>\\Desktop\\تطبيق ناشر ايات قئانية\\QuranVidGen\\src\\app\\GeneratorPage\\videos-dialog\\videos-dialog.component.ts", "C:\\Users\\<USER>\\Desktop\\تطبيق ناشر ايات قئانية\\QuranVidGen\\src\\app\\GeneratorPage\\videos-dialog\\videos-dialog.component.html"], "sourcesContent": ["import { Component, EventEmitter, Output } from '@angular/core';\r\nimport { range } from 'rxjs';\r\n\r\n@Component({\r\n  selector: 'app-videos-dialog',\r\n  templateUrl: './videos-dialog.component.html',\r\n  styleUrls: ['./videos-dialog.component.scss']\r\n})\r\nexport class VideosDialogComponent {\r\n  videoNumbers: number[] = Array.from({ length: 15 }, (_, i) => i + 1);\r\n  @Output() pickedVideo = new EventEmitter<number | undefined>();\r\n\r\n  pickVideo(number:number | undefined){\r\n    this.pickedVideo.emit(number);\r\n  }\r\n}\r\n", "<div class=\"row w-100 object-fit-contain gap-1 row-gap-3 px-4 my-4 justify-content-around\">\r\n  <ng-container *ngFor=\"let number of videoNumbers\">\r\n    <div class=\"col-5 aftero col-md-3 col-lg-2 p-0 m-0 d-flex justify-content-center align-items-center\" style=\"cursor: pointer;\" (click)=\"pickVideo(number)\" (mouseover)=\"vid.play()\" (mouseleave)=\"vid.pause()\">\r\n      <video #vid class=\"video-item\" [src]=\"'assets/videos/' + number + '.mp4'\" >\r\n      </video>\r\n    </div>\r\n  </ng-container>\r\n  <div class=\"col-5 col-md-3 col-lg-2 p-0 m-0 d-flex justify-content-center align-items-center\" style=\"cursor: pointer;\" (click)=\"pickVideo(undefined)\">\r\n    <div class=\"video-item bg-danger hover-effect d-flex justify-content-center align-items-center\">\r\n      <img width=\"80px\" height=\"80px\"  src=\"assets/images/random.svg\"/>\r\n    </div>\r\n  </div>\r\n\r\n</div>\r\n"], "mappings": "AAAA,SAAoBA,YAAY,QAAgB,eAAe;;;;;;ICC7DC,EAAA,CAAAC,uBAAA,GAAkD;IAChDD,EAAA,CAAAE,cAAA,aAA8M;IAAhFF,EAAA,CAAAG,UAAA,mBAAAC,mEAAA;MAAA,MAAAC,WAAA,GAAAL,EAAA,CAAAM,aAAA,CAAAC,GAAA;MAAA,MAAAC,SAAA,GAAAH,WAAA,CAAAI,SAAA;MAAA,MAAAC,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAASX,EAAA,CAAAY,WAAA,CAAAF,MAAA,CAAAG,SAAA,CAAAL,SAAA,CAAiB;IAAA,EAAC,uBAAAM,uEAAA;MAAAd,EAAA,CAAAM,aAAA,CAAAC,GAAA;MAAA,MAAAQ,GAAA,GAAAf,EAAA,CAAAgB,WAAA;MAAA,OAAchB,EAAA,CAAAY,WAAA,CAAAG,GAAA,CAAAE,IAAA,EAAU;IAAA,EAAxB,wBAAAC,wEAAA;MAAAlB,EAAA,CAAAM,aAAA,CAAAC,GAAA;MAAA,MAAAQ,GAAA,GAAAf,EAAA,CAAAgB,WAAA;MAAA,OAAwChB,EAAA,CAAAY,WAAA,CAAAG,GAAA,CAAAI,KAAA,EAAW;IAAA,EAAnD;IACvJnB,EAAA,CAAAoB,SAAA,kBACQ;IACVpB,EAAA,CAAAqB,YAAA,EAAM;IACRrB,EAAA,CAAAsB,qBAAA,EAAe;;;;IAHoBtB,EAAA,CAAAuB,SAAA,GAA0C;IAA1CvB,EAAA,CAAAwB,UAAA,2BAAAhB,SAAA,WAAAR,EAAA,CAAAyB,aAAA,CAA0C;;;ADK/E,OAAM,MAAOC,qBAAqB;EALlCC,YAAA;IAME,KAAAC,YAAY,GAAaC,KAAK,CAACC,IAAI,CAAC;MAAEC,MAAM,EAAE;IAAE,CAAE,EAAE,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,GAAG,CAAC,CAAC;IAC1D,KAAAC,WAAW,GAAG,IAAInC,YAAY,EAAsB;;EAE9Dc,SAASA,CAACsB,MAAyB;IACjC,IAAI,CAACD,WAAW,CAACE,IAAI,CAACD,MAAM,CAAC;EAC/B;;;uBANWT,qBAAqB;IAAA;EAAA;;;YAArBA,qBAAqB;MAAAW,SAAA;MAAAC,OAAA;QAAAJ,WAAA;MAAA;MAAAK,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,+BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCRlC5C,EAAA,CAAAE,cAAA,aAA2F;UACzFF,EAAA,CAAA8C,UAAA,IAAAC,6CAAA,0BAKe;UACf/C,EAAA,CAAAE,cAAA,aAAsJ;UAA/BF,EAAA,CAAAG,UAAA,mBAAA6C,oDAAA;YAAA,OAASH,GAAA,CAAAhC,SAAA,CAAUoC,SAAS,CAAC;UAAA,EAAC;UACnJjD,EAAA,CAAAE,cAAA,aAAgG;UAC9FF,EAAA,CAAAoB,SAAA,aAAiE;UACnEpB,EAAA,CAAAqB,YAAA,EAAM;;;UATyBrB,EAAA,CAAAuB,SAAA,GAAe;UAAfvB,EAAA,CAAAwB,UAAA,YAAAqB,GAAA,CAAAjB,YAAA,CAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}