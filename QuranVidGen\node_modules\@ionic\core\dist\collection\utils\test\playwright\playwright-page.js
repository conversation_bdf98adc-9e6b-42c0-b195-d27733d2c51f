/*!
 * (C) Ionic http://ionicframework.com - MIT License
 */
import { test as base } from "@playwright/test";
import { PageUtils } from "../press-keys";
import { initPageEvents } from "./page/event-spy";
import { goto as goToPage, setContent, setIonViewport, spyOnEvent, waitForChanges, locator } from "./page/utils";
/**
 * Extends the base `page` test figure within Playwright.
 * @param page The page to extend.
 * @param testInfo The test info.
 * @returns The modified playwright page with extended functionality.
 */
export async function extendPageFixture(page, testInfo) {
    const originalGoto = page.goto.bind(page);
    const originalLocator = page.locator.bind(page);
    // Overridden Playwright methods
    page.goto = (url, options) => goToPage(page, url, testInfo, originalGoto, options);
    page.setContent = (html, options) => setContent(page, html, testInfo, options);
    page.locator = (selector, options) => locator(page, originalLocator, selector, options);
    // Custom Ionic methods
    page.setIonViewport = (options) => setIonViewport(page, options);
    page.waitForChanges = (timeoutMs) => waitForChanges(page, timeoutMs);
    page.spyOnEvent = (eventName) => spyOnEvent(page, eventName);
    // Custom event behavior
    await initPageEvents(page);
    return page;
}
export const test = base.extend({
    page: async ({ page }, use, testInfo) => {
        page = await extendPageFixture(page, testInfo);
        await use(page);
    },
    skip: {
        rtl: (reason = 'The functionality that is being tested is not applicable to RTL layouts.') => {
            const testInfo = base.info();
            base.skip(testInfo.project.metadata.rtl === true, reason);
        },
        browser: (browserNameOrFunction, reason = `The functionality that is being tested is not applicable to this browser.`) => {
            const browserName = base.info().project.use.browserName;
            if (typeof browserNameOrFunction === 'function') {
                base.skip(browserNameOrFunction(browserName), reason);
            }
            else {
                base.skip(browserName === browserNameOrFunction, reason);
            }
        },
        mode: (mode, reason = `The functionality that is being tested is not applicable to ${mode} mode`) => {
            base.skip(base.info().project.metadata.mode === mode, reason);
        },
    },
    pageUtils: async ({ page }, use) => {
        await use(new PageUtils({ page }));
    },
});
