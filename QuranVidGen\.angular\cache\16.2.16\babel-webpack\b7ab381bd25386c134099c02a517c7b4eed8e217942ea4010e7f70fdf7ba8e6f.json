{"ast": null, "code": "/**\n * @license Angular v16.2.12\n * (c) 2010-2022 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport * as i0 from '@angular/core';\nimport { ViewEncapsulation, Injectable, Inject, RendererFactory2, NgZone, ANIMATION_MODULE_TYPE, NgModule } from '@angular/core';\nexport { ANIMATION_MODULE_TYPE } from '@angular/core';\nimport { ɵDomRendererFactory2, BrowserModule } from '@angular/platform-browser';\nimport { AnimationBuilder, sequence, AnimationFactory } from '@angular/animations';\nimport * as i1 from '@angular/animations/browser';\nimport { ɵAnimationEngine, ɵWebAnimationsStyleNormalizer, ɵAnimationStyleNormalizer, AnimationDriver, ɵWebAnimationsDriver, ɵNoopAnimationDriver } from '@angular/animations/browser';\nimport { DOCUMENT } from '@angular/common';\nclass BrowserAnimationBuilder extends AnimationBuilder {\n  constructor(rootRenderer, doc) {\n    super();\n    this._nextAnimationId = 0;\n    const typeData = {\n      id: '0',\n      encapsulation: ViewEncapsulation.None,\n      styles: [],\n      data: {\n        animation: []\n      }\n    };\n    this._renderer = rootRenderer.createRenderer(doc.body, typeData);\n  }\n  build(animation) {\n    const id = this._nextAnimationId.toString();\n    this._nextAnimationId++;\n    const entry = Array.isArray(animation) ? sequence(animation) : animation;\n    issueAnimationCommand(this._renderer, null, id, 'register', [entry]);\n    return new BrowserAnimationFactory(id, this._renderer);\n  }\n  static {\n    this.ɵfac = function BrowserAnimationBuilder_Factory(t) {\n      return new (t || BrowserAnimationBuilder)(i0.ɵɵinject(i0.RendererFactory2), i0.ɵɵinject(DOCUMENT));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: BrowserAnimationBuilder,\n      factory: BrowserAnimationBuilder.ɵfac\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(BrowserAnimationBuilder, [{\n    type: Injectable\n  }], function () {\n    return [{\n      type: i0.RendererFactory2\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }];\n  }, null);\n})();\nclass BrowserAnimationFactory extends AnimationFactory {\n  constructor(_id, _renderer) {\n    super();\n    this._id = _id;\n    this._renderer = _renderer;\n  }\n  create(element, options) {\n    return new RendererAnimationPlayer(this._id, element, options || {}, this._renderer);\n  }\n}\nclass RendererAnimationPlayer {\n  constructor(id, element, options, _renderer) {\n    this.id = id;\n    this.element = element;\n    this._renderer = _renderer;\n    this.parentPlayer = null;\n    this._started = false;\n    this.totalTime = 0;\n    this._command('create', options);\n  }\n  _listen(eventName, callback) {\n    return this._renderer.listen(this.element, `@@${this.id}:${eventName}`, callback);\n  }\n  _command(command, ...args) {\n    return issueAnimationCommand(this._renderer, this.element, this.id, command, args);\n  }\n  onDone(fn) {\n    this._listen('done', fn);\n  }\n  onStart(fn) {\n    this._listen('start', fn);\n  }\n  onDestroy(fn) {\n    this._listen('destroy', fn);\n  }\n  init() {\n    this._command('init');\n  }\n  hasStarted() {\n    return this._started;\n  }\n  play() {\n    this._command('play');\n    this._started = true;\n  }\n  pause() {\n    this._command('pause');\n  }\n  restart() {\n    this._command('restart');\n  }\n  finish() {\n    this._command('finish');\n  }\n  destroy() {\n    this._command('destroy');\n  }\n  reset() {\n    this._command('reset');\n    this._started = false;\n  }\n  setPosition(p) {\n    this._command('setPosition', p);\n  }\n  getPosition() {\n    return this._renderer.engine.players[+this.id]?.getPosition() ?? 0;\n  }\n}\nfunction issueAnimationCommand(renderer, element, id, command, args) {\n  return renderer.setProperty(element, `@@${id}:${command}`, args);\n}\nconst ANIMATION_PREFIX = '@';\nconst DISABLE_ANIMATIONS_FLAG = '@.disabled';\nclass AnimationRendererFactory {\n  constructor(delegate, engine, _zone) {\n    this.delegate = delegate;\n    this.engine = engine;\n    this._zone = _zone;\n    this._currentId = 0;\n    this._microtaskId = 1;\n    this._animationCallbacksBuffer = [];\n    this._rendererCache = new Map();\n    this._cdRecurDepth = 0;\n    engine.onRemovalComplete = (element, delegate) => {\n      // Note: if a component element has a leave animation, and a host leave animation,\n      // the view engine will call `removeChild` for the parent\n      // component renderer as well as for the child component renderer.\n      // Therefore, we need to check if we already removed the element.\n      const parentNode = delegate?.parentNode(element);\n      if (parentNode) {\n        delegate.removeChild(parentNode, element);\n      }\n    };\n  }\n  createRenderer(hostElement, type) {\n    const EMPTY_NAMESPACE_ID = '';\n    // cache the delegates to find out which cached delegate can\n    // be used by which cached renderer\n    const delegate = this.delegate.createRenderer(hostElement, type);\n    if (!hostElement || !type || !type.data || !type.data['animation']) {\n      let renderer = this._rendererCache.get(delegate);\n      if (!renderer) {\n        // Ensure that the renderer is removed from the cache on destroy\n        // since it may contain references to detached DOM nodes.\n        const onRendererDestroy = () => this._rendererCache.delete(delegate);\n        renderer = new BaseAnimationRenderer(EMPTY_NAMESPACE_ID, delegate, this.engine, onRendererDestroy);\n        // only cache this result when the base renderer is used\n        this._rendererCache.set(delegate, renderer);\n      }\n      return renderer;\n    }\n    const componentId = type.id;\n    const namespaceId = type.id + '-' + this._currentId;\n    this._currentId++;\n    this.engine.register(namespaceId, hostElement);\n    const registerTrigger = trigger => {\n      if (Array.isArray(trigger)) {\n        trigger.forEach(registerTrigger);\n      } else {\n        this.engine.registerTrigger(componentId, namespaceId, hostElement, trigger.name, trigger);\n      }\n    };\n    const animationTriggers = type.data['animation'];\n    animationTriggers.forEach(registerTrigger);\n    return new AnimationRenderer(this, namespaceId, delegate, this.engine);\n  }\n  begin() {\n    this._cdRecurDepth++;\n    if (this.delegate.begin) {\n      this.delegate.begin();\n    }\n  }\n  _scheduleCountTask() {\n    queueMicrotask(() => {\n      this._microtaskId++;\n    });\n  }\n  /** @internal */\n  scheduleListenerCallback(count, fn, data) {\n    if (count >= 0 && count < this._microtaskId) {\n      this._zone.run(() => fn(data));\n      return;\n    }\n    if (this._animationCallbacksBuffer.length == 0) {\n      queueMicrotask(() => {\n        this._zone.run(() => {\n          this._animationCallbacksBuffer.forEach(tuple => {\n            const [fn, data] = tuple;\n            fn(data);\n          });\n          this._animationCallbacksBuffer = [];\n        });\n      });\n    }\n    this._animationCallbacksBuffer.push([fn, data]);\n  }\n  end() {\n    this._cdRecurDepth--;\n    // this is to prevent animations from running twice when an inner\n    // component does CD when a parent component instead has inserted it\n    if (this._cdRecurDepth == 0) {\n      this._zone.runOutsideAngular(() => {\n        this._scheduleCountTask();\n        this.engine.flush(this._microtaskId);\n      });\n    }\n    if (this.delegate.end) {\n      this.delegate.end();\n    }\n  }\n  whenRenderingDone() {\n    return this.engine.whenRenderingDone();\n  }\n  static {\n    this.ɵfac = function AnimationRendererFactory_Factory(t) {\n      return new (t || AnimationRendererFactory)(i0.ɵɵinject(i0.RendererFactory2), i0.ɵɵinject(i1.ɵAnimationEngine), i0.ɵɵinject(i0.NgZone));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: AnimationRendererFactory,\n      factory: AnimationRendererFactory.ɵfac\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(AnimationRendererFactory, [{\n    type: Injectable\n  }], function () {\n    return [{\n      type: i0.RendererFactory2\n    }, {\n      type: i1.ɵAnimationEngine\n    }, {\n      type: i0.NgZone\n    }];\n  }, null);\n})();\nclass BaseAnimationRenderer {\n  constructor(namespaceId, delegate, engine, _onDestroy) {\n    this.namespaceId = namespaceId;\n    this.delegate = delegate;\n    this.engine = engine;\n    this._onDestroy = _onDestroy;\n  }\n  get data() {\n    return this.delegate.data;\n  }\n  destroyNode(node) {\n    this.delegate.destroyNode?.(node);\n  }\n  destroy() {\n    this.engine.destroy(this.namespaceId, this.delegate);\n    this.engine.afterFlushAnimationsDone(() => {\n      // Call the renderer destroy method after the animations has finished as otherwise\n      // styles will be removed too early which will cause an unstyled animation.\n      queueMicrotask(() => {\n        this.delegate.destroy();\n      });\n    });\n    this._onDestroy?.();\n  }\n  createElement(name, namespace) {\n    return this.delegate.createElement(name, namespace);\n  }\n  createComment(value) {\n    return this.delegate.createComment(value);\n  }\n  createText(value) {\n    return this.delegate.createText(value);\n  }\n  appendChild(parent, newChild) {\n    this.delegate.appendChild(parent, newChild);\n    this.engine.onInsert(this.namespaceId, newChild, parent, false);\n  }\n  insertBefore(parent, newChild, refChild, isMove = true) {\n    this.delegate.insertBefore(parent, newChild, refChild);\n    // If `isMove` true than we should animate this insert.\n    this.engine.onInsert(this.namespaceId, newChild, parent, isMove);\n  }\n  removeChild(parent, oldChild, isHostElement) {\n    this.engine.onRemove(this.namespaceId, oldChild, this.delegate);\n  }\n  selectRootElement(selectorOrNode, preserveContent) {\n    return this.delegate.selectRootElement(selectorOrNode, preserveContent);\n  }\n  parentNode(node) {\n    return this.delegate.parentNode(node);\n  }\n  nextSibling(node) {\n    return this.delegate.nextSibling(node);\n  }\n  setAttribute(el, name, value, namespace) {\n    this.delegate.setAttribute(el, name, value, namespace);\n  }\n  removeAttribute(el, name, namespace) {\n    this.delegate.removeAttribute(el, name, namespace);\n  }\n  addClass(el, name) {\n    this.delegate.addClass(el, name);\n  }\n  removeClass(el, name) {\n    this.delegate.removeClass(el, name);\n  }\n  setStyle(el, style, value, flags) {\n    this.delegate.setStyle(el, style, value, flags);\n  }\n  removeStyle(el, style, flags) {\n    this.delegate.removeStyle(el, style, flags);\n  }\n  setProperty(el, name, value) {\n    if (name.charAt(0) == ANIMATION_PREFIX && name == DISABLE_ANIMATIONS_FLAG) {\n      this.disableAnimations(el, !!value);\n    } else {\n      this.delegate.setProperty(el, name, value);\n    }\n  }\n  setValue(node, value) {\n    this.delegate.setValue(node, value);\n  }\n  listen(target, eventName, callback) {\n    return this.delegate.listen(target, eventName, callback);\n  }\n  disableAnimations(element, value) {\n    this.engine.disableAnimations(element, value);\n  }\n}\nclass AnimationRenderer extends BaseAnimationRenderer {\n  constructor(factory, namespaceId, delegate, engine, onDestroy) {\n    super(namespaceId, delegate, engine, onDestroy);\n    this.factory = factory;\n    this.namespaceId = namespaceId;\n  }\n  setProperty(el, name, value) {\n    if (name.charAt(0) == ANIMATION_PREFIX) {\n      if (name.charAt(1) == '.' && name == DISABLE_ANIMATIONS_FLAG) {\n        value = value === undefined ? true : !!value;\n        this.disableAnimations(el, value);\n      } else {\n        this.engine.process(this.namespaceId, el, name.slice(1), value);\n      }\n    } else {\n      this.delegate.setProperty(el, name, value);\n    }\n  }\n  listen(target, eventName, callback) {\n    if (eventName.charAt(0) == ANIMATION_PREFIX) {\n      const element = resolveElementFromTarget(target);\n      let name = eventName.slice(1);\n      let phase = '';\n      // @listener.phase is for trigger animation callbacks\n      // @@listener is for animation builder callbacks\n      if (name.charAt(0) != ANIMATION_PREFIX) {\n        [name, phase] = parseTriggerCallbackName(name);\n      }\n      return this.engine.listen(this.namespaceId, element, name, phase, event => {\n        const countId = event['_data'] || -1;\n        this.factory.scheduleListenerCallback(countId, callback, event);\n      });\n    }\n    return this.delegate.listen(target, eventName, callback);\n  }\n}\nfunction resolveElementFromTarget(target) {\n  switch (target) {\n    case 'body':\n      return document.body;\n    case 'document':\n      return document;\n    case 'window':\n      return window;\n    default:\n      return target;\n  }\n}\nfunction parseTriggerCallbackName(triggerName) {\n  const dotIndex = triggerName.indexOf('.');\n  const trigger = triggerName.substring(0, dotIndex);\n  const phase = triggerName.slice(dotIndex + 1);\n  return [trigger, phase];\n}\nclass InjectableAnimationEngine extends ɵAnimationEngine {\n  // The `ApplicationRef` is injected here explicitly to force the dependency ordering.\n  // Since the `ApplicationRef` should be created earlier before the `AnimationEngine`, they\n  // both have `ngOnDestroy` hooks and `flush()` must be called after all views are destroyed.\n  constructor(doc, driver, normalizer, appRef) {\n    super(doc.body, driver, normalizer);\n  }\n  ngOnDestroy() {\n    this.flush();\n  }\n  static {\n    this.ɵfac = function InjectableAnimationEngine_Factory(t) {\n      return new (t || InjectableAnimationEngine)(i0.ɵɵinject(DOCUMENT), i0.ɵɵinject(i1.AnimationDriver), i0.ɵɵinject(i1.ɵAnimationStyleNormalizer), i0.ɵɵinject(i0.ApplicationRef));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: InjectableAnimationEngine,\n      factory: InjectableAnimationEngine.ɵfac\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(InjectableAnimationEngine, [{\n    type: Injectable\n  }], function () {\n    return [{\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }, {\n      type: i1.AnimationDriver\n    }, {\n      type: i1.ɵAnimationStyleNormalizer\n    }, {\n      type: i0.ApplicationRef\n    }];\n  }, null);\n})();\nfunction instantiateDefaultStyleNormalizer() {\n  return new ɵWebAnimationsStyleNormalizer();\n}\nfunction instantiateRendererFactory(renderer, engine, zone) {\n  return new AnimationRendererFactory(renderer, engine, zone);\n}\nconst SHARED_ANIMATION_PROVIDERS = [{\n  provide: AnimationBuilder,\n  useClass: BrowserAnimationBuilder\n}, {\n  provide: ɵAnimationStyleNormalizer,\n  useFactory: instantiateDefaultStyleNormalizer\n}, {\n  provide: ɵAnimationEngine,\n  useClass: InjectableAnimationEngine\n}, {\n  provide: RendererFactory2,\n  useFactory: instantiateRendererFactory,\n  deps: [ɵDomRendererFactory2, ɵAnimationEngine, NgZone]\n}];\n/**\n * Separate providers from the actual module so that we can do a local modification in Google3 to\n * include them in the BrowserModule.\n */\nconst BROWSER_ANIMATIONS_PROVIDERS = [{\n  provide: AnimationDriver,\n  useFactory: () => new ɵWebAnimationsDriver()\n}, {\n  provide: ANIMATION_MODULE_TYPE,\n  useValue: 'BrowserAnimations'\n}, ...SHARED_ANIMATION_PROVIDERS];\n/**\n * Separate providers from the actual module so that we can do a local modification in Google3 to\n * include them in the BrowserTestingModule.\n */\nconst BROWSER_NOOP_ANIMATIONS_PROVIDERS = [{\n  provide: AnimationDriver,\n  useClass: ɵNoopAnimationDriver\n}, {\n  provide: ANIMATION_MODULE_TYPE,\n  useValue: 'NoopAnimations'\n}, ...SHARED_ANIMATION_PROVIDERS];\n\n/**\n * Exports `BrowserModule` with additional [dependency-injection providers](guide/glossary#provider)\n * for use with animations. See [Animations](guide/animations).\n * @publicApi\n */\nclass BrowserAnimationsModule {\n  /**\n   * Configures the module based on the specified object.\n   *\n   * @param config Object used to configure the behavior of the `BrowserAnimationsModule`.\n   * @see {@link BrowserAnimationsModuleConfig}\n   *\n   * @usageNotes\n   * When registering the `BrowserAnimationsModule`, you can use the `withConfig`\n   * function as follows:\n   * ```\n   * @NgModule({\n   *   imports: [BrowserAnimationsModule.withConfig(config)]\n   * })\n   * class MyNgModule {}\n   * ```\n   */\n  static withConfig(config) {\n    return {\n      ngModule: BrowserAnimationsModule,\n      providers: config.disableAnimations ? BROWSER_NOOP_ANIMATIONS_PROVIDERS : BROWSER_ANIMATIONS_PROVIDERS\n    };\n  }\n  static {\n    this.ɵfac = function BrowserAnimationsModule_Factory(t) {\n      return new (t || BrowserAnimationsModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: BrowserAnimationsModule\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      providers: BROWSER_ANIMATIONS_PROVIDERS,\n      imports: [BrowserModule]\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(BrowserAnimationsModule, [{\n    type: NgModule,\n    args: [{\n      exports: [BrowserModule],\n      providers: BROWSER_ANIMATIONS_PROVIDERS\n    }]\n  }], null, null);\n})();\n/**\n * Returns the set of [dependency-injection providers](guide/glossary#provider)\n * to enable animations in an application. See [animations guide](guide/animations)\n * to learn more about animations in Angular.\n *\n * @usageNotes\n *\n * The function is useful when you want to enable animations in an application\n * bootstrapped using the `bootstrapApplication` function. In this scenario there\n * is no need to import the `BrowserAnimationsModule` NgModule at all, just add\n * providers returned by this function to the `providers` list as show below.\n *\n * ```typescript\n * bootstrapApplication(RootComponent, {\n *   providers: [\n *     provideAnimations()\n *   ]\n * });\n * ```\n *\n * @publicApi\n */\nfunction provideAnimations() {\n  // Return a copy to prevent changes to the original array in case any in-place\n  // alterations are performed to the `provideAnimations` call results in app code.\n  return [...BROWSER_ANIMATIONS_PROVIDERS];\n}\n/**\n * A null player that must be imported to allow disabling of animations.\n * @publicApi\n */\nclass NoopAnimationsModule {\n  static {\n    this.ɵfac = function NoopAnimationsModule_Factory(t) {\n      return new (t || NoopAnimationsModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: NoopAnimationsModule\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      providers: BROWSER_NOOP_ANIMATIONS_PROVIDERS,\n      imports: [BrowserModule]\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NoopAnimationsModule, [{\n    type: NgModule,\n    args: [{\n      exports: [BrowserModule],\n      providers: BROWSER_NOOP_ANIMATIONS_PROVIDERS\n    }]\n  }], null, null);\n})();\n/**\n * Returns the set of [dependency-injection providers](guide/glossary#provider)\n * to disable animations in an application. See [animations guide](guide/animations)\n * to learn more about animations in Angular.\n *\n * @usageNotes\n *\n * The function is useful when you want to bootstrap an application using\n * the `bootstrapApplication` function, but you need to disable animations\n * (for example, when running tests).\n *\n * ```typescript\n * bootstrapApplication(RootComponent, {\n *   providers: [\n *     provideNoopAnimations()\n *   ]\n * });\n * ```\n *\n * @publicApi\n */\nfunction provideNoopAnimations() {\n  // Return a copy to prevent changes to the original array in case any in-place\n  // alterations are performed to the `provideNoopAnimations` call results in app code.\n  return [...BROWSER_NOOP_ANIMATIONS_PROVIDERS];\n}\n\n/**\n * @module\n * @description\n * Entry point for all animation APIs of the animation browser package.\n */\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of this package.\n */\n\n// This file is not used to build this module. It is only used during editing\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { BrowserAnimationsModule, NoopAnimationsModule, provideAnimations, provideNoopAnimations, AnimationRenderer as ɵAnimationRenderer, AnimationRendererFactory as ɵAnimationRendererFactory, BrowserAnimationBuilder as ɵBrowserAnimationBuilder, BrowserAnimationFactory as ɵBrowserAnimationFactory, InjectableAnimationEngine as ɵInjectableAnimationEngine };", "map": {"version": 3, "names": ["i0", "ViewEncapsulation", "Injectable", "Inject", "RendererFactory2", "NgZone", "ANIMATION_MODULE_TYPE", "NgModule", "ɵDomRendererFactory2", "BrowserModule", "AnimationBuilder", "sequence", "AnimationFactory", "i1", "ɵAnimationEngine", "ɵWebAnimationsStyleNormalizer", "ɵAnimationStyleNormalizer", "AnimationDriver", "ɵWebAnimationsDriver", "ɵNoopAnimationDriver", "DOCUMENT", "BrowserAnimationBuilder", "constructor", "<PERSON><PERSON><PERSON><PERSON>", "doc", "_nextAnimationId", "typeData", "id", "encapsulation", "None", "styles", "data", "animation", "_renderer", "<PERSON><PERSON><PERSON><PERSON>", "body", "build", "toString", "entry", "Array", "isArray", "issueAnimationCommand", "BrowserAnimationFactory", "ɵfac", "BrowserAnimationBuilder_Factory", "t", "ɵɵinject", "ɵprov", "ɵɵdefineInjectable", "token", "factory", "ngDevMode", "ɵsetClassMetadata", "type", "undefined", "decorators", "args", "_id", "create", "element", "options", "RendererAnimationPlayer", "parentPlayer", "_started", "totalTime", "_command", "_listen", "eventName", "callback", "listen", "command", "onDone", "fn", "onStart", "onDestroy", "init", "hasStarted", "play", "pause", "restart", "finish", "destroy", "reset", "setPosition", "p", "getPosition", "engine", "players", "renderer", "setProperty", "ANIMATION_PREFIX", "DISABLE_ANIMATIONS_FLAG", "AnimationRendererFactory", "delegate", "_zone", "_currentId", "_microtaskId", "_animationCallbacksBuffer", "_rendererCache", "Map", "_cdRecurDepth", "onRemovalComplete", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "hostElement", "EMPTY_NAMESPACE_ID", "get", "on<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "delete", "BaseAnimationRenderer", "set", "componentId", "namespaceId", "register", "registerTrigger", "trigger", "for<PERSON>ach", "name", "animationTriggers", "<PERSON><PERSON><PERSON><PERSON>", "begin", "_scheduleCountTask", "queueMicrotask", "scheduleListenerCallback", "count", "run", "length", "tuple", "push", "end", "runOutsideAngular", "flush", "whenRenderingDone", "AnimationRendererFactory_Factory", "_onD<PERSON>roy", "destroyNode", "node", "afterFlushAnimationsDone", "createElement", "namespace", "createComment", "value", "createText", "append<PERSON><PERSON><PERSON>", "parent", "<PERSON><PERSON><PERSON><PERSON>", "onInsert", "insertBefore", "refChild", "isMove", "<PERSON><PERSON><PERSON><PERSON>", "isHostElement", "onRemove", "selectRootElement", "selectorOrNode", "preserve<PERSON><PERSON>nt", "nextS<PERSON>ling", "setAttribute", "el", "removeAttribute", "addClass", "removeClass", "setStyle", "style", "flags", "removeStyle", "char<PERSON>t", "disableAnimations", "setValue", "target", "process", "slice", "resolveElementFromTarget", "phase", "parseTriggerCallbackName", "event", "countId", "document", "window", "triggerName", "dotIndex", "indexOf", "substring", "InjectableAnimationEngine", "driver", "normalizer", "appRef", "ngOnDestroy", "InjectableAnimationEngine_Factory", "ApplicationRef", "instantiateDefaultStyleNormalizer", "instantiateRendererFactory", "zone", "SHARED_ANIMATION_PROVIDERS", "provide", "useClass", "useFactory", "deps", "BROWSER_ANIMATIONS_PROVIDERS", "useValue", "BROWSER_NOOP_ANIMATIONS_PROVIDERS", "BrowserAnimationsModule", "withConfig", "config", "ngModule", "providers", "BrowserAnimationsModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "imports", "exports", "provideAnimations", "NoopAnimationsModule", "NoopAnimationsModule_Factory", "provideNoopAnimations", "ɵAnimationRenderer", "ɵAnimationRendererFactory", "ɵBrowserAnimationBuilder", "ɵBrowserAnimationFactory", "ɵInjectableAnimationEngine"], "sources": ["C:/Users/<USER>/Desktop/تطبيق ناشر ايات قئانية/QuranVidGen/node_modules/@angular/platform-browser/fesm2022/animations.mjs"], "sourcesContent": ["/**\n * @license Angular v16.2.12\n * (c) 2010-2022 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport * as i0 from '@angular/core';\nimport { ViewEncapsulation, Injectable, Inject, RendererFactory2, NgZone, ANIMATION_MODULE_TYPE, NgModule } from '@angular/core';\nexport { ANIMATION_MODULE_TYPE } from '@angular/core';\nimport { ɵDomRendererFactory2, BrowserModule } from '@angular/platform-browser';\nimport { AnimationBuilder, sequence, AnimationFactory } from '@angular/animations';\nimport * as i1 from '@angular/animations/browser';\nimport { ɵAnimationEngine, ɵWebAnimationsStyleNormalizer, ɵAnimationStyleNormalizer, AnimationDriver, ɵWebAnimationsDriver, ɵNoopAnimationDriver } from '@angular/animations/browser';\nimport { DOCUMENT } from '@angular/common';\n\nclass BrowserAnimationBuilder extends AnimationBuilder {\n    constructor(rootRenderer, doc) {\n        super();\n        this._nextAnimationId = 0;\n        const typeData = { id: '0', encapsulation: ViewEncapsulation.None, styles: [], data: { animation: [] } };\n        this._renderer = rootRenderer.createRenderer(doc.body, typeData);\n    }\n    build(animation) {\n        const id = this._nextAnimationId.toString();\n        this._nextAnimationId++;\n        const entry = Array.isArray(animation) ? sequence(animation) : animation;\n        issueAnimationCommand(this._renderer, null, id, 'register', [entry]);\n        return new BrowserAnimationFactory(id, this._renderer);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: BrowserAnimationBuilder, deps: [{ token: i0.RendererFactory2 }, { token: DOCUMENT }], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: BrowserAnimationBuilder }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: BrowserAnimationBuilder, decorators: [{\n            type: Injectable\n        }], ctorParameters: function () { return [{ type: i0.RendererFactory2 }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }]; } });\nclass BrowserAnimationFactory extends AnimationFactory {\n    constructor(_id, _renderer) {\n        super();\n        this._id = _id;\n        this._renderer = _renderer;\n    }\n    create(element, options) {\n        return new RendererAnimationPlayer(this._id, element, options || {}, this._renderer);\n    }\n}\nclass RendererAnimationPlayer {\n    constructor(id, element, options, _renderer) {\n        this.id = id;\n        this.element = element;\n        this._renderer = _renderer;\n        this.parentPlayer = null;\n        this._started = false;\n        this.totalTime = 0;\n        this._command('create', options);\n    }\n    _listen(eventName, callback) {\n        return this._renderer.listen(this.element, `@@${this.id}:${eventName}`, callback);\n    }\n    _command(command, ...args) {\n        return issueAnimationCommand(this._renderer, this.element, this.id, command, args);\n    }\n    onDone(fn) {\n        this._listen('done', fn);\n    }\n    onStart(fn) {\n        this._listen('start', fn);\n    }\n    onDestroy(fn) {\n        this._listen('destroy', fn);\n    }\n    init() {\n        this._command('init');\n    }\n    hasStarted() {\n        return this._started;\n    }\n    play() {\n        this._command('play');\n        this._started = true;\n    }\n    pause() {\n        this._command('pause');\n    }\n    restart() {\n        this._command('restart');\n    }\n    finish() {\n        this._command('finish');\n    }\n    destroy() {\n        this._command('destroy');\n    }\n    reset() {\n        this._command('reset');\n        this._started = false;\n    }\n    setPosition(p) {\n        this._command('setPosition', p);\n    }\n    getPosition() {\n        return this._renderer.engine.players[+this.id]?.getPosition() ?? 0;\n    }\n}\nfunction issueAnimationCommand(renderer, element, id, command, args) {\n    return renderer.setProperty(element, `@@${id}:${command}`, args);\n}\n\nconst ANIMATION_PREFIX = '@';\nconst DISABLE_ANIMATIONS_FLAG = '@.disabled';\nclass AnimationRendererFactory {\n    constructor(delegate, engine, _zone) {\n        this.delegate = delegate;\n        this.engine = engine;\n        this._zone = _zone;\n        this._currentId = 0;\n        this._microtaskId = 1;\n        this._animationCallbacksBuffer = [];\n        this._rendererCache = new Map();\n        this._cdRecurDepth = 0;\n        engine.onRemovalComplete = (element, delegate) => {\n            // Note: if a component element has a leave animation, and a host leave animation,\n            // the view engine will call `removeChild` for the parent\n            // component renderer as well as for the child component renderer.\n            // Therefore, we need to check if we already removed the element.\n            const parentNode = delegate?.parentNode(element);\n            if (parentNode) {\n                delegate.removeChild(parentNode, element);\n            }\n        };\n    }\n    createRenderer(hostElement, type) {\n        const EMPTY_NAMESPACE_ID = '';\n        // cache the delegates to find out which cached delegate can\n        // be used by which cached renderer\n        const delegate = this.delegate.createRenderer(hostElement, type);\n        if (!hostElement || !type || !type.data || !type.data['animation']) {\n            let renderer = this._rendererCache.get(delegate);\n            if (!renderer) {\n                // Ensure that the renderer is removed from the cache on destroy\n                // since it may contain references to detached DOM nodes.\n                const onRendererDestroy = () => this._rendererCache.delete(delegate);\n                renderer =\n                    new BaseAnimationRenderer(EMPTY_NAMESPACE_ID, delegate, this.engine, onRendererDestroy);\n                // only cache this result when the base renderer is used\n                this._rendererCache.set(delegate, renderer);\n            }\n            return renderer;\n        }\n        const componentId = type.id;\n        const namespaceId = type.id + '-' + this._currentId;\n        this._currentId++;\n        this.engine.register(namespaceId, hostElement);\n        const registerTrigger = (trigger) => {\n            if (Array.isArray(trigger)) {\n                trigger.forEach(registerTrigger);\n            }\n            else {\n                this.engine.registerTrigger(componentId, namespaceId, hostElement, trigger.name, trigger);\n            }\n        };\n        const animationTriggers = type.data['animation'];\n        animationTriggers.forEach(registerTrigger);\n        return new AnimationRenderer(this, namespaceId, delegate, this.engine);\n    }\n    begin() {\n        this._cdRecurDepth++;\n        if (this.delegate.begin) {\n            this.delegate.begin();\n        }\n    }\n    _scheduleCountTask() {\n        queueMicrotask(() => {\n            this._microtaskId++;\n        });\n    }\n    /** @internal */\n    scheduleListenerCallback(count, fn, data) {\n        if (count >= 0 && count < this._microtaskId) {\n            this._zone.run(() => fn(data));\n            return;\n        }\n        if (this._animationCallbacksBuffer.length == 0) {\n            queueMicrotask(() => {\n                this._zone.run(() => {\n                    this._animationCallbacksBuffer.forEach(tuple => {\n                        const [fn, data] = tuple;\n                        fn(data);\n                    });\n                    this._animationCallbacksBuffer = [];\n                });\n            });\n        }\n        this._animationCallbacksBuffer.push([fn, data]);\n    }\n    end() {\n        this._cdRecurDepth--;\n        // this is to prevent animations from running twice when an inner\n        // component does CD when a parent component instead has inserted it\n        if (this._cdRecurDepth == 0) {\n            this._zone.runOutsideAngular(() => {\n                this._scheduleCountTask();\n                this.engine.flush(this._microtaskId);\n            });\n        }\n        if (this.delegate.end) {\n            this.delegate.end();\n        }\n    }\n    whenRenderingDone() {\n        return this.engine.whenRenderingDone();\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: AnimationRendererFactory, deps: [{ token: i0.RendererFactory2 }, { token: i1.ɵAnimationEngine }, { token: i0.NgZone }], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: AnimationRendererFactory }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: AnimationRendererFactory, decorators: [{\n            type: Injectable\n        }], ctorParameters: function () { return [{ type: i0.RendererFactory2 }, { type: i1.ɵAnimationEngine }, { type: i0.NgZone }]; } });\nclass BaseAnimationRenderer {\n    constructor(namespaceId, delegate, engine, _onDestroy) {\n        this.namespaceId = namespaceId;\n        this.delegate = delegate;\n        this.engine = engine;\n        this._onDestroy = _onDestroy;\n    }\n    get data() {\n        return this.delegate.data;\n    }\n    destroyNode(node) {\n        this.delegate.destroyNode?.(node);\n    }\n    destroy() {\n        this.engine.destroy(this.namespaceId, this.delegate);\n        this.engine.afterFlushAnimationsDone(() => {\n            // Call the renderer destroy method after the animations has finished as otherwise\n            // styles will be removed too early which will cause an unstyled animation.\n            queueMicrotask(() => {\n                this.delegate.destroy();\n            });\n        });\n        this._onDestroy?.();\n    }\n    createElement(name, namespace) {\n        return this.delegate.createElement(name, namespace);\n    }\n    createComment(value) {\n        return this.delegate.createComment(value);\n    }\n    createText(value) {\n        return this.delegate.createText(value);\n    }\n    appendChild(parent, newChild) {\n        this.delegate.appendChild(parent, newChild);\n        this.engine.onInsert(this.namespaceId, newChild, parent, false);\n    }\n    insertBefore(parent, newChild, refChild, isMove = true) {\n        this.delegate.insertBefore(parent, newChild, refChild);\n        // If `isMove` true than we should animate this insert.\n        this.engine.onInsert(this.namespaceId, newChild, parent, isMove);\n    }\n    removeChild(parent, oldChild, isHostElement) {\n        this.engine.onRemove(this.namespaceId, oldChild, this.delegate);\n    }\n    selectRootElement(selectorOrNode, preserveContent) {\n        return this.delegate.selectRootElement(selectorOrNode, preserveContent);\n    }\n    parentNode(node) {\n        return this.delegate.parentNode(node);\n    }\n    nextSibling(node) {\n        return this.delegate.nextSibling(node);\n    }\n    setAttribute(el, name, value, namespace) {\n        this.delegate.setAttribute(el, name, value, namespace);\n    }\n    removeAttribute(el, name, namespace) {\n        this.delegate.removeAttribute(el, name, namespace);\n    }\n    addClass(el, name) {\n        this.delegate.addClass(el, name);\n    }\n    removeClass(el, name) {\n        this.delegate.removeClass(el, name);\n    }\n    setStyle(el, style, value, flags) {\n        this.delegate.setStyle(el, style, value, flags);\n    }\n    removeStyle(el, style, flags) {\n        this.delegate.removeStyle(el, style, flags);\n    }\n    setProperty(el, name, value) {\n        if (name.charAt(0) == ANIMATION_PREFIX && name == DISABLE_ANIMATIONS_FLAG) {\n            this.disableAnimations(el, !!value);\n        }\n        else {\n            this.delegate.setProperty(el, name, value);\n        }\n    }\n    setValue(node, value) {\n        this.delegate.setValue(node, value);\n    }\n    listen(target, eventName, callback) {\n        return this.delegate.listen(target, eventName, callback);\n    }\n    disableAnimations(element, value) {\n        this.engine.disableAnimations(element, value);\n    }\n}\nclass AnimationRenderer extends BaseAnimationRenderer {\n    constructor(factory, namespaceId, delegate, engine, onDestroy) {\n        super(namespaceId, delegate, engine, onDestroy);\n        this.factory = factory;\n        this.namespaceId = namespaceId;\n    }\n    setProperty(el, name, value) {\n        if (name.charAt(0) == ANIMATION_PREFIX) {\n            if (name.charAt(1) == '.' && name == DISABLE_ANIMATIONS_FLAG) {\n                value = value === undefined ? true : !!value;\n                this.disableAnimations(el, value);\n            }\n            else {\n                this.engine.process(this.namespaceId, el, name.slice(1), value);\n            }\n        }\n        else {\n            this.delegate.setProperty(el, name, value);\n        }\n    }\n    listen(target, eventName, callback) {\n        if (eventName.charAt(0) == ANIMATION_PREFIX) {\n            const element = resolveElementFromTarget(target);\n            let name = eventName.slice(1);\n            let phase = '';\n            // @listener.phase is for trigger animation callbacks\n            // @@listener is for animation builder callbacks\n            if (name.charAt(0) != ANIMATION_PREFIX) {\n                [name, phase] = parseTriggerCallbackName(name);\n            }\n            return this.engine.listen(this.namespaceId, element, name, phase, event => {\n                const countId = event['_data'] || -1;\n                this.factory.scheduleListenerCallback(countId, callback, event);\n            });\n        }\n        return this.delegate.listen(target, eventName, callback);\n    }\n}\nfunction resolveElementFromTarget(target) {\n    switch (target) {\n        case 'body':\n            return document.body;\n        case 'document':\n            return document;\n        case 'window':\n            return window;\n        default:\n            return target;\n    }\n}\nfunction parseTriggerCallbackName(triggerName) {\n    const dotIndex = triggerName.indexOf('.');\n    const trigger = triggerName.substring(0, dotIndex);\n    const phase = triggerName.slice(dotIndex + 1);\n    return [trigger, phase];\n}\n\nclass InjectableAnimationEngine extends ɵAnimationEngine {\n    // The `ApplicationRef` is injected here explicitly to force the dependency ordering.\n    // Since the `ApplicationRef` should be created earlier before the `AnimationEngine`, they\n    // both have `ngOnDestroy` hooks and `flush()` must be called after all views are destroyed.\n    constructor(doc, driver, normalizer, appRef) {\n        super(doc.body, driver, normalizer);\n    }\n    ngOnDestroy() {\n        this.flush();\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: InjectableAnimationEngine, deps: [{ token: DOCUMENT }, { token: i1.AnimationDriver }, { token: i1.ɵAnimationStyleNormalizer }, { token: i0.ApplicationRef }], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: InjectableAnimationEngine }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: InjectableAnimationEngine, decorators: [{\n            type: Injectable\n        }], ctorParameters: function () { return [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }, { type: i1.AnimationDriver }, { type: i1.ɵAnimationStyleNormalizer }, { type: i0.ApplicationRef }]; } });\nfunction instantiateDefaultStyleNormalizer() {\n    return new ɵWebAnimationsStyleNormalizer();\n}\nfunction instantiateRendererFactory(renderer, engine, zone) {\n    return new AnimationRendererFactory(renderer, engine, zone);\n}\nconst SHARED_ANIMATION_PROVIDERS = [\n    { provide: AnimationBuilder, useClass: BrowserAnimationBuilder },\n    { provide: ɵAnimationStyleNormalizer, useFactory: instantiateDefaultStyleNormalizer },\n    { provide: ɵAnimationEngine, useClass: InjectableAnimationEngine }, {\n        provide: RendererFactory2,\n        useFactory: instantiateRendererFactory,\n        deps: [ɵDomRendererFactory2, ɵAnimationEngine, NgZone]\n    }\n];\n/**\n * Separate providers from the actual module so that we can do a local modification in Google3 to\n * include them in the BrowserModule.\n */\nconst BROWSER_ANIMATIONS_PROVIDERS = [\n    { provide: AnimationDriver, useFactory: () => new ɵWebAnimationsDriver() },\n    { provide: ANIMATION_MODULE_TYPE, useValue: 'BrowserAnimations' }, ...SHARED_ANIMATION_PROVIDERS\n];\n/**\n * Separate providers from the actual module so that we can do a local modification in Google3 to\n * include them in the BrowserTestingModule.\n */\nconst BROWSER_NOOP_ANIMATIONS_PROVIDERS = [\n    { provide: AnimationDriver, useClass: ɵNoopAnimationDriver },\n    { provide: ANIMATION_MODULE_TYPE, useValue: 'NoopAnimations' }, ...SHARED_ANIMATION_PROVIDERS\n];\n\n/**\n * Exports `BrowserModule` with additional [dependency-injection providers](guide/glossary#provider)\n * for use with animations. See [Animations](guide/animations).\n * @publicApi\n */\nclass BrowserAnimationsModule {\n    /**\n     * Configures the module based on the specified object.\n     *\n     * @param config Object used to configure the behavior of the `BrowserAnimationsModule`.\n     * @see {@link BrowserAnimationsModuleConfig}\n     *\n     * @usageNotes\n     * When registering the `BrowserAnimationsModule`, you can use the `withConfig`\n     * function as follows:\n     * ```\n     * @NgModule({\n     *   imports: [BrowserAnimationsModule.withConfig(config)]\n     * })\n     * class MyNgModule {}\n     * ```\n     */\n    static withConfig(config) {\n        return {\n            ngModule: BrowserAnimationsModule,\n            providers: config.disableAnimations ? BROWSER_NOOP_ANIMATIONS_PROVIDERS :\n                BROWSER_ANIMATIONS_PROVIDERS\n        };\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: BrowserAnimationsModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"16.2.12\", ngImport: i0, type: BrowserAnimationsModule, exports: [BrowserModule] }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: BrowserAnimationsModule, providers: BROWSER_ANIMATIONS_PROVIDERS, imports: [BrowserModule] }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: BrowserAnimationsModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    exports: [BrowserModule],\n                    providers: BROWSER_ANIMATIONS_PROVIDERS,\n                }]\n        }] });\n/**\n * Returns the set of [dependency-injection providers](guide/glossary#provider)\n * to enable animations in an application. See [animations guide](guide/animations)\n * to learn more about animations in Angular.\n *\n * @usageNotes\n *\n * The function is useful when you want to enable animations in an application\n * bootstrapped using the `bootstrapApplication` function. In this scenario there\n * is no need to import the `BrowserAnimationsModule` NgModule at all, just add\n * providers returned by this function to the `providers` list as show below.\n *\n * ```typescript\n * bootstrapApplication(RootComponent, {\n *   providers: [\n *     provideAnimations()\n *   ]\n * });\n * ```\n *\n * @publicApi\n */\nfunction provideAnimations() {\n    // Return a copy to prevent changes to the original array in case any in-place\n    // alterations are performed to the `provideAnimations` call results in app code.\n    return [...BROWSER_ANIMATIONS_PROVIDERS];\n}\n/**\n * A null player that must be imported to allow disabling of animations.\n * @publicApi\n */\nclass NoopAnimationsModule {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: NoopAnimationsModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"16.2.12\", ngImport: i0, type: NoopAnimationsModule, exports: [BrowserModule] }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: NoopAnimationsModule, providers: BROWSER_NOOP_ANIMATIONS_PROVIDERS, imports: [BrowserModule] }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: NoopAnimationsModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    exports: [BrowserModule],\n                    providers: BROWSER_NOOP_ANIMATIONS_PROVIDERS,\n                }]\n        }] });\n/**\n * Returns the set of [dependency-injection providers](guide/glossary#provider)\n * to disable animations in an application. See [animations guide](guide/animations)\n * to learn more about animations in Angular.\n *\n * @usageNotes\n *\n * The function is useful when you want to bootstrap an application using\n * the `bootstrapApplication` function, but you need to disable animations\n * (for example, when running tests).\n *\n * ```typescript\n * bootstrapApplication(RootComponent, {\n *   providers: [\n *     provideNoopAnimations()\n *   ]\n * });\n * ```\n *\n * @publicApi\n */\nfunction provideNoopAnimations() {\n    // Return a copy to prevent changes to the original array in case any in-place\n    // alterations are performed to the `provideNoopAnimations` call results in app code.\n    return [...BROWSER_NOOP_ANIMATIONS_PROVIDERS];\n}\n\n/**\n * @module\n * @description\n * Entry point for all animation APIs of the animation browser package.\n */\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of this package.\n */\n\n// This file is not used to build this module. It is only used during editing\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { BrowserAnimationsModule, NoopAnimationsModule, provideAnimations, provideNoopAnimations, AnimationRenderer as ɵAnimationRenderer, AnimationRendererFactory as ɵAnimationRendererFactory, BrowserAnimationBuilder as ɵBrowserAnimationBuilder, BrowserAnimationFactory as ɵBrowserAnimationFactory, InjectableAnimationEngine as ɵInjectableAnimationEngine };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,iBAAiB,EAAEC,UAAU,EAAEC,MAAM,EAAEC,gBAAgB,EAAEC,MAAM,EAAEC,qBAAqB,EAAEC,QAAQ,QAAQ,eAAe;AAChI,SAASD,qBAAqB,QAAQ,eAAe;AACrD,SAASE,oBAAoB,EAAEC,aAAa,QAAQ,2BAA2B;AAC/E,SAASC,gBAAgB,EAAEC,QAAQ,EAAEC,gBAAgB,QAAQ,qBAAqB;AAClF,OAAO,KAAKC,EAAE,MAAM,6BAA6B;AACjD,SAASC,gBAAgB,EAAEC,6BAA6B,EAAEC,yBAAyB,EAAEC,eAAe,EAAEC,oBAAoB,EAAEC,oBAAoB,QAAQ,6BAA6B;AACrL,SAASC,QAAQ,QAAQ,iBAAiB;AAE1C,MAAMC,uBAAuB,SAASX,gBAAgB,CAAC;EACnDY,WAAWA,CAACC,YAAY,EAAEC,GAAG,EAAE;IAC3B,KAAK,CAAC,CAAC;IACP,IAAI,CAACC,gBAAgB,GAAG,CAAC;IACzB,MAAMC,QAAQ,GAAG;MAAEC,EAAE,EAAE,GAAG;MAAEC,aAAa,EAAE3B,iBAAiB,CAAC4B,IAAI;MAAEC,MAAM,EAAE,EAAE;MAAEC,IAAI,EAAE;QAAEC,SAAS,EAAE;MAAG;IAAE,CAAC;IACxG,IAAI,CAACC,SAAS,GAAGV,YAAY,CAACW,cAAc,CAACV,GAAG,CAACW,IAAI,EAAET,QAAQ,CAAC;EACpE;EACAU,KAAKA,CAACJ,SAAS,EAAE;IACb,MAAML,EAAE,GAAG,IAAI,CAACF,gBAAgB,CAACY,QAAQ,CAAC,CAAC;IAC3C,IAAI,CAACZ,gBAAgB,EAAE;IACvB,MAAMa,KAAK,GAAGC,KAAK,CAACC,OAAO,CAACR,SAAS,CAAC,GAAGrB,QAAQ,CAACqB,SAAS,CAAC,GAAGA,SAAS;IACxES,qBAAqB,CAAC,IAAI,CAACR,SAAS,EAAE,IAAI,EAAEN,EAAE,EAAE,UAAU,EAAE,CAACW,KAAK,CAAC,CAAC;IACpE,OAAO,IAAII,uBAAuB,CAACf,EAAE,EAAE,IAAI,CAACM,SAAS,CAAC;EAC1D;EACA;IAAS,IAAI,CAACU,IAAI,YAAAC,gCAAAC,CAAA;MAAA,YAAAA,CAAA,IAAyFxB,uBAAuB,EAAjCrB,EAAE,CAAA8C,QAAA,CAAiD9C,EAAE,CAACI,gBAAgB,GAAtEJ,EAAE,CAAA8C,QAAA,CAAiF1B,QAAQ;IAAA,CAA6C;EAAE;EAC3O;IAAS,IAAI,CAAC2B,KAAK,kBAD8E/C,EAAE,CAAAgD,kBAAA;MAAAC,KAAA,EACY5B,uBAAuB;MAAA6B,OAAA,EAAvB7B,uBAAuB,CAAAsB;IAAA,EAAG;EAAE;AAC/I;AACA;EAAA,QAAAQ,SAAA,oBAAAA,SAAA,KAHqGnD,EAAE,CAAAoD,iBAAA,CAGX/B,uBAAuB,EAAc,CAAC;IACtHgC,IAAI,EAAEnD;EACV,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEmD,IAAI,EAAErD,EAAE,CAACI;IAAiB,CAAC,EAAE;MAAEiD,IAAI,EAAEC,SAAS;MAAEC,UAAU,EAAE,CAAC;QAC7FF,IAAI,EAAElD,MAAM;QACZqD,IAAI,EAAE,CAACpC,QAAQ;MACnB,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC;AAAA;AACxB,MAAMsB,uBAAuB,SAAS9B,gBAAgB,CAAC;EACnDU,WAAWA,CAACmC,GAAG,EAAExB,SAAS,EAAE;IACxB,KAAK,CAAC,CAAC;IACP,IAAI,CAACwB,GAAG,GAAGA,GAAG;IACd,IAAI,CAACxB,SAAS,GAAGA,SAAS;EAC9B;EACAyB,MAAMA,CAACC,OAAO,EAAEC,OAAO,EAAE;IACrB,OAAO,IAAIC,uBAAuB,CAAC,IAAI,CAACJ,GAAG,EAAEE,OAAO,EAAEC,OAAO,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC3B,SAAS,CAAC;EACxF;AACJ;AACA,MAAM4B,uBAAuB,CAAC;EAC1BvC,WAAWA,CAACK,EAAE,EAAEgC,OAAO,EAAEC,OAAO,EAAE3B,SAAS,EAAE;IACzC,IAAI,CAACN,EAAE,GAAGA,EAAE;IACZ,IAAI,CAACgC,OAAO,GAAGA,OAAO;IACtB,IAAI,CAAC1B,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAAC6B,YAAY,GAAG,IAAI;IACxB,IAAI,CAACC,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACC,SAAS,GAAG,CAAC;IAClB,IAAI,CAACC,QAAQ,CAAC,QAAQ,EAAEL,OAAO,CAAC;EACpC;EACAM,OAAOA,CAACC,SAAS,EAAEC,QAAQ,EAAE;IACzB,OAAO,IAAI,CAACnC,SAAS,CAACoC,MAAM,CAAC,IAAI,CAACV,OAAO,EAAG,KAAI,IAAI,CAAChC,EAAG,IAAGwC,SAAU,EAAC,EAAEC,QAAQ,CAAC;EACrF;EACAH,QAAQA,CAACK,OAAO,EAAE,GAAGd,IAAI,EAAE;IACvB,OAAOf,qBAAqB,CAAC,IAAI,CAACR,SAAS,EAAE,IAAI,CAAC0B,OAAO,EAAE,IAAI,CAAChC,EAAE,EAAE2C,OAAO,EAAEd,IAAI,CAAC;EACtF;EACAe,MAAMA,CAACC,EAAE,EAAE;IACP,IAAI,CAACN,OAAO,CAAC,MAAM,EAAEM,EAAE,CAAC;EAC5B;EACAC,OAAOA,CAACD,EAAE,EAAE;IACR,IAAI,CAACN,OAAO,CAAC,OAAO,EAAEM,EAAE,CAAC;EAC7B;EACAE,SAASA,CAACF,EAAE,EAAE;IACV,IAAI,CAACN,OAAO,CAAC,SAAS,EAAEM,EAAE,CAAC;EAC/B;EACAG,IAAIA,CAAA,EAAG;IACH,IAAI,CAACV,QAAQ,CAAC,MAAM,CAAC;EACzB;EACAW,UAAUA,CAAA,EAAG;IACT,OAAO,IAAI,CAACb,QAAQ;EACxB;EACAc,IAAIA,CAAA,EAAG;IACH,IAAI,CAACZ,QAAQ,CAAC,MAAM,CAAC;IACrB,IAAI,CAACF,QAAQ,GAAG,IAAI;EACxB;EACAe,KAAKA,CAAA,EAAG;IACJ,IAAI,CAACb,QAAQ,CAAC,OAAO,CAAC;EAC1B;EACAc,OAAOA,CAAA,EAAG;IACN,IAAI,CAACd,QAAQ,CAAC,SAAS,CAAC;EAC5B;EACAe,MAAMA,CAAA,EAAG;IACL,IAAI,CAACf,QAAQ,CAAC,QAAQ,CAAC;EAC3B;EACAgB,OAAOA,CAAA,EAAG;IACN,IAAI,CAAChB,QAAQ,CAAC,SAAS,CAAC;EAC5B;EACAiB,KAAKA,CAAA,EAAG;IACJ,IAAI,CAACjB,QAAQ,CAAC,OAAO,CAAC;IACtB,IAAI,CAACF,QAAQ,GAAG,KAAK;EACzB;EACAoB,WAAWA,CAACC,CAAC,EAAE;IACX,IAAI,CAACnB,QAAQ,CAAC,aAAa,EAAEmB,CAAC,CAAC;EACnC;EACAC,WAAWA,CAAA,EAAG;IACV,OAAO,IAAI,CAACpD,SAAS,CAACqD,MAAM,CAACC,OAAO,CAAC,CAAC,IAAI,CAAC5D,EAAE,CAAC,EAAE0D,WAAW,CAAC,CAAC,IAAI,CAAC;EACtE;AACJ;AACA,SAAS5C,qBAAqBA,CAAC+C,QAAQ,EAAE7B,OAAO,EAAEhC,EAAE,EAAE2C,OAAO,EAAEd,IAAI,EAAE;EACjE,OAAOgC,QAAQ,CAACC,WAAW,CAAC9B,OAAO,EAAG,KAAIhC,EAAG,IAAG2C,OAAQ,EAAC,EAAEd,IAAI,CAAC;AACpE;AAEA,MAAMkC,gBAAgB,GAAG,GAAG;AAC5B,MAAMC,uBAAuB,GAAG,YAAY;AAC5C,MAAMC,wBAAwB,CAAC;EAC3BtE,WAAWA,CAACuE,QAAQ,EAAEP,MAAM,EAAEQ,KAAK,EAAE;IACjC,IAAI,CAACD,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACP,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACQ,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,UAAU,GAAG,CAAC;IACnB,IAAI,CAACC,YAAY,GAAG,CAAC;IACrB,IAAI,CAACC,yBAAyB,GAAG,EAAE;IACnC,IAAI,CAACC,cAAc,GAAG,IAAIC,GAAG,CAAC,CAAC;IAC/B,IAAI,CAACC,aAAa,GAAG,CAAC;IACtBd,MAAM,CAACe,iBAAiB,GAAG,CAAC1C,OAAO,EAAEkC,QAAQ,KAAK;MAC9C;MACA;MACA;MACA;MACA,MAAMS,UAAU,GAAGT,QAAQ,EAAES,UAAU,CAAC3C,OAAO,CAAC;MAChD,IAAI2C,UAAU,EAAE;QACZT,QAAQ,CAACU,WAAW,CAACD,UAAU,EAAE3C,OAAO,CAAC;MAC7C;IACJ,CAAC;EACL;EACAzB,cAAcA,CAACsE,WAAW,EAAEnD,IAAI,EAAE;IAC9B,MAAMoD,kBAAkB,GAAG,EAAE;IAC7B;IACA;IACA,MAAMZ,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAAC3D,cAAc,CAACsE,WAAW,EAAEnD,IAAI,CAAC;IAChE,IAAI,CAACmD,WAAW,IAAI,CAACnD,IAAI,IAAI,CAACA,IAAI,CAACtB,IAAI,IAAI,CAACsB,IAAI,CAACtB,IAAI,CAAC,WAAW,CAAC,EAAE;MAChE,IAAIyD,QAAQ,GAAG,IAAI,CAACU,cAAc,CAACQ,GAAG,CAACb,QAAQ,CAAC;MAChD,IAAI,CAACL,QAAQ,EAAE;QACX;QACA;QACA,MAAMmB,iBAAiB,GAAGA,CAAA,KAAM,IAAI,CAACT,cAAc,CAACU,MAAM,CAACf,QAAQ,CAAC;QACpEL,QAAQ,GACJ,IAAIqB,qBAAqB,CAACJ,kBAAkB,EAAEZ,QAAQ,EAAE,IAAI,CAACP,MAAM,EAAEqB,iBAAiB,CAAC;QAC3F;QACA,IAAI,CAACT,cAAc,CAACY,GAAG,CAACjB,QAAQ,EAAEL,QAAQ,CAAC;MAC/C;MACA,OAAOA,QAAQ;IACnB;IACA,MAAMuB,WAAW,GAAG1D,IAAI,CAAC1B,EAAE;IAC3B,MAAMqF,WAAW,GAAG3D,IAAI,CAAC1B,EAAE,GAAG,GAAG,GAAG,IAAI,CAACoE,UAAU;IACnD,IAAI,CAACA,UAAU,EAAE;IACjB,IAAI,CAACT,MAAM,CAAC2B,QAAQ,CAACD,WAAW,EAAER,WAAW,CAAC;IAC9C,MAAMU,eAAe,GAAIC,OAAO,IAAK;MACjC,IAAI5E,KAAK,CAACC,OAAO,CAAC2E,OAAO,CAAC,EAAE;QACxBA,OAAO,CAACC,OAAO,CAACF,eAAe,CAAC;MACpC,CAAC,MACI;QACD,IAAI,CAAC5B,MAAM,CAAC4B,eAAe,CAACH,WAAW,EAAEC,WAAW,EAAER,WAAW,EAAEW,OAAO,CAACE,IAAI,EAAEF,OAAO,CAAC;MAC7F;IACJ,CAAC;IACD,MAAMG,iBAAiB,GAAGjE,IAAI,CAACtB,IAAI,CAAC,WAAW,CAAC;IAChDuF,iBAAiB,CAACF,OAAO,CAACF,eAAe,CAAC;IAC1C,OAAO,IAAIK,iBAAiB,CAAC,IAAI,EAAEP,WAAW,EAAEnB,QAAQ,EAAE,IAAI,CAACP,MAAM,CAAC;EAC1E;EACAkC,KAAKA,CAAA,EAAG;IACJ,IAAI,CAACpB,aAAa,EAAE;IACpB,IAAI,IAAI,CAACP,QAAQ,CAAC2B,KAAK,EAAE;MACrB,IAAI,CAAC3B,QAAQ,CAAC2B,KAAK,CAAC,CAAC;IACzB;EACJ;EACAC,kBAAkBA,CAAA,EAAG;IACjBC,cAAc,CAAC,MAAM;MACjB,IAAI,CAAC1B,YAAY,EAAE;IACvB,CAAC,CAAC;EACN;EACA;EACA2B,wBAAwBA,CAACC,KAAK,EAAEpD,EAAE,EAAEzC,IAAI,EAAE;IACtC,IAAI6F,KAAK,IAAI,CAAC,IAAIA,KAAK,GAAG,IAAI,CAAC5B,YAAY,EAAE;MACzC,IAAI,CAACF,KAAK,CAAC+B,GAAG,CAAC,MAAMrD,EAAE,CAACzC,IAAI,CAAC,CAAC;MAC9B;IACJ;IACA,IAAI,IAAI,CAACkE,yBAAyB,CAAC6B,MAAM,IAAI,CAAC,EAAE;MAC5CJ,cAAc,CAAC,MAAM;QACjB,IAAI,CAAC5B,KAAK,CAAC+B,GAAG,CAAC,MAAM;UACjB,IAAI,CAAC5B,yBAAyB,CAACmB,OAAO,CAACW,KAAK,IAAI;YAC5C,MAAM,CAACvD,EAAE,EAAEzC,IAAI,CAAC,GAAGgG,KAAK;YACxBvD,EAAE,CAACzC,IAAI,CAAC;UACZ,CAAC,CAAC;UACF,IAAI,CAACkE,yBAAyB,GAAG,EAAE;QACvC,CAAC,CAAC;MACN,CAAC,CAAC;IACN;IACA,IAAI,CAACA,yBAAyB,CAAC+B,IAAI,CAAC,CAACxD,EAAE,EAAEzC,IAAI,CAAC,CAAC;EACnD;EACAkG,GAAGA,CAAA,EAAG;IACF,IAAI,CAAC7B,aAAa,EAAE;IACpB;IACA;IACA,IAAI,IAAI,CAACA,aAAa,IAAI,CAAC,EAAE;MACzB,IAAI,CAACN,KAAK,CAACoC,iBAAiB,CAAC,MAAM;QAC/B,IAAI,CAACT,kBAAkB,CAAC,CAAC;QACzB,IAAI,CAACnC,MAAM,CAAC6C,KAAK,CAAC,IAAI,CAACnC,YAAY,CAAC;MACxC,CAAC,CAAC;IACN;IACA,IAAI,IAAI,CAACH,QAAQ,CAACoC,GAAG,EAAE;MACnB,IAAI,CAACpC,QAAQ,CAACoC,GAAG,CAAC,CAAC;IACvB;EACJ;EACAG,iBAAiBA,CAAA,EAAG;IAChB,OAAO,IAAI,CAAC9C,MAAM,CAAC8C,iBAAiB,CAAC,CAAC;EAC1C;EACA;IAAS,IAAI,CAACzF,IAAI,YAAA0F,iCAAAxF,CAAA;MAAA,YAAAA,CAAA,IAAyF+C,wBAAwB,EAzLlC5F,EAAE,CAAA8C,QAAA,CAyLkD9C,EAAE,CAACI,gBAAgB,GAzLvEJ,EAAE,CAAA8C,QAAA,CAyLkFjC,EAAE,CAACC,gBAAgB,GAzLvGd,EAAE,CAAA8C,QAAA,CAyLkH9C,EAAE,CAACK,MAAM;IAAA,CAA6C;EAAE;EAC7Q;IAAS,IAAI,CAAC0C,KAAK,kBA1L8E/C,EAAE,CAAAgD,kBAAA;MAAAC,KAAA,EA0LY2C,wBAAwB;MAAA1C,OAAA,EAAxB0C,wBAAwB,CAAAjD;IAAA,EAAG;EAAE;AAChJ;AACA;EAAA,QAAAQ,SAAA,oBAAAA,SAAA,KA5LqGnD,EAAE,CAAAoD,iBAAA,CA4LXwC,wBAAwB,EAAc,CAAC;IACvHvC,IAAI,EAAEnD;EACV,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEmD,IAAI,EAAErD,EAAE,CAACI;IAAiB,CAAC,EAAE;MAAEiD,IAAI,EAAExC,EAAE,CAACC;IAAiB,CAAC,EAAE;MAAEuC,IAAI,EAAErD,EAAE,CAACK;IAAO,CAAC,CAAC;EAAE,CAAC;AAAA;AACvI,MAAMwG,qBAAqB,CAAC;EACxBvF,WAAWA,CAAC0F,WAAW,EAAEnB,QAAQ,EAAEP,MAAM,EAAEgD,UAAU,EAAE;IACnD,IAAI,CAACtB,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACnB,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACP,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACgD,UAAU,GAAGA,UAAU;EAChC;EACA,IAAIvG,IAAIA,CAAA,EAAG;IACP,OAAO,IAAI,CAAC8D,QAAQ,CAAC9D,IAAI;EAC7B;EACAwG,WAAWA,CAACC,IAAI,EAAE;IACd,IAAI,CAAC3C,QAAQ,CAAC0C,WAAW,GAAGC,IAAI,CAAC;EACrC;EACAvD,OAAOA,CAAA,EAAG;IACN,IAAI,CAACK,MAAM,CAACL,OAAO,CAAC,IAAI,CAAC+B,WAAW,EAAE,IAAI,CAACnB,QAAQ,CAAC;IACpD,IAAI,CAACP,MAAM,CAACmD,wBAAwB,CAAC,MAAM;MACvC;MACA;MACAf,cAAc,CAAC,MAAM;QACjB,IAAI,CAAC7B,QAAQ,CAACZ,OAAO,CAAC,CAAC;MAC3B,CAAC,CAAC;IACN,CAAC,CAAC;IACF,IAAI,CAACqD,UAAU,GAAG,CAAC;EACvB;EACAI,aAAaA,CAACrB,IAAI,EAAEsB,SAAS,EAAE;IAC3B,OAAO,IAAI,CAAC9C,QAAQ,CAAC6C,aAAa,CAACrB,IAAI,EAAEsB,SAAS,CAAC;EACvD;EACAC,aAAaA,CAACC,KAAK,EAAE;IACjB,OAAO,IAAI,CAAChD,QAAQ,CAAC+C,aAAa,CAACC,KAAK,CAAC;EAC7C;EACAC,UAAUA,CAACD,KAAK,EAAE;IACd,OAAO,IAAI,CAAChD,QAAQ,CAACiD,UAAU,CAACD,KAAK,CAAC;EAC1C;EACAE,WAAWA,CAACC,MAAM,EAAEC,QAAQ,EAAE;IAC1B,IAAI,CAACpD,QAAQ,CAACkD,WAAW,CAACC,MAAM,EAAEC,QAAQ,CAAC;IAC3C,IAAI,CAAC3D,MAAM,CAAC4D,QAAQ,CAAC,IAAI,CAAClC,WAAW,EAAEiC,QAAQ,EAAED,MAAM,EAAE,KAAK,CAAC;EACnE;EACAG,YAAYA,CAACH,MAAM,EAAEC,QAAQ,EAAEG,QAAQ,EAAEC,MAAM,GAAG,IAAI,EAAE;IACpD,IAAI,CAACxD,QAAQ,CAACsD,YAAY,CAACH,MAAM,EAAEC,QAAQ,EAAEG,QAAQ,CAAC;IACtD;IACA,IAAI,CAAC9D,MAAM,CAAC4D,QAAQ,CAAC,IAAI,CAAClC,WAAW,EAAEiC,QAAQ,EAAED,MAAM,EAAEK,MAAM,CAAC;EACpE;EACA9C,WAAWA,CAACyC,MAAM,EAAEM,QAAQ,EAAEC,aAAa,EAAE;IACzC,IAAI,CAACjE,MAAM,CAACkE,QAAQ,CAAC,IAAI,CAACxC,WAAW,EAAEsC,QAAQ,EAAE,IAAI,CAACzD,QAAQ,CAAC;EACnE;EACA4D,iBAAiBA,CAACC,cAAc,EAAEC,eAAe,EAAE;IAC/C,OAAO,IAAI,CAAC9D,QAAQ,CAAC4D,iBAAiB,CAACC,cAAc,EAAEC,eAAe,CAAC;EAC3E;EACArD,UAAUA,CAACkC,IAAI,EAAE;IACb,OAAO,IAAI,CAAC3C,QAAQ,CAACS,UAAU,CAACkC,IAAI,CAAC;EACzC;EACAoB,WAAWA,CAACpB,IAAI,EAAE;IACd,OAAO,IAAI,CAAC3C,QAAQ,CAAC+D,WAAW,CAACpB,IAAI,CAAC;EAC1C;EACAqB,YAAYA,CAACC,EAAE,EAAEzC,IAAI,EAAEwB,KAAK,EAAEF,SAAS,EAAE;IACrC,IAAI,CAAC9C,QAAQ,CAACgE,YAAY,CAACC,EAAE,EAAEzC,IAAI,EAAEwB,KAAK,EAAEF,SAAS,CAAC;EAC1D;EACAoB,eAAeA,CAACD,EAAE,EAAEzC,IAAI,EAAEsB,SAAS,EAAE;IACjC,IAAI,CAAC9C,QAAQ,CAACkE,eAAe,CAACD,EAAE,EAAEzC,IAAI,EAAEsB,SAAS,CAAC;EACtD;EACAqB,QAAQA,CAACF,EAAE,EAAEzC,IAAI,EAAE;IACf,IAAI,CAACxB,QAAQ,CAACmE,QAAQ,CAACF,EAAE,EAAEzC,IAAI,CAAC;EACpC;EACA4C,WAAWA,CAACH,EAAE,EAAEzC,IAAI,EAAE;IAClB,IAAI,CAACxB,QAAQ,CAACoE,WAAW,CAACH,EAAE,EAAEzC,IAAI,CAAC;EACvC;EACA6C,QAAQA,CAACJ,EAAE,EAAEK,KAAK,EAAEtB,KAAK,EAAEuB,KAAK,EAAE;IAC9B,IAAI,CAACvE,QAAQ,CAACqE,QAAQ,CAACJ,EAAE,EAAEK,KAAK,EAAEtB,KAAK,EAAEuB,KAAK,CAAC;EACnD;EACAC,WAAWA,CAACP,EAAE,EAAEK,KAAK,EAAEC,KAAK,EAAE;IAC1B,IAAI,CAACvE,QAAQ,CAACwE,WAAW,CAACP,EAAE,EAAEK,KAAK,EAAEC,KAAK,CAAC;EAC/C;EACA3E,WAAWA,CAACqE,EAAE,EAAEzC,IAAI,EAAEwB,KAAK,EAAE;IACzB,IAAIxB,IAAI,CAACiD,MAAM,CAAC,CAAC,CAAC,IAAI5E,gBAAgB,IAAI2B,IAAI,IAAI1B,uBAAuB,EAAE;MACvE,IAAI,CAAC4E,iBAAiB,CAACT,EAAE,EAAE,CAAC,CAACjB,KAAK,CAAC;IACvC,CAAC,MACI;MACD,IAAI,CAAChD,QAAQ,CAACJ,WAAW,CAACqE,EAAE,EAAEzC,IAAI,EAAEwB,KAAK,CAAC;IAC9C;EACJ;EACA2B,QAAQA,CAAChC,IAAI,EAAEK,KAAK,EAAE;IAClB,IAAI,CAAChD,QAAQ,CAAC2E,QAAQ,CAAChC,IAAI,EAAEK,KAAK,CAAC;EACvC;EACAxE,MAAMA,CAACoG,MAAM,EAAEtG,SAAS,EAAEC,QAAQ,EAAE;IAChC,OAAO,IAAI,CAACyB,QAAQ,CAACxB,MAAM,CAACoG,MAAM,EAAEtG,SAAS,EAAEC,QAAQ,CAAC;EAC5D;EACAmG,iBAAiBA,CAAC5G,OAAO,EAAEkF,KAAK,EAAE;IAC9B,IAAI,CAACvD,MAAM,CAACiF,iBAAiB,CAAC5G,OAAO,EAAEkF,KAAK,CAAC;EACjD;AACJ;AACA,MAAMtB,iBAAiB,SAASV,qBAAqB,CAAC;EAClDvF,WAAWA,CAAC4B,OAAO,EAAE8D,WAAW,EAAEnB,QAAQ,EAAEP,MAAM,EAAEZ,SAAS,EAAE;IAC3D,KAAK,CAACsC,WAAW,EAAEnB,QAAQ,EAAEP,MAAM,EAAEZ,SAAS,CAAC;IAC/C,IAAI,CAACxB,OAAO,GAAGA,OAAO;IACtB,IAAI,CAAC8D,WAAW,GAAGA,WAAW;EAClC;EACAvB,WAAWA,CAACqE,EAAE,EAAEzC,IAAI,EAAEwB,KAAK,EAAE;IACzB,IAAIxB,IAAI,CAACiD,MAAM,CAAC,CAAC,CAAC,IAAI5E,gBAAgB,EAAE;MACpC,IAAI2B,IAAI,CAACiD,MAAM,CAAC,CAAC,CAAC,IAAI,GAAG,IAAIjD,IAAI,IAAI1B,uBAAuB,EAAE;QAC1DkD,KAAK,GAAGA,KAAK,KAAKvF,SAAS,GAAG,IAAI,GAAG,CAAC,CAACuF,KAAK;QAC5C,IAAI,CAAC0B,iBAAiB,CAACT,EAAE,EAAEjB,KAAK,CAAC;MACrC,CAAC,MACI;QACD,IAAI,CAACvD,MAAM,CAACoF,OAAO,CAAC,IAAI,CAAC1D,WAAW,EAAE8C,EAAE,EAAEzC,IAAI,CAACsD,KAAK,CAAC,CAAC,CAAC,EAAE9B,KAAK,CAAC;MACnE;IACJ,CAAC,MACI;MACD,IAAI,CAAChD,QAAQ,CAACJ,WAAW,CAACqE,EAAE,EAAEzC,IAAI,EAAEwB,KAAK,CAAC;IAC9C;EACJ;EACAxE,MAAMA,CAACoG,MAAM,EAAEtG,SAAS,EAAEC,QAAQ,EAAE;IAChC,IAAID,SAAS,CAACmG,MAAM,CAAC,CAAC,CAAC,IAAI5E,gBAAgB,EAAE;MACzC,MAAM/B,OAAO,GAAGiH,wBAAwB,CAACH,MAAM,CAAC;MAChD,IAAIpD,IAAI,GAAGlD,SAAS,CAACwG,KAAK,CAAC,CAAC,CAAC;MAC7B,IAAIE,KAAK,GAAG,EAAE;MACd;MACA;MACA,IAAIxD,IAAI,CAACiD,MAAM,CAAC,CAAC,CAAC,IAAI5E,gBAAgB,EAAE;QACpC,CAAC2B,IAAI,EAAEwD,KAAK,CAAC,GAAGC,wBAAwB,CAACzD,IAAI,CAAC;MAClD;MACA,OAAO,IAAI,CAAC/B,MAAM,CAACjB,MAAM,CAAC,IAAI,CAAC2C,WAAW,EAAErD,OAAO,EAAE0D,IAAI,EAAEwD,KAAK,EAAEE,KAAK,IAAI;QACvE,MAAMC,OAAO,GAAGD,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QACpC,IAAI,CAAC7H,OAAO,CAACyE,wBAAwB,CAACqD,OAAO,EAAE5G,QAAQ,EAAE2G,KAAK,CAAC;MACnE,CAAC,CAAC;IACN;IACA,OAAO,IAAI,CAAClF,QAAQ,CAACxB,MAAM,CAACoG,MAAM,EAAEtG,SAAS,EAAEC,QAAQ,CAAC;EAC5D;AACJ;AACA,SAASwG,wBAAwBA,CAACH,MAAM,EAAE;EACtC,QAAQA,MAAM;IACV,KAAK,MAAM;MACP,OAAOQ,QAAQ,CAAC9I,IAAI;IACxB,KAAK,UAAU;MACX,OAAO8I,QAAQ;IACnB,KAAK,QAAQ;MACT,OAAOC,MAAM;IACjB;MACI,OAAOT,MAAM;EACrB;AACJ;AACA,SAASK,wBAAwBA,CAACK,WAAW,EAAE;EAC3C,MAAMC,QAAQ,GAAGD,WAAW,CAACE,OAAO,CAAC,GAAG,CAAC;EACzC,MAAMlE,OAAO,GAAGgE,WAAW,CAACG,SAAS,CAAC,CAAC,EAAEF,QAAQ,CAAC;EAClD,MAAMP,KAAK,GAAGM,WAAW,CAACR,KAAK,CAACS,QAAQ,GAAG,CAAC,CAAC;EAC7C,OAAO,CAACjE,OAAO,EAAE0D,KAAK,CAAC;AAC3B;AAEA,MAAMU,yBAAyB,SAASzK,gBAAgB,CAAC;EACrD;EACA;EACA;EACAQ,WAAWA,CAACE,GAAG,EAAEgK,MAAM,EAAEC,UAAU,EAAEC,MAAM,EAAE;IACzC,KAAK,CAAClK,GAAG,CAACW,IAAI,EAAEqJ,MAAM,EAAEC,UAAU,CAAC;EACvC;EACAE,WAAWA,CAAA,EAAG;IACV,IAAI,CAACxD,KAAK,CAAC,CAAC;EAChB;EACA;IAAS,IAAI,CAACxF,IAAI,YAAAiJ,kCAAA/I,CAAA;MAAA,YAAAA,CAAA,IAAyF0I,yBAAyB,EA5VnCvL,EAAE,CAAA8C,QAAA,CA4VmD1B,QAAQ,GA5V7DpB,EAAE,CAAA8C,QAAA,CA4VwEjC,EAAE,CAACI,eAAe,GA5V5FjB,EAAE,CAAA8C,QAAA,CA4VuGjC,EAAE,CAACG,yBAAyB,GA5VrIhB,EAAE,CAAA8C,QAAA,CA4VgJ9C,EAAE,CAAC6L,cAAc;IAAA,CAA6C;EAAE;EACnT;IAAS,IAAI,CAAC9I,KAAK,kBA7V8E/C,EAAE,CAAAgD,kBAAA;MAAAC,KAAA,EA6VYsI,yBAAyB;MAAArI,OAAA,EAAzBqI,yBAAyB,CAAA5I;IAAA,EAAG;EAAE;AACjJ;AACA;EAAA,QAAAQ,SAAA,oBAAAA,SAAA,KA/VqGnD,EAAE,CAAAoD,iBAAA,CA+VXmI,yBAAyB,EAAc,CAAC;IACxHlI,IAAI,EAAEnD;EACV,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEmD,IAAI,EAAEC,SAAS;MAAEC,UAAU,EAAE,CAAC;QAC9DF,IAAI,EAAElD,MAAM;QACZqD,IAAI,EAAE,CAACpC,QAAQ;MACnB,CAAC;IAAE,CAAC,EAAE;MAAEiC,IAAI,EAAExC,EAAE,CAACI;IAAgB,CAAC,EAAE;MAAEoC,IAAI,EAAExC,EAAE,CAACG;IAA0B,CAAC,EAAE;MAAEqC,IAAI,EAAErD,EAAE,CAAC6L;IAAe,CAAC,CAAC;EAAE,CAAC;AAAA;AAC3H,SAASC,iCAAiCA,CAAA,EAAG;EACzC,OAAO,IAAI/K,6BAA6B,CAAC,CAAC;AAC9C;AACA,SAASgL,0BAA0BA,CAACvG,QAAQ,EAAEF,MAAM,EAAE0G,IAAI,EAAE;EACxD,OAAO,IAAIpG,wBAAwB,CAACJ,QAAQ,EAAEF,MAAM,EAAE0G,IAAI,CAAC;AAC/D;AACA,MAAMC,0BAA0B,GAAG,CAC/B;EAAEC,OAAO,EAAExL,gBAAgB;EAAEyL,QAAQ,EAAE9K;AAAwB,CAAC,EAChE;EAAE6K,OAAO,EAAElL,yBAAyB;EAAEoL,UAAU,EAAEN;AAAkC,CAAC,EACrF;EAAEI,OAAO,EAAEpL,gBAAgB;EAAEqL,QAAQ,EAAEZ;AAA0B,CAAC,EAAE;EAChEW,OAAO,EAAE9L,gBAAgB;EACzBgM,UAAU,EAAEL,0BAA0B;EACtCM,IAAI,EAAE,CAAC7L,oBAAoB,EAAEM,gBAAgB,EAAET,MAAM;AACzD,CAAC,CACJ;AACD;AACA;AACA;AACA;AACA,MAAMiM,4BAA4B,GAAG,CACjC;EAAEJ,OAAO,EAAEjL,eAAe;EAAEmL,UAAU,EAAEA,CAAA,KAAM,IAAIlL,oBAAoB,CAAC;AAAE,CAAC,EAC1E;EAAEgL,OAAO,EAAE5L,qBAAqB;EAAEiM,QAAQ,EAAE;AAAoB,CAAC,EAAE,GAAGN,0BAA0B,CACnG;AACD;AACA;AACA;AACA;AACA,MAAMO,iCAAiC,GAAG,CACtC;EAAEN,OAAO,EAAEjL,eAAe;EAAEkL,QAAQ,EAAEhL;AAAqB,CAAC,EAC5D;EAAE+K,OAAO,EAAE5L,qBAAqB;EAAEiM,QAAQ,EAAE;AAAiB,CAAC,EAAE,GAAGN,0BAA0B,CAChG;;AAED;AACA;AACA;AACA;AACA;AACA,MAAMQ,uBAAuB,CAAC;EAC1B;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,OAAOC,UAAUA,CAACC,MAAM,EAAE;IACtB,OAAO;MACHC,QAAQ,EAAEH,uBAAuB;MACjCI,SAAS,EAAEF,MAAM,CAACpC,iBAAiB,GAAGiC,iCAAiC,GACnEF;IACR,CAAC;EACL;EACA;IAAS,IAAI,CAAC3J,IAAI,YAAAmK,gCAAAjK,CAAA;MAAA,YAAAA,CAAA,IAAyF4J,uBAAuB;IAAA,CAAkD;EAAE;EACtL;IAAS,IAAI,CAACM,IAAI,kBAna+E/M,EAAE,CAAAgN,gBAAA;MAAA3J,IAAA,EAmaSoJ;IAAuB,EAA6B;EAAE;EAClK;IAAS,IAAI,CAACQ,IAAI,kBApa+EjN,EAAE,CAAAkN,gBAAA;MAAAL,SAAA,EAoa6CP,4BAA4B;MAAAa,OAAA,GAAY1M,aAAa;IAAA,EAAI;EAAE;AAC/M;AACA;EAAA,QAAA0C,SAAA,oBAAAA,SAAA,KAtaqGnD,EAAE,CAAAoD,iBAAA,CAsaXqJ,uBAAuB,EAAc,CAAC;IACtHpJ,IAAI,EAAE9C,QAAQ;IACdiD,IAAI,EAAE,CAAC;MACC4J,OAAO,EAAE,CAAC3M,aAAa,CAAC;MACxBoM,SAAS,EAAEP;IACf,CAAC;EACT,CAAC,CAAC;AAAA;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASe,iBAAiBA,CAAA,EAAG;EACzB;EACA;EACA,OAAO,CAAC,GAAGf,4BAA4B,CAAC;AAC5C;AACA;AACA;AACA;AACA;AACA,MAAMgB,oBAAoB,CAAC;EACvB;IAAS,IAAI,CAAC3K,IAAI,YAAA4K,6BAAA1K,CAAA;MAAA,YAAAA,CAAA,IAAyFyK,oBAAoB;IAAA,CAAkD;EAAE;EACnL;IAAS,IAAI,CAACP,IAAI,kBA9c+E/M,EAAE,CAAAgN,gBAAA;MAAA3J,IAAA,EA8cSiK;IAAoB,EAA6B;EAAE;EAC/J;IAAS,IAAI,CAACL,IAAI,kBA/c+EjN,EAAE,CAAAkN,gBAAA;MAAAL,SAAA,EA+c0CL,iCAAiC;MAAAW,OAAA,GAAY1M,aAAa;IAAA,EAAI;EAAE;AACjN;AACA;EAAA,QAAA0C,SAAA,oBAAAA,SAAA,KAjdqGnD,EAAE,CAAAoD,iBAAA,CAidXkK,oBAAoB,EAAc,CAAC;IACnHjK,IAAI,EAAE9C,QAAQ;IACdiD,IAAI,EAAE,CAAC;MACC4J,OAAO,EAAE,CAAC3M,aAAa,CAAC;MACxBoM,SAAS,EAAEL;IACf,CAAC;EACT,CAAC,CAAC;AAAA;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASgB,qBAAqBA,CAAA,EAAG;EAC7B;EACA;EACA,OAAO,CAAC,GAAGhB,iCAAiC,CAAC;AACjD;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA,SAASC,uBAAuB,EAAEa,oBAAoB,EAAED,iBAAiB,EAAEG,qBAAqB,EAAEjG,iBAAiB,IAAIkG,kBAAkB,EAAE7H,wBAAwB,IAAI8H,yBAAyB,EAAErM,uBAAuB,IAAIsM,wBAAwB,EAAEjL,uBAAuB,IAAIkL,wBAAwB,EAAErC,yBAAyB,IAAIsC,0BAA0B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}