{"version": 3, "file": "windowTime.js", "sources": ["../../../src/internal/operators/windowTime.ts"], "names": [], "mappings": ";AAAA,OAAO,EAAE,OAAO,EAAE,MAAM,YAAY,CAAC;AAErC,OAAO,EAAE,KAAK,EAAE,MAAM,oBAAoB,CAAC;AAC3C,OAAO,EAAE,UAAU,EAAE,MAAM,eAAe,CAAC;AAG3C,OAAO,EAAE,SAAS,EAAE,MAAM,mBAAmB,CAAC;AAC9C,OAAO,EAAE,WAAW,EAAE,MAAM,qBAAqB,CAAC;AA+FlD,MAAM,UAAU,UAAU,CAAI,cAAsB;IAClD,IAAI,SAAS,GAAkB,KAAK,CAAC;IACrC,IAAI,sBAAsB,GAAW,IAAI,CAAC;IAC1C,IAAI,aAAa,GAAW,MAAM,CAAC,iBAAiB,CAAC;IAErD,IAAI,WAAW,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,EAAE;QAC7B,SAAS,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;KAC1B;IAED,IAAI,WAAW,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,EAAE;QAC7B,SAAS,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;KAC1B;SAAM,IAAI,SAAS,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,EAAE;QAClC,aAAa,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;KACtC;IAED,IAAI,WAAW,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,EAAE;QAC7B,SAAS,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;KAC1B;SAAM,IAAI,SAAS,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,EAAE;QAClC,sBAAsB,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;KAC/C;IAED,OAAO,SAAS,0BAA0B,CAAC,MAAqB;QAC9D,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI,kBAAkB,CAAI,cAAc,EAAE,sBAAsB,EAAE,aAAa,EAAE,SAAS,CAAC,CAAC,CAAC;IAClH,CAAC,CAAC;AACJ,CAAC;AAED;IAEE,4BAAoB,cAAsB,EACtB,sBAAqC,EACrC,aAAqB,EACrB,SAAwB;QAHxB,mBAAc,GAAd,cAAc,CAAQ;QACtB,2BAAsB,GAAtB,sBAAsB,CAAe;QACrC,kBAAa,GAAb,aAAa,CAAQ;QACrB,cAAS,GAAT,SAAS,CAAe;IAC5C,CAAC;IAED,iCAAI,GAAJ,UAAK,UAAqC,EAAE,MAAW;QACrD,OAAO,MAAM,CAAC,SAAS,CAAC,IAAI,oBAAoB,CAC9C,UAAU,EAAE,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,sBAAsB,EAAE,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,SAAS,CACjG,CAAC,CAAC;IACL,CAAC;IACH,yBAAC;AAAD,CAAC,AAbD,IAaC;AA0BD;IAAgC,0CAAU;IAA1C;QAAA,qEAWC;QAVS,2BAAqB,GAAW,CAAC,CAAC;;IAU5C,CAAC;IARC,6BAAI,GAAJ,UAAK,KAAS;QACZ,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAC7B,iBAAM,IAAI,YAAC,KAAK,CAAC,CAAC;IACpB,CAAC;IAED,sBAAI,gDAAoB;aAAxB;YACE,OAAO,IAAI,CAAC,qBAAqB,CAAC;QACpC,CAAC;;;OAAA;IACH,qBAAC;AAAD,CAAC,AAXD,CAAgC,OAAO,GAWtC;AAOD;IAAsC,gDAAa;IAGjD,8BAAsB,WAAsC,EACxC,cAAsB,EACtB,sBAAqC,EACrC,aAAqB,EACrB,SAAwB;QAJ5C,YAKE,kBAAM,WAAW,CAAC,SAYnB;QAjBqB,iBAAW,GAAX,WAAW,CAA2B;QACxC,oBAAc,GAAd,cAAc,CAAQ;QACtB,4BAAsB,GAAtB,sBAAsB,CAAe;QACrC,mBAAa,GAAb,aAAa,CAAQ;QACrB,eAAS,GAAT,SAAS,CAAe;QANpC,aAAO,GAAwB,EAAE,CAAC;QASxC,IAAM,MAAM,GAAG,KAAI,CAAC,UAAU,EAAE,CAAC;QACjC,IAAI,sBAAsB,KAAK,IAAI,IAAI,sBAAsB,IAAI,CAAC,EAAE;YAClE,IAAM,UAAU,GAAkB,EAAE,UAAU,EAAE,KAAI,EAAE,MAAM,QAAA,EAAE,OAAO,EAAO,IAAI,EAAE,CAAC;YACnF,IAAM,aAAa,GAAqB,EAAE,cAAc,gBAAA,EAAE,sBAAsB,wBAAA,EAAE,UAAU,EAAE,KAAI,EAAE,SAAS,WAAA,EAAE,CAAC;YAChH,KAAI,CAAC,GAAG,CAAC,SAAS,CAAC,QAAQ,CAAgB,mBAAmB,EAAE,cAAc,EAAE,UAAU,CAAC,CAAC,CAAC;YAC7F,KAAI,CAAC,GAAG,CAAC,SAAS,CAAC,QAAQ,CAAmB,sBAAsB,EAAE,sBAAsB,EAAE,aAAa,CAAC,CAAC,CAAC;SAC/G;aAAM;YACL,IAAM,iBAAiB,GAAyB,EAAE,UAAU,EAAE,KAAI,EAAE,MAAM,QAAA,EAAE,cAAc,gBAAA,EAAE,CAAC;YAC7F,KAAI,CAAC,GAAG,CAAC,SAAS,CAAC,QAAQ,CAAuB,0BAA0B,EAAE,cAAc,EAAE,iBAAiB,CAAC,CAAC,CAAC;SACnH;;IACH,CAAC;IAES,oCAAK,GAAf,UAAgB,KAAQ;QACtB,IAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;QAC7B,IAAM,GAAG,GAAG,OAAO,CAAC,MAAM,CAAC;QAC3B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;YAC5B,IAAM,QAAM,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;YAC1B,IAAI,CAAC,QAAM,CAAC,MAAM,EAAE;gBAClB,QAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBACnB,IAAI,QAAM,CAAC,oBAAoB,IAAI,IAAI,CAAC,aAAa,EAAE;oBACrD,IAAI,CAAC,WAAW,CAAC,QAAM,CAAC,CAAC;iBAC1B;aACF;SACF;IACH,CAAC;IAES,qCAAM,GAAhB,UAAiB,GAAQ;QACvB,IAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;QAC7B,OAAO,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE;YACzB,OAAO,CAAC,KAAK,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;SAC5B;QACD,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IAC9B,CAAC;IAES,wCAAS,GAAnB;QACE,IAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;QAC7B,OAAO,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE;YACzB,IAAM,QAAM,GAAG,OAAO,CAAC,KAAK,EAAE,CAAC;YAC/B,IAAI,CAAC,QAAM,CAAC,MAAM,EAAE;gBAClB,QAAM,CAAC,QAAQ,EAAE,CAAC;aACnB;SACF;QACD,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE,CAAC;IAC9B,CAAC;IAEM,yCAAU,GAAjB;QACE,IAAM,MAAM,GAAG,IAAI,cAAc,EAAK,CAAC;QACvC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAC1B,IAAM,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC;QACrC,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACzB,OAAO,MAAM,CAAC;IAChB,CAAC;IAEM,0CAAW,GAAlB,UAAmB,MAAyB;QAC1C,MAAM,CAAC,QAAQ,EAAE,CAAC;QAClB,IAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;QAC7B,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC;IAC7C,CAAC;IACH,2BAAC;AAAD,CAAC,AApED,CAAsC,UAAU,GAoE/C;AAED,SAAS,0BAA0B,CAAiD,KAA2B;IACrG,IAAA,6BAAU,EAAE,qCAAc,EAAE,qBAAM,CAAW;IACrD,IAAI,MAAM,EAAE;QACV,UAAU,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;KAChC;IACD,KAAK,CAAC,MAAM,GAAG,UAAU,CAAC,UAAU,EAAE,CAAC;IACvC,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,cAAc,CAAC,CAAC;AACvC,CAAC;AAED,SAAS,sBAAsB,CAA6C,KAAuB;IACzF,IAAA,qCAAc,EAAE,6BAAU,EAAE,2BAAS,EAAE,qDAAsB,CAAW;IAChF,IAAM,MAAM,GAAG,UAAU,CAAC,UAAU,EAAE,CAAC;IACvC,IAAM,MAAM,GAAG,IAAI,CAAC;IACpB,IAAI,OAAO,GAA0B,EAAE,MAAM,QAAA,EAAE,YAAY,EAAO,IAAI,EAAE,CAAC;IACzE,IAAM,aAAa,GAAkB,EAAE,UAAU,YAAA,EAAE,MAAM,QAAA,EAAE,OAAO,SAAA,EAAE,CAAC;IACrE,OAAO,CAAC,YAAY,GAAG,SAAS,CAAC,QAAQ,CAAgB,mBAAmB,EAAE,cAAc,EAAE,aAAa,CAAC,CAAC;IAC7G,MAAM,CAAC,GAAG,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;IACjC,MAAM,CAAC,QAAQ,CAAC,KAAK,EAAE,sBAAsB,CAAC,CAAC;AACjD,CAAC;AAED,SAAS,mBAAmB,CAAI,KAAoB;IAC1C,IAAA,6BAAU,EAAE,qBAAM,EAAE,uBAAO,CAAW;IAC9C,IAAI,OAAO,IAAI,OAAO,CAAC,MAAM,IAAI,OAAO,CAAC,YAAY,EAAE;QACrD,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;KAC7C;IACD,UAAU,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;AACjC,CAAC"}