{"ast": null, "code": "import { CORE_SIZE } from './constants';\nimport { fromEvent, map, take } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"./quran.service\";\nexport class HelperService {\n  constructor(quranService) {\n    this.quranService = quranService;\n  }\n  SurahNumberRestrict(surahNumber, startAyahInput, endAyahInput) {\n    startAyahInput.disabled = true;\n    endAyahInput.disabled = true;\n    startAyahInput.value = '';\n    endAyahInput.value = '';\n    this.quranService.GetSurah(surahNumber)?.subscribe(surah => {\n      startAyahInput.value = '1';\n      endAyahInput.value = surah.totalAyah.toString();\n      startAyahInput.max = surah.totalAyah.toString();\n      endAyahInput.max = surah.totalAyah.toString();\n      startAyahInput.disabled = false;\n      endAyahInput.disabled = false;\n    });\n  }\n  InputNumberRestrict(input) {\n    let numVal = Number.parseInt(input.value);\n    let max = Number.parseInt(input.max);\n    let min = Number.parseInt(input.min);\n    if (numVal > max) {\n      input.value = max.toString();\n    }\n    if (numVal < min) {\n      input.value = min.toString();\n    }\n  }\n  getDownloadProgress(url, recieved) {\n    const total = CORE_SIZE[url];\n    return Math.floor(recieved / total * 100);\n  }\n  getDuration(data) {\n    // Create a Blob from the Uint8Array\n    const blob = new Blob([data], {\n      type: 'audio/mpeg'\n    });\n    // Create an object URL from the Blob\n    const url = URL.createObjectURL(blob);\n    const audio = new Audio(url);\n    return fromEvent(audio, 'loadedmetadata').pipe(map(() => audio.duration), take(1)).toPromise();\n  }\n  static {\n    this.ɵfac = function HelperService_Factory(t) {\n      return new (t || HelperService)(i0.ɵɵinject(i1.QuranService));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: HelperService,\n      factory: HelperService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["CORE_SIZE", "fromEvent", "map", "take", "HelperService", "constructor", "quranService", "SurahNumberRestrict", "surah<PERSON>umber", "startAyahInput", "endAyahInput", "disabled", "value", "Get<PERSON><PERSON><PERSON>", "subscribe", "surah", "totalAyah", "toString", "max", "InputNumberRestrict", "input", "numVal", "Number", "parseInt", "min", "getDownloadProgress", "url", "recieved", "total", "Math", "floor", "getDuration", "data", "blob", "Blob", "type", "URL", "createObjectURL", "audio", "Audio", "pipe", "duration", "to<PERSON>romise", "i0", "ɵɵinject", "i1", "QuranService", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Desktop\\تطبيق ناشر ايات قئانية\\QuranVidGen\\src\\app\\Services\\helper.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { CORE_SIZE } from './constants';\r\nimport { QuranService } from './quran.service';\r\nimport { Observable, fromEvent, map, take } from 'rxjs';\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class HelperService {\r\n\r\n  constructor(private quranService:QuranService) { }\r\n\r\n\r\n  SurahNumberRestrict(surahNumber:number,startAyahInput:HTMLInputElement,endAyahInput:HTMLInputElement){\r\n    startAyahInput.disabled = true;\r\n    endAyahInput.disabled = true;\r\n    startAyahInput.value = '';\r\n    endAyahInput.value = '';\r\n    this.quranService.GetSurah(surahNumber)?.subscribe(surah => {\r\n      startAyahInput.value = '1';\r\n      endAyahInput.value = surah.totalAyah.toString();\r\n      startAyahInput.max = surah.totalAyah.toString();\r\n      endAyahInput.max = surah.totalAyah.toString();\r\n      startAyahInput.disabled = false;\r\n      endAyahInput.disabled = false;\r\n    });\r\n\r\n\r\n  }\r\n\r\n  InputNumberRestrict(input:HTMLInputElement){\r\n    let numVal = Number.parseInt(input.value);\r\n    let max = Number.parseInt(input.max)\r\n    let min = Number.parseInt(input.min)\r\n    if(numVal > max){\r\n      input.value = max.toString();\r\n    }\r\n    if(numVal < min){\r\n      input.value = min.toString();\r\n    }\r\n  }\r\n\r\n  getDownloadProgress(url:string,recieved:number):number{\r\n    const total = (CORE_SIZE as any)[url];\r\n    return Math.floor(recieved / total * 100);\r\n  }\r\n\r\n  getDuration(data: Uint8Array): Promise<number | undefined> {\r\n    // Create a Blob from the Uint8Array\r\n    const blob = new Blob([data], { type: 'audio/mpeg' });\r\n\r\n    // Create an object URL from the Blob\r\n    const url = URL.createObjectURL(blob);\r\n\r\n    const audio = new Audio(url);\r\n    return fromEvent(audio, 'loadedmetadata').pipe(\r\n      map(() => audio.duration),\r\n      take(1)\r\n    ).toPromise();\r\n  }\r\n\r\n}\r\n"], "mappings": "AACA,SAASA,SAAS,QAAQ,aAAa;AAEvC,SAAqBC,SAAS,EAAEC,GAAG,EAAEC,IAAI,QAAQ,MAAM;;;AAKvD,OAAM,MAAOC,aAAa;EAExBC,YAAoBC,YAAyB;IAAzB,KAAAA,YAAY,GAAZA,YAAY;EAAiB;EAGjDC,mBAAmBA,CAACC,WAAkB,EAACC,cAA+B,EAACC,YAA6B;IAClGD,cAAc,CAACE,QAAQ,GAAG,IAAI;IAC9BD,YAAY,CAACC,QAAQ,GAAG,IAAI;IAC5BF,cAAc,CAACG,KAAK,GAAG,EAAE;IACzBF,YAAY,CAACE,KAAK,GAAG,EAAE;IACvB,IAAI,CAACN,YAAY,CAACO,QAAQ,CAACL,WAAW,CAAC,EAAEM,SAAS,CAACC,KAAK,IAAG;MACzDN,cAAc,CAACG,KAAK,GAAG,GAAG;MAC1BF,YAAY,CAACE,KAAK,GAAGG,KAAK,CAACC,SAAS,CAACC,QAAQ,EAAE;MAC/CR,cAAc,CAACS,GAAG,GAAGH,KAAK,CAACC,SAAS,CAACC,QAAQ,EAAE;MAC/CP,YAAY,CAACQ,GAAG,GAAGH,KAAK,CAACC,SAAS,CAACC,QAAQ,EAAE;MAC7CR,cAAc,CAACE,QAAQ,GAAG,KAAK;MAC/BD,YAAY,CAACC,QAAQ,GAAG,KAAK;IAC/B,CAAC,CAAC;EAGJ;EAEAQ,mBAAmBA,CAACC,KAAsB;IACxC,IAAIC,MAAM,GAAGC,MAAM,CAACC,QAAQ,CAACH,KAAK,CAACR,KAAK,CAAC;IACzC,IAAIM,GAAG,GAAGI,MAAM,CAACC,QAAQ,CAACH,KAAK,CAACF,GAAG,CAAC;IACpC,IAAIM,GAAG,GAAGF,MAAM,CAACC,QAAQ,CAACH,KAAK,CAACI,GAAG,CAAC;IACpC,IAAGH,MAAM,GAAGH,GAAG,EAAC;MACdE,KAAK,CAACR,KAAK,GAAGM,GAAG,CAACD,QAAQ,EAAE;;IAE9B,IAAGI,MAAM,GAAGG,GAAG,EAAC;MACdJ,KAAK,CAACR,KAAK,GAAGY,GAAG,CAACP,QAAQ,EAAE;;EAEhC;EAEAQ,mBAAmBA,CAACC,GAAU,EAACC,QAAe;IAC5C,MAAMC,KAAK,GAAI5B,SAAiB,CAAC0B,GAAG,CAAC;IACrC,OAAOG,IAAI,CAACC,KAAK,CAACH,QAAQ,GAAGC,KAAK,GAAG,GAAG,CAAC;EAC3C;EAEAG,WAAWA,CAACC,IAAgB;IAC1B;IACA,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACF,IAAI,CAAC,EAAE;MAAEG,IAAI,EAAE;IAAY,CAAE,CAAC;IAErD;IACA,MAAMT,GAAG,GAAGU,GAAG,CAACC,eAAe,CAACJ,IAAI,CAAC;IAErC,MAAMK,KAAK,GAAG,IAAIC,KAAK,CAACb,GAAG,CAAC;IAC5B,OAAOzB,SAAS,CAACqC,KAAK,EAAE,gBAAgB,CAAC,CAACE,IAAI,CAC5CtC,GAAG,CAAC,MAAMoC,KAAK,CAACG,QAAQ,CAAC,EACzBtC,IAAI,CAAC,CAAC,CAAC,CACR,CAACuC,SAAS,EAAE;EACf;;;uBAnDWtC,aAAa,EAAAuC,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,YAAA;IAAA;EAAA;;;aAAb1C,aAAa;MAAA2C,OAAA,EAAb3C,aAAa,CAAA4C,IAAA;MAAAC,UAAA,EAFZ;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}