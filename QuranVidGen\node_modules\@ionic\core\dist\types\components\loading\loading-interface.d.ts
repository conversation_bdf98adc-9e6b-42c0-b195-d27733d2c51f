import type { <PERSON><PERSON><PERSON><PERSON>, Mode } from '../../interface';
import type { IonicSafeString } from '../../utils/sanitization';
import type { SpinnerTypes } from '../spinner/spinner-configs';
export interface LoadingOptions {
    spinner?: SpinnerTypes | null;
    message?: string | IonicSafeString;
    cssClass?: string | string[];
    showBackdrop?: boolean;
    duration?: number;
    translucent?: boolean;
    animated?: boolean;
    backdropDismiss?: boolean;
    mode?: Mode;
    keyboardClose?: boolean;
    id?: string;
    htmlAttributes?: {
        [key: string]: any;
    };
    enterAnimation?: AnimationBuilder;
    leaveAnimation?: AnimationBuilder;
}
