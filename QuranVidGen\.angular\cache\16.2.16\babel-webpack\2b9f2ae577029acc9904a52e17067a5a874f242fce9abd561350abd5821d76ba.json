{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Desktop/\\u062A\\u0637\\u0628\\u064A\\u0642 \\u0646\\u0627\\u0634\\u0631 \\u0627\\u064A\\u0627\\u062A \\u0642\\u0626\\u0627\\u0646\\u064A\\u0629/QuranVidGen/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { ERROR_RESPONSE_BODY_READER, ERROR_INCOMPLETED_DOWNLOAD } from \"./errors.js\";\nimport { HeaderContentLength } from \"./const.js\";\nconst readFromBlobOrFile = blob => new Promise((resolve, reject) => {\n  const fileReader = new FileReader();\n  fileReader.onload = () => {\n    const {\n      result\n    } = fileReader;\n    if (result instanceof ArrayBuffer) {\n      resolve(new Uint8Array(result));\n    } else {\n      resolve(new Uint8Array());\n    }\n  };\n  fileReader.onerror = event => {\n    reject(Error(`File could not be read! Code=${event?.target?.error?.code || -1}`));\n  };\n  fileReader.readAsArrayBuffer(blob);\n});\n/**\n * An util function to fetch data from url string, base64, URL, File or Blob format.\n *\n * Examples:\n * ```ts\n * // URL\n * await fetchFile(\"http://localhost:3000/video.mp4\");\n * // base64\n * await fetchFile(\"data:<type>;base64,wL2dvYWwgbW9yZ...\");\n * // URL\n * await fetchFile(new URL(\"video.mp4\", import.meta.url));\n * // File\n * fileInput.addEventListener('change', (e) => {\n *   await fetchFile(e.target.files[0]);\n * });\n * // Blob\n * const blob = new Blob(...);\n * await fetchFile(blob);\n * ```\n */\nexport const fetchFile = /*#__PURE__*/function () {\n  var _ref = _asyncToGenerator(function* (file) {\n    let data;\n    if (typeof file === \"string\") {\n      /* From base64 format */\n      if (/data:_data\\/([a-zA-Z]*);base64,([^\"]*)/.test(file)) {\n        data = atob(file.split(\",\")[1]).split(\"\").map(c => c.charCodeAt(0));\n        /* From remote server/URL */\n      } else {\n        data = yield (yield fetch(file)).arrayBuffer();\n      }\n    } else if (file instanceof URL) {\n      data = yield (yield fetch(file)).arrayBuffer();\n    } else if (file instanceof File || file instanceof Blob) {\n      data = yield readFromBlobOrFile(file);\n    } else {\n      return new Uint8Array();\n    }\n    return new Uint8Array(data);\n  });\n  return function fetchFile(_x) {\n    return _ref.apply(this, arguments);\n  };\n}();\n/**\n * importScript dynamically import a script, useful when you\n * want to use different versions of ffmpeg.wasm based on environment.\n *\n * Example:\n *\n * ```ts\n * await importScript(\"http://localhost:3000/ffmpeg.js\");\n * ```\n */\nexport const importScript = /*#__PURE__*/function () {\n  var _ref2 = _asyncToGenerator(function* (url) {\n    return new Promise(resolve => {\n      const script = document.createElement(\"script\");\n      const eventHandler = () => {\n        script.removeEventListener(\"load\", eventHandler);\n        resolve();\n      };\n      script.src = url;\n      script.type = \"text/javascript\";\n      script.addEventListener(\"load\", eventHandler);\n      document.getElementsByTagName(\"head\")[0].appendChild(script);\n    });\n  });\n  return function importScript(_x2) {\n    return _ref2.apply(this, arguments);\n  };\n}();\n/**\n * Download content of a URL with progress.\n *\n * Progress only works when Content-Length is provided by the server.\n *\n */\nexport const downloadWithProgress = /*#__PURE__*/function () {\n  var _ref3 = _asyncToGenerator(function* (url, cb) {\n    const resp = yield fetch(url);\n    let buf;\n    try {\n      // Set total to -1 to indicate that there is not Content-Type Header.\n      const total = parseInt(resp.headers.get(HeaderContentLength) || \"-1\");\n      const reader = resp.body?.getReader();\n      if (!reader) throw ERROR_RESPONSE_BODY_READER;\n      const chunks = [];\n      let received = 0;\n      for (;;) {\n        const {\n          done,\n          value\n        } = yield reader.read();\n        const delta = value ? value.length : 0;\n        if (done) {\n          if (total != -1 && total !== received) throw ERROR_INCOMPLETED_DOWNLOAD;\n          cb && cb({\n            url,\n            total,\n            received,\n            delta,\n            done\n          });\n          break;\n        }\n        chunks.push(value);\n        received += delta;\n        cb && cb({\n          url,\n          total,\n          received,\n          delta,\n          done\n        });\n      }\n      const data = new Uint8Array(received);\n      let position = 0;\n      for (const chunk of chunks) {\n        data.set(chunk, position);\n        position += chunk.length;\n      }\n      buf = data.buffer;\n    } catch (e) {\n      console.log(`failed to send download progress event: `, e);\n      // Fetch arrayBuffer directly when it is not possible to get progress.\n      buf = yield resp.arrayBuffer();\n      cb && cb({\n        url,\n        total: buf.byteLength,\n        received: buf.byteLength,\n        delta: 0,\n        done: true\n      });\n    }\n    return buf;\n  });\n  return function downloadWithProgress(_x3, _x4) {\n    return _ref3.apply(this, arguments);\n  };\n}();\n/**\n * toBlobURL fetches data from an URL and return a blob URL.\n *\n * Example:\n *\n * ```ts\n * await toBlobURL(\"http://localhost:3000/ffmpeg.js\", \"text/javascript\");\n * ```\n */\nexport const toBlobURL = /*#__PURE__*/function () {\n  var _ref4 = _asyncToGenerator(function* (url, mimeType, progress = false, cb) {\n    const buf = progress ? yield downloadWithProgress(url, cb) : yield (yield fetch(url)).arrayBuffer();\n    const blob = new Blob([buf], {\n      type: mimeType\n    });\n    return URL.createObjectURL(blob);\n  });\n  return function toBlobURL(_x5, _x6) {\n    return _ref4.apply(this, arguments);\n  };\n}();", "map": {"version": 3, "names": ["ERROR_RESPONSE_BODY_READER", "ERROR_INCOMPLETED_DOWNLOAD", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "readFromBlobOrFile", "blob", "Promise", "resolve", "reject", "fileReader", "FileReader", "onload", "result", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Uint8Array", "onerror", "event", "Error", "target", "error", "code", "readAsA<PERSON>y<PERSON><PERSON>er", "fetchFile", "_ref", "_asyncToGenerator", "file", "data", "test", "atob", "split", "map", "c", "charCodeAt", "fetch", "arrayBuffer", "URL", "File", "Blob", "_x", "apply", "arguments", "importScript", "_ref2", "url", "script", "document", "createElement", "<PERSON><PERSON><PERSON><PERSON>", "removeEventListener", "src", "type", "addEventListener", "getElementsByTagName", "append<PERSON><PERSON><PERSON>", "_x2", "downloadWithProgress", "_ref3", "cb", "resp", "buf", "total", "parseInt", "headers", "get", "reader", "body", "<PERSON><PERSON><PERSON><PERSON>", "chunks", "received", "done", "value", "read", "delta", "length", "push", "position", "chunk", "set", "buffer", "e", "console", "log", "byteLength", "_x3", "_x4", "toBlobURL", "_ref4", "mimeType", "progress", "createObjectURL", "_x5", "_x6"], "sources": ["C:/Users/<USER>/Desktop/تطبيق ناشر ايات قئانية/QuranVidGen/node_modules/@ffmpeg/util/dist/esm/index.js"], "sourcesContent": ["import { ERROR_RESPONSE_BODY_READER, ERROR_INCOMPLETED_DOWNLOAD, } from \"./errors.js\";\nimport { HeaderContentLength } from \"./const.js\";\nconst readFromBlobOrFile = (blob) => new Promise((resolve, reject) => {\n    const fileReader = new FileReader();\n    fileReader.onload = () => {\n        const { result } = fileReader;\n        if (result instanceof ArrayBuffer) {\n            resolve(new Uint8Array(result));\n        }\n        else {\n            resolve(new Uint8Array());\n        }\n    };\n    fileReader.onerror = (event) => {\n        reject(Error(`File could not be read! Code=${event?.target?.error?.code || -1}`));\n    };\n    fileReader.readAsArrayBuffer(blob);\n});\n/**\n * An util function to fetch data from url string, base64, URL, File or Blob format.\n *\n * Examples:\n * ```ts\n * // URL\n * await fetchFile(\"http://localhost:3000/video.mp4\");\n * // base64\n * await fetchFile(\"data:<type>;base64,wL2dvYWwgbW9yZ...\");\n * // URL\n * await fetchFile(new URL(\"video.mp4\", import.meta.url));\n * // File\n * fileInput.addEventListener('change', (e) => {\n *   await fetchFile(e.target.files[0]);\n * });\n * // Blob\n * const blob = new Blob(...);\n * await fetchFile(blob);\n * ```\n */\nexport const fetchFile = async (file) => {\n    let data;\n    if (typeof file === \"string\") {\n        /* From base64 format */\n        if (/data:_data\\/([a-zA-Z]*);base64,([^\"]*)/.test(file)) {\n            data = atob(file.split(\",\")[1])\n                .split(\"\")\n                .map((c) => c.charCodeAt(0));\n            /* From remote server/URL */\n        }\n        else {\n            data = await (await fetch(file)).arrayBuffer();\n        }\n    }\n    else if (file instanceof URL) {\n        data = await (await fetch(file)).arrayBuffer();\n    }\n    else if (file instanceof File || file instanceof Blob) {\n        data = await readFromBlobOrFile(file);\n    }\n    else {\n        return new Uint8Array();\n    }\n    return new Uint8Array(data);\n};\n/**\n * importScript dynamically import a script, useful when you\n * want to use different versions of ffmpeg.wasm based on environment.\n *\n * Example:\n *\n * ```ts\n * await importScript(\"http://localhost:3000/ffmpeg.js\");\n * ```\n */\nexport const importScript = async (url) => new Promise((resolve) => {\n    const script = document.createElement(\"script\");\n    const eventHandler = () => {\n        script.removeEventListener(\"load\", eventHandler);\n        resolve();\n    };\n    script.src = url;\n    script.type = \"text/javascript\";\n    script.addEventListener(\"load\", eventHandler);\n    document.getElementsByTagName(\"head\")[0].appendChild(script);\n});\n/**\n * Download content of a URL with progress.\n *\n * Progress only works when Content-Length is provided by the server.\n *\n */\nexport const downloadWithProgress = async (url, cb) => {\n    const resp = await fetch(url);\n    let buf;\n    try {\n        // Set total to -1 to indicate that there is not Content-Type Header.\n        const total = parseInt(resp.headers.get(HeaderContentLength) || \"-1\");\n        const reader = resp.body?.getReader();\n        if (!reader)\n            throw ERROR_RESPONSE_BODY_READER;\n        const chunks = [];\n        let received = 0;\n        for (;;) {\n            const { done, value } = await reader.read();\n            const delta = value ? value.length : 0;\n            if (done) {\n                if (total != -1 && total !== received)\n                    throw ERROR_INCOMPLETED_DOWNLOAD;\n                cb && cb({ url, total, received, delta, done });\n                break;\n            }\n            chunks.push(value);\n            received += delta;\n            cb && cb({ url, total, received, delta, done });\n        }\n        const data = new Uint8Array(received);\n        let position = 0;\n        for (const chunk of chunks) {\n            data.set(chunk, position);\n            position += chunk.length;\n        }\n        buf = data.buffer;\n    }\n    catch (e) {\n        console.log(`failed to send download progress event: `, e);\n        // Fetch arrayBuffer directly when it is not possible to get progress.\n        buf = await resp.arrayBuffer();\n        cb &&\n            cb({\n                url,\n                total: buf.byteLength,\n                received: buf.byteLength,\n                delta: 0,\n                done: true,\n            });\n    }\n    return buf;\n};\n/**\n * toBlobURL fetches data from an URL and return a blob URL.\n *\n * Example:\n *\n * ```ts\n * await toBlobURL(\"http://localhost:3000/ffmpeg.js\", \"text/javascript\");\n * ```\n */\nexport const toBlobURL = async (url, mimeType, progress = false, cb) => {\n    const buf = progress\n        ? await downloadWithProgress(url, cb)\n        : await (await fetch(url)).arrayBuffer();\n    const blob = new Blob([buf], { type: mimeType });\n    return URL.createObjectURL(blob);\n};\n"], "mappings": ";AAAA,SAASA,0BAA0B,EAAEC,0BAA0B,QAAS,aAAa;AACrF,SAASC,mBAAmB,QAAQ,YAAY;AAChD,MAAMC,kBAAkB,GAAIC,IAAI,IAAK,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;EAClE,MAAMC,UAAU,GAAG,IAAIC,UAAU,CAAC,CAAC;EACnCD,UAAU,CAACE,MAAM,GAAG,MAAM;IACtB,MAAM;MAAEC;IAAO,CAAC,GAAGH,UAAU;IAC7B,IAAIG,MAAM,YAAYC,WAAW,EAAE;MAC/BN,OAAO,CAAC,IAAIO,UAAU,CAACF,MAAM,CAAC,CAAC;IACnC,CAAC,MACI;MACDL,OAAO,CAAC,IAAIO,UAAU,CAAC,CAAC,CAAC;IAC7B;EACJ,CAAC;EACDL,UAAU,CAACM,OAAO,GAAIC,KAAK,IAAK;IAC5BR,MAAM,CAACS,KAAK,CAAE,gCAA+BD,KAAK,EAAEE,MAAM,EAAEC,KAAK,EAAEC,IAAI,IAAI,CAAC,CAAE,EAAC,CAAC,CAAC;EACrF,CAAC;EACDX,UAAU,CAACY,iBAAiB,CAAChB,IAAI,CAAC;AACtC,CAAC,CAAC;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMiB,SAAS;EAAA,IAAAC,IAAA,GAAAC,iBAAA,CAAG,WAAOC,IAAI,EAAK;IACrC,IAAIC,IAAI;IACR,IAAI,OAAOD,IAAI,KAAK,QAAQ,EAAE;MAC1B;MACA,IAAI,wCAAwC,CAACE,IAAI,CAACF,IAAI,CAAC,EAAE;QACrDC,IAAI,GAAGE,IAAI,CAACH,IAAI,CAACI,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAC1BA,KAAK,CAAC,EAAE,CAAC,CACTC,GAAG,CAAEC,CAAC,IAAKA,CAAC,CAACC,UAAU,CAAC,CAAC,CAAC,CAAC;QAChC;MACJ,CAAC,MACI;QACDN,IAAI,SAAS,OAAOO,KAAK,CAACR,IAAI,CAAC,EAAES,WAAW,CAAC,CAAC;MAClD;IACJ,CAAC,MACI,IAAIT,IAAI,YAAYU,GAAG,EAAE;MAC1BT,IAAI,SAAS,OAAOO,KAAK,CAACR,IAAI,CAAC,EAAES,WAAW,CAAC,CAAC;IAClD,CAAC,MACI,IAAIT,IAAI,YAAYW,IAAI,IAAIX,IAAI,YAAYY,IAAI,EAAE;MACnDX,IAAI,SAAStB,kBAAkB,CAACqB,IAAI,CAAC;IACzC,CAAC,MACI;MACD,OAAO,IAAIX,UAAU,CAAC,CAAC;IAC3B;IACA,OAAO,IAAIA,UAAU,CAACY,IAAI,CAAC;EAC/B,CAAC;EAAA,gBAxBYJ,SAASA,CAAAgB,EAAA;IAAA,OAAAf,IAAA,CAAAgB,KAAA,OAAAC,SAAA;EAAA;AAAA,GAwBrB;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,YAAY;EAAA,IAAAC,KAAA,GAAAlB,iBAAA,CAAG,WAAOmB,GAAG;IAAA,OAAK,IAAIrC,OAAO,CAAEC,OAAO,IAAK;MAChE,MAAMqC,MAAM,GAAGC,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;MAC/C,MAAMC,YAAY,GAAGA,CAAA,KAAM;QACvBH,MAAM,CAACI,mBAAmB,CAAC,MAAM,EAAED,YAAY,CAAC;QAChDxC,OAAO,CAAC,CAAC;MACb,CAAC;MACDqC,MAAM,CAACK,GAAG,GAAGN,GAAG;MAChBC,MAAM,CAACM,IAAI,GAAG,iBAAiB;MAC/BN,MAAM,CAACO,gBAAgB,CAAC,MAAM,EAAEJ,YAAY,CAAC;MAC7CF,QAAQ,CAACO,oBAAoB,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAACC,WAAW,CAACT,MAAM,CAAC;IAChE,CAAC,CAAC;EAAA;EAAA,gBAVWH,YAAYA,CAAAa,GAAA;IAAA,OAAAZ,KAAA,CAAAH,KAAA,OAAAC,SAAA;EAAA;AAAA,GAUvB;AACF;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMe,oBAAoB;EAAA,IAAAC,KAAA,GAAAhC,iBAAA,CAAG,WAAOmB,GAAG,EAAEc,EAAE,EAAK;IACnD,MAAMC,IAAI,SAASzB,KAAK,CAACU,GAAG,CAAC;IAC7B,IAAIgB,GAAG;IACP,IAAI;MACA;MACA,MAAMC,KAAK,GAAGC,QAAQ,CAACH,IAAI,CAACI,OAAO,CAACC,GAAG,CAAC5D,mBAAmB,CAAC,IAAI,IAAI,CAAC;MACrE,MAAM6D,MAAM,GAAGN,IAAI,CAACO,IAAI,EAAEC,SAAS,CAAC,CAAC;MACrC,IAAI,CAACF,MAAM,EACP,MAAM/D,0BAA0B;MACpC,MAAMkE,MAAM,GAAG,EAAE;MACjB,IAAIC,QAAQ,GAAG,CAAC;MAChB,SAAS;QACL,MAAM;UAAEC,IAAI;UAAEC;QAAM,CAAC,SAASN,MAAM,CAACO,IAAI,CAAC,CAAC;QAC3C,MAAMC,KAAK,GAAGF,KAAK,GAAGA,KAAK,CAACG,MAAM,GAAG,CAAC;QACtC,IAAIJ,IAAI,EAAE;UACN,IAAIT,KAAK,IAAI,CAAC,CAAC,IAAIA,KAAK,KAAKQ,QAAQ,EACjC,MAAMlE,0BAA0B;UACpCuD,EAAE,IAAIA,EAAE,CAAC;YAAEd,GAAG;YAAEiB,KAAK;YAAEQ,QAAQ;YAAEI,KAAK;YAAEH;UAAK,CAAC,CAAC;UAC/C;QACJ;QACAF,MAAM,CAACO,IAAI,CAACJ,KAAK,CAAC;QAClBF,QAAQ,IAAII,KAAK;QACjBf,EAAE,IAAIA,EAAE,CAAC;UAAEd,GAAG;UAAEiB,KAAK;UAAEQ,QAAQ;UAAEI,KAAK;UAAEH;QAAK,CAAC,CAAC;MACnD;MACA,MAAM3C,IAAI,GAAG,IAAIZ,UAAU,CAACsD,QAAQ,CAAC;MACrC,IAAIO,QAAQ,GAAG,CAAC;MAChB,KAAK,MAAMC,KAAK,IAAIT,MAAM,EAAE;QACxBzC,IAAI,CAACmD,GAAG,CAACD,KAAK,EAAED,QAAQ,CAAC;QACzBA,QAAQ,IAAIC,KAAK,CAACH,MAAM;MAC5B;MACAd,GAAG,GAAGjC,IAAI,CAACoD,MAAM;IACrB,CAAC,CACD,OAAOC,CAAC,EAAE;MACNC,OAAO,CAACC,GAAG,CAAE,0CAAyC,EAAEF,CAAC,CAAC;MAC1D;MACApB,GAAG,SAASD,IAAI,CAACxB,WAAW,CAAC,CAAC;MAC9BuB,EAAE,IACEA,EAAE,CAAC;QACCd,GAAG;QACHiB,KAAK,EAAED,GAAG,CAACuB,UAAU;QACrBd,QAAQ,EAAET,GAAG,CAACuB,UAAU;QACxBV,KAAK,EAAE,CAAC;QACRH,IAAI,EAAE;MACV,CAAC,CAAC;IACV;IACA,OAAOV,GAAG;EACd,CAAC;EAAA,gBA9CYJ,oBAAoBA,CAAA4B,GAAA,EAAAC,GAAA;IAAA,OAAA5B,KAAA,CAAAjB,KAAA,OAAAC,SAAA;EAAA;AAAA,GA8ChC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAM6C,SAAS;EAAA,IAAAC,KAAA,GAAA9D,iBAAA,CAAG,WAAOmB,GAAG,EAAE4C,QAAQ,EAAEC,QAAQ,GAAG,KAAK,EAAE/B,EAAE,EAAK;IACpE,MAAME,GAAG,GAAG6B,QAAQ,SACRjC,oBAAoB,CAACZ,GAAG,EAAEc,EAAE,CAAC,SAC7B,OAAOxB,KAAK,CAACU,GAAG,CAAC,EAAET,WAAW,CAAC,CAAC;IAC5C,MAAM7B,IAAI,GAAG,IAAIgC,IAAI,CAAC,CAACsB,GAAG,CAAC,EAAE;MAAET,IAAI,EAAEqC;IAAS,CAAC,CAAC;IAChD,OAAOpD,GAAG,CAACsD,eAAe,CAACpF,IAAI,CAAC;EACpC,CAAC;EAAA,gBANYgF,SAASA,CAAAK,GAAA,EAAAC,GAAA;IAAA,OAAAL,KAAA,CAAA/C,KAAA,OAAAC,SAAA;EAAA;AAAA,GAMrB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}