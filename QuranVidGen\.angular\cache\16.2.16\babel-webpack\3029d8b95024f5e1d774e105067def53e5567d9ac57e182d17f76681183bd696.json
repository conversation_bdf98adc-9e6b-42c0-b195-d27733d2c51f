{"ast": null, "code": "import { registerPlugin } from '@capacitor/core';\nconst Filesystem = registerPlugin('Filesystem', {\n  web: () => import('./web').then(m => new m.FilesystemWeb())\n});\nexport * from './definitions';\nexport { Filesystem };", "map": {"version": 3, "names": ["registerPlugin", "Filesystem", "web", "then", "m", "FilesystemWeb"], "sources": ["C:/Users/<USER>/Desktop/تطبيق ناشر ايات قئانية/QuranVidGen/node_modules/@capacitor/filesystem/dist/esm/index.js"], "sourcesContent": ["import { registerPlugin } from '@capacitor/core';\nconst Filesystem = registerPlugin('Filesystem', {\n    web: () => import('./web').then(m => new m.FilesystemWeb()),\n});\nexport * from './definitions';\nexport { Filesystem };\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,iBAAiB;AAChD,MAAMC,UAAU,GAAGD,cAAc,CAAC,YAAY,EAAE;EAC5CE,GAAG,EAAEA,CAAA,KAAM,MAAM,CAAC,OAAO,CAAC,CAACC,IAAI,CAACC,CAAC,IAAI,IAAIA,CAAC,CAACC,aAAa,CAAC,CAAC;AAC9D,CAAC,CAAC;AACF,cAAc,eAAe;AAC7B,SAASJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}