// DO NOT EDIT THIS FILE! IT IS GENERATED EACH TIME "capacitor update" IS RUN

android {
  compileOptions {
      sourceCompatibility JavaVersion.VERSION_17
      targetCompatibility JavaVersion.VERSION_17
  }
}

apply from: "../capacitor-cordova-android-plugins/cordova.variables.gradle"
dependencies {
    implementation project(':capacitor-dialog')
    implementation project(':capacitor-filesystem')
    implementation project(':himeka-capacitor-ffmpeg-kit')

}


if (hasProperty('postBuildExtras')) {
  postBuildExtras()
}
