{"ast": null, "code": "import { forkJoin, map } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"./call-server.service\";\nimport * as i2 from \"@angular/common/http\";\nexport class QuranService {\n  constructor(callServerService, http) {\n    this.callServerService = callServerService;\n    this.http = http;\n  }\n  GetSurah(surahNumber) {\n    return this.callServerService.Get(`https://quranapi.pages.dev/api/${surahNumber}.json`);\n  }\n  GetAyah(surahNumber, AyahNumber) {\n    return this.callServerService.Get(`https://quranapi.pages.dev/api/${surahNumber}/${AyahNumber}.json`);\n  }\n  GetAyatTexts(surahNumber, AyahStartNumber, AyahEndNumber, language) {\n    let observables = [];\n    for (let i = AyahStartNumber; i <= AyahEndNumber; i++) {\n      observables.push(this.GetAyah(surahNumber, i));\n    }\n    return forkJoin(observables).pipe(map(ayahs => {\n      let text = [];\n      ayahs.forEach(ayah => {\n        text.push(language == 'arabic' ? ayah.arabic2 : ayah.english);\n      });\n      return text;\n    }));\n  }\n  GetReciters() {\n    return this.callServerService.Get(`https://quranapi.pages.dev/api/reciters.json`).pipe(map(value => {\n      return Object.keys(value).map(key => {\n        return {\n          id: key,\n          name: value[key]\n        };\n      });\n    }));\n  }\n  GetAllSuras() {\n    return this.callServerService.Get('https://quranapi.pages.dev/api/surah.json');\n  }\n  GetAyahAudio(reciterId, surahNumber, ayahNumber) {\n    return this.http.get(`https://quranaudio.pages.dev/${reciterId}/${surahNumber}_${ayahNumber}.mp3`, {\n      responseType: 'blob'\n    });\n  }\n  GetAyahsAudio(reciterId, surahNumber, startAyah, endAyah) {\n    const observables = [];\n    for (let ayahNumber = startAyah; ayahNumber <= endAyah; ayahNumber++) {\n      const observable = this.GetAyahAudio(reciterId, surahNumber, ayahNumber);\n      observables.push(observable);\n    }\n    return forkJoin(observables);\n  }\n  static {\n    this.ɵfac = function QuranService_Factory(t) {\n      return new (t || QuranService)(i0.ɵɵinject(i1.CallServerService), i0.ɵɵinject(i2.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: QuranService,\n      factory: QuranService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["fork<PERSON><PERSON>n", "map", "QuranService", "constructor", "callServerService", "http", "Get<PERSON><PERSON><PERSON>", "surah<PERSON>umber", "Get", "GetAyah", "AyahNumber", "GetAyatTexts", "AyahStartNumber", "AyahEndNumber", "language", "observables", "i", "push", "pipe", "ayahs", "text", "for<PERSON>ach", "ayah", "arabic2", "english", "GetReciters", "value", "Object", "keys", "key", "id", "name", "GetAllSuras", "GetAyahAudio", "reciterId", "ayahNumber", "get", "responseType", "GetAyahsAudio", "startAyah", "endAyah", "observable", "i0", "ɵɵinject", "i1", "CallServerService", "i2", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Desktop\\تطبيق ناشر ايات قئانية\\QuranVidGen\\src\\app\\Services\\quran.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { CallServerService } from './call-server.service';\r\nimport { Observable, forkJoin, from, map, of, tap, throwError } from 'rxjs';\r\nimport { Surah } from '../Interfaces/surah';\r\nimport {Reciter} from '../Interfaces/reciter';\r\nimport { HttpClient } from '@angular/common/http';\r\nimport { Ayah } from '../Interfaces/ayah';\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class QuranService {\r\n\r\n  constructor(private callServerService:CallServerService,private http:HttpClient) { }\r\n\r\n\r\n  GetSurah(surahNumber:number):Observable<Surah> | undefined{\r\n    return this.callServerService.Get(`https://quranapi.pages.dev/api/${surahNumber}.json`);\r\n  }\r\n\r\n  GetAyah(surahNumber:number,AyahNumber:number):Observable<Ayah> | undefined{\r\n    return this.callServerService.Get(`https://quranapi.pages.dev/api/${surahNumber}/${AyahNumber}.json`)\r\n  }\r\n\r\n  GetAyatTexts(surahNumber:number,AyahStartNumber:number,AyahEndNumber:number,language:'arabic' | 'english'): Observable<string[]> {\r\n    let observables: Observable<Ayah>[] = [];\r\n    for (let i = AyahStartNumber; i <= AyahEndNumber; i++) {\r\n        observables.push(this.GetAyah(surahNumber,i)!);\r\n    }\r\n\r\n    return forkJoin(observables).pipe(\r\n        map(ayahs => {\r\n            let text: string[] = [];\r\n            ayahs.forEach(ayah => {\r\n                text.push(language == 'arabic' ? ayah.arabic2 : ayah.english);\r\n            });\r\n            return text;\r\n        })\r\n    );\r\n}\r\n\r\n\r\n  GetReciters():Observable<Reciter[]> | undefined{\r\n    return this.callServerService.Get(`https://quranapi.pages.dev/api/reciters.json`).pipe(map(value => {\r\n      return Object.keys(value).map(key => {\r\n        return {\r\n          id: key,\r\n          name: value[key]\r\n        } as Reciter;\r\n      });\r\n    }));\r\n  }\r\n\r\n  GetAllSuras():Observable<Surah[]>{\r\n    return this.callServerService.Get('https://quranapi.pages.dev/api/surah.json');\r\n  }\r\n\r\n  GetAyahAudio(reciterId:number | string,surahNumber:number,ayahNumber:number):Observable<Blob>{\r\n    return this.http.get(`https://quranaudio.pages.dev/${reciterId}/${surahNumber}_${ayahNumber}.mp3`,{responseType:'blob'});\r\n  }\r\n\r\n  GetAyahsAudio(reciterId: number | string, surahNumber: number, startAyah: number, endAyah: number): Observable<Blob[]> {\r\n    const observables: Observable<Blob>[] = [];\r\n\r\n    for (let ayahNumber = startAyah; ayahNumber <= endAyah; ayahNumber++) {\r\n      const observable = this.GetAyahAudio(reciterId,surahNumber,ayahNumber);\r\n      observables.push(observable);\r\n    }\r\n    return forkJoin(observables);\r\n  }\r\n\r\n}\r\n"], "mappings": "AAEA,SAAqBA,QAAQ,EAAQC,GAAG,QAA6B,MAAM;;;;AAS3E,OAAM,MAAOC,YAAY;EAEvBC,YAAoBC,iBAAmC,EAASC,IAAe;IAA3D,KAAAD,iBAAiB,GAAjBA,iBAAiB;IAA2B,KAAAC,IAAI,GAAJA,IAAI;EAAe;EAGnFC,QAAQA,CAACC,WAAkB;IACzB,OAAO,IAAI,CAACH,iBAAiB,CAACI,GAAG,CAAC,kCAAkCD,WAAW,OAAO,CAAC;EACzF;EAEAE,OAAOA,CAACF,WAAkB,EAACG,UAAiB;IAC1C,OAAO,IAAI,CAACN,iBAAiB,CAACI,GAAG,CAAC,kCAAkCD,WAAW,IAAIG,UAAU,OAAO,CAAC;EACvG;EAEAC,YAAYA,CAACJ,WAAkB,EAACK,eAAsB,EAACC,aAAoB,EAACC,QAA6B;IACvG,IAAIC,WAAW,GAAuB,EAAE;IACxC,KAAK,IAAIC,CAAC,GAAGJ,eAAe,EAAEI,CAAC,IAAIH,aAAa,EAAEG,CAAC,EAAE,EAAE;MACnDD,WAAW,CAACE,IAAI,CAAC,IAAI,CAACR,OAAO,CAACF,WAAW,EAACS,CAAC,CAAE,CAAC;;IAGlD,OAAOhB,QAAQ,CAACe,WAAW,CAAC,CAACG,IAAI,CAC7BjB,GAAG,CAACkB,KAAK,IAAG;MACR,IAAIC,IAAI,GAAa,EAAE;MACvBD,KAAK,CAACE,OAAO,CAACC,IAAI,IAAG;QACjBF,IAAI,CAACH,IAAI,CAACH,QAAQ,IAAI,QAAQ,GAAGQ,IAAI,CAACC,OAAO,GAAGD,IAAI,CAACE,OAAO,CAAC;MACjE,CAAC,CAAC;MACF,OAAOJ,IAAI;IACf,CAAC,CAAC,CACL;EACL;EAGEK,WAAWA,CAAA;IACT,OAAO,IAAI,CAACrB,iBAAiB,CAACI,GAAG,CAAC,8CAA8C,CAAC,CAACU,IAAI,CAACjB,GAAG,CAACyB,KAAK,IAAG;MACjG,OAAOC,MAAM,CAACC,IAAI,CAACF,KAAK,CAAC,CAACzB,GAAG,CAAC4B,GAAG,IAAG;QAClC,OAAO;UACLC,EAAE,EAAED,GAAG;UACPE,IAAI,EAAEL,KAAK,CAACG,GAAG;SACL;MACd,CAAC,CAAC;IACJ,CAAC,CAAC,CAAC;EACL;EAEAG,WAAWA,CAAA;IACT,OAAO,IAAI,CAAC5B,iBAAiB,CAACI,GAAG,CAAC,2CAA2C,CAAC;EAChF;EAEAyB,YAAYA,CAACC,SAAyB,EAAC3B,WAAkB,EAAC4B,UAAiB;IACzE,OAAO,IAAI,CAAC9B,IAAI,CAAC+B,GAAG,CAAC,gCAAgCF,SAAS,IAAI3B,WAAW,IAAI4B,UAAU,MAAM,EAAC;MAACE,YAAY,EAAC;IAAM,CAAC,CAAC;EAC1H;EAEAC,aAAaA,CAACJ,SAA0B,EAAE3B,WAAmB,EAAEgC,SAAiB,EAAEC,OAAe;IAC/F,MAAMzB,WAAW,GAAuB,EAAE;IAE1C,KAAK,IAAIoB,UAAU,GAAGI,SAAS,EAAEJ,UAAU,IAAIK,OAAO,EAAEL,UAAU,EAAE,EAAE;MACpE,MAAMM,UAAU,GAAG,IAAI,CAACR,YAAY,CAACC,SAAS,EAAC3B,WAAW,EAAC4B,UAAU,CAAC;MACtEpB,WAAW,CAACE,IAAI,CAACwB,UAAU,CAAC;;IAE9B,OAAOzC,QAAQ,CAACe,WAAW,CAAC;EAC9B;;;uBA1DWb,YAAY,EAAAwC,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,iBAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAZ7C,YAAY;MAAA8C,OAAA,EAAZ9C,YAAY,CAAA+C,IAAA;MAAAC,UAAA,EAFX;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}