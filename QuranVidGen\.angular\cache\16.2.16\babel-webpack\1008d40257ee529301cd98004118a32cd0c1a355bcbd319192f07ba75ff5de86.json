{"ast": null, "code": "export const CORE_VERSION = \"0.12.6\";\nexport const CORE_URL = `https://unpkg.com/@ffmpeg/core@${CORE_VERSION}/dist/esm/ffmpeg-core.js`;\nexport const CORE_MT_URL = `https://unpkg.com/@ffmpeg/core-mt@${CORE_VERSION}/dist/esm/ffmpeg-core.js`;\nexport const CORE_SIZE = {\n  [`https://unpkg.com/@ffmpeg/core@${CORE_VERSION}/dist/esm/ffmpeg-core.js`]: 114673,\n  [`https://unpkg.com/@ffmpeg/core@${CORE_VERSION}/dist/esm/ffmpeg-core.wasm`]: 32129114,\n  [`https://unpkg.com/@ffmpeg/core-mt@${CORE_VERSION}/dist/esm/ffmpeg-core.js`]: 132680,\n  [`https://unpkg.com/@ffmpeg/core-mt@${CORE_VERSION}/dist/esm/ffmpeg-core.wasm`]: 32609891,\n  [`https://unpkg.com/@ffmpeg/core-mt@${CORE_VERSION}/dist/esm/ffmpeg-core.worker.js`]: 2915\n};", "map": {"version": 3, "names": ["CORE_VERSION", "CORE_URL", "CORE_MT_URL", "CORE_SIZE"], "sources": ["C:\\Users\\<USER>\\Desktop\\تطبيق ناشر ايات قئانية\\QuranVidGen\\src\\app\\Services\\constants.ts"], "sourcesContent": ["export const CORE_VERSION = \"0.12.6\";\r\n\r\nexport const CORE_URL = `https://unpkg.com/@ffmpeg/core@${CORE_VERSION}/dist/esm/ffmpeg-core.js`;\r\nexport const CORE_MT_URL = `https://unpkg.com/@ffmpeg/core-mt@${CORE_VERSION}/dist/esm/ffmpeg-core.js`;\r\n\r\nexport const CORE_SIZE:{[key:string]:number} = {\r\n  [`https://unpkg.com/@ffmpeg/core@${CORE_VERSION}/dist/esm/ffmpeg-core.js`]: 114673,\r\n  [`https://unpkg.com/@ffmpeg/core@${CORE_VERSION}/dist/esm/ffmpeg-core.wasm`]: 32129114,\r\n  [`https://unpkg.com/@ffmpeg/core-mt@${CORE_VERSION}/dist/esm/ffmpeg-core.js`]: 132680,\r\n  [`https://unpkg.com/@ffmpeg/core-mt@${CORE_VERSION}/dist/esm/ffmpeg-core.wasm`]: 32609891,\r\n  [`https://unpkg.com/@ffmpeg/core-mt@${CORE_VERSION}/dist/esm/ffmpeg-core.worker.js`]: 2915,\r\n};\r\n"], "mappings": "AAAA,OAAO,MAAMA,YAAY,GAAG,QAAQ;AAEpC,OAAO,MAAMC,QAAQ,GAAG,kCAAkCD,YAAY,0BAA0B;AAChG,OAAO,MAAME,WAAW,GAAG,qCAAqCF,YAAY,0BAA0B;AAEtG,OAAO,MAAMG,SAAS,GAAyB;EAC7C,CAAC,kCAAkCH,YAAY,0BAA0B,GAAG,MAAM;EAClF,CAAC,kCAAkCA,YAAY,4BAA4B,GAAG,QAAQ;EACtF,CAAC,qCAAqCA,YAAY,0BAA0B,GAAG,MAAM;EACrF,CAAC,qCAAqCA,YAAY,4BAA4B,GAAG,QAAQ;EACzF,CAAC,qCAAqCA,YAAY,iCAAiC,GAAG;CACvF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}