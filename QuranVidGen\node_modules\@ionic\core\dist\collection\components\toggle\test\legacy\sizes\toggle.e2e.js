/*!
 * (C) Ionic http://ionicframework.com - MIT License
 */
import { expect } from "@playwright/test";
import { configs, test } from "../../../../../utils/test/playwright/index";
configs().forEach(({ title, screenshot, config }) => {
    test.describe(title('toggle: sizes'), () => {
        test('should not have visual regressions', async ({ page }) => {
            await page.goto(`/src/components/toggle/test/legacy/sizes`, config);
            await page.setIonViewport();
            await expect(page).toHaveScreenshot(screenshot(`toggle-sizes-diff`));
        });
    });
});
