/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
declare function plural(val: number): number;
declare const _default: (string | number | number[] | (string | undefined)[] | typeof plural | (string[] | undefined)[] | {
    AUD: string[];
    BRL: (string | undefined)[];
    BYN: (string | undefined)[];
    CAD: (string | undefined)[];
    CNY: (string | undefined)[];
    ESP: string[];
    MXN: (string | undefined)[];
    PHP: (string | undefined)[];
    THB: string[];
    USD: (string | undefined)[];
    VEF: (string | undefined)[];
    XCD: (string | undefined)[];
    XXX: never[];
} | undefined)[];
export default _default;
