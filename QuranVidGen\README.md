<h1 align="center">Quran Video Generator Hybrid Website & Android Application 📽️📖</h1>

QuranVidGen is an innovative project that leverages the power of Angular 16 and ffmpeg.wasm to generate Quran videos entirely on the client side. Our goal is to make the spiritual experience of the Quran accessible in a visual format, with ease and efficiency.

Built with the latest web technologies, our application ensures a seamless user experience while maintaining high performance. The Android app, created with Ionic and Capacitor, extends this functionality to mobile devices, allowing users to engage with the Quran on-the-go.

Join us in our journey to bring the Quran to life through technology.
Some of the notable features of the project include:

- An android app using Ionic framework and capacitor to carry the experience on your mobile device (fully offline)
- Usage of FFMPEG web assembly to generate the videos directly on the client device without needing backend infrastructure
- fully customized video generation with multiple reciters and background videos and ayat ranges of any surah.
- Usage of primeng UI library for a beautiful and modern interface that reacts to your device's theme (dark/light) automatically.  
- Implementation of Bootstrap 5 , tailwindcss , SCSS with Angular 16 framework to create a responsive UI.

<h2 align="center"> Technologies used 🧪</h2>


<div align="center">
<a href="[https://dotnet.microsoft.com/](https://angular.dev/)">
<img height="32" width="32" src="https://cdn.simpleicons.org/angular/red"/></a>

<a href="[https://getbootstrap.com/](https://webassembly.org/)">
<img height="32" width="32" src="https://cdn.simpleicons.org/webassembly/#654FF0"/></a>

<a href="https://ffmpeg.org/">
<img height="32" width="32" src="https://cdn.simpleicons.org/ffmpeg/#007808"/></a>
<a href="https://primeng.org/">
<img height="32" width="32" src="https://www.primefaces.org/wp-content/uploads/2018/05/primeng-logo.png"/></a>
  
<a href="https://getbootstrap.com/">
<img height="32" width="32" src="https://cdn.simpleicons.org/bootstrap/#7952B3"/></a>

<a href="https://ionicframework.com/">
<img height="32" width="32" src="https://cdn.simpleicons.org/ionic/"/></a>

<a href="https://capacitorjs.com/">
<img height="32" width="32" src="https://cdn.simpleicons.org/capacitor/"/></a>
<a href="[https://sass-lang.com/](https://sass-lang.com/)">
<img height="32" width="32" src="https://cdn.simpleicons.org/sass/#CC6699"/></a>
<a href="https://tailwindcss.com">
<img height="32" width="32" src="https://cdn.simpleicons.org/tailwindcss/#1572B6"/></a>

<a href="https://www.typescriptlang.org/">
<img height="32" width="32" src="https://cdn.simpleicons.org/typescript/"/></a>


</div>
<hr/>
<h2 align="center"> Screenshots 🌠 </h2>

<p align="center">
  <img src="https://i.ibb.co/QnmrjgR/Screenshot-2024-06-19-025645.png" width="80%">
</p>
<p align="center">
  <img src="https://i.ibb.co/1Mx7Xp5/Screenshot-2024-06-19-025053.png" width="80%">
</p>

<p align="center">
  <img src="https://i.ibb.co/n7CNbV1/Screenshot-2024-06-19-025134.png" width="80%">
</p>


<p align="center">
  <img src="https://i.ibb.co/FDb75dW/Screenshot-2024-06-19-030322.png" width="80%">
</p>




</hr>

