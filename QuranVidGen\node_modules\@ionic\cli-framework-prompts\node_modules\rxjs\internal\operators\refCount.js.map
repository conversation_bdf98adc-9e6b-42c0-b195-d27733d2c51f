{"version": 3, "file": "refCount.js", "sources": ["../../src/internal/operators/refCount.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AACA,4CAA2C;AA2D3C,SAAgB,QAAQ;IACtB,OAAO,SAAS,wBAAwB,CAAC,MAAgC;QACvE,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI,gBAAgB,CAAC,MAAM,CAAC,CAAC,CAAC;IACnD,CAAgC,CAAC;AACnC,CAAC;AAJD,4BAIC;AAED;IACE,0BAAoB,WAAqC;QAArC,gBAAW,GAAX,WAAW,CAA0B;IACzD,CAAC;IACD,+BAAI,GAAJ,UAAK,UAAyB,EAAE,MAAW;QAEjC,IAAA,8BAAW,CAAU;QACtB,WAAY,CAAC,SAAS,EAAE,CAAC;QAEhC,IAAM,UAAU,GAAG,IAAI,kBAAkB,CAAC,UAAU,EAAE,WAAW,CAAC,CAAC;QACnE,IAAM,YAAY,GAAG,MAAM,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;QAElD,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE;YACf,UAAW,CAAC,UAAU,GAAG,WAAW,CAAC,OAAO,EAAE,CAAC;SACvD;QAED,OAAO,YAAY,CAAC;IACtB,CAAC;IACH,uBAAC;AAAD,CAAC,AAjBD,IAiBC;AAED;IAAoC,sCAAa;IAI/C,4BAAY,WAA0B,EAClB,WAAqC;QADzD,YAEE,kBAAM,WAAW,CAAC,SACnB;QAFmB,iBAAW,GAAX,WAAW,CAA0B;;IAEzD,CAAC;IAES,yCAAY,GAAtB;QAEU,IAAA,8BAAW,CAAU;QAC7B,IAAI,CAAC,WAAW,EAAE;YAChB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;YACvB,OAAO;SACR;QAED,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;QACxB,IAAM,QAAQ,GAAU,WAAY,CAAC,SAAS,CAAC;QAC/C,IAAI,QAAQ,IAAI,CAAC,EAAE;YACjB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;YACvB,OAAO;SACR;QAEM,WAAY,CAAC,SAAS,GAAG,QAAQ,GAAG,CAAC,CAAC;QAC7C,IAAI,QAAQ,GAAG,CAAC,EAAE;YAChB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;YACvB,OAAO;SACR;QA0BO,IAAA,4BAAU,CAAU;QAC5B,IAAM,gBAAgB,GAAU,WAAY,CAAC,WAAW,CAAC;QACzD,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;QAEvB,IAAI,gBAAgB,IAAI,CAAC,CAAC,UAAU,IAAI,gBAAgB,KAAK,UAAU,CAAC,EAAE;YACxE,gBAAgB,CAAC,WAAW,EAAE,CAAC;SAChC;IACH,CAAC;IACH,yBAAC;AAAD,CAAC,AA9DD,CAAoC,uBAAU,GA8D7C"}