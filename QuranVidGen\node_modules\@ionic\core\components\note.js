/*!
 * (C) Ionic http://ionicframework.com - MIT License
 */
import { proxyCustomElement, HTMLElement, h, Host } from '@stencil/core/internal/client';
import { c as createColorClasses } from './theme.js';
import { b as getIonMode } from './ionic-global.js';

const noteIosCss = ":host{color:var(--color);font-family:var(--ion-font-family, inherit);-webkit-box-sizing:border-box;box-sizing:border-box}:host(.ion-color){color:var(--ion-color-base)}:host{--color:var(--ion-color-step-350, #a6a6a6);font-size:max(14px, 1rem)}";
const IonNoteIosStyle0 = noteIosCss;

const noteMdCss = ":host{color:var(--color);font-family:var(--ion-font-family, inherit);-webkit-box-sizing:border-box;box-sizing:border-box}:host(.ion-color){color:var(--ion-color-base)}:host{--color:var(--ion-color-step-600, #666666);font-size:0.875rem}";
const IonNoteMdStyle0 = noteMdCss;

const Note = /*@__PURE__*/ proxyCustomElement(class Note extends HTMLElement {
    constructor() {
        super();
        this.__registerHost();
        this.__attachShadow();
        this.color = undefined;
    }
    render() {
        const mode = getIonMode(this);
        return (h(Host, { key: '79a17a318ec6e8326c9741b4a9bb4598acdc225e', class: createColorClasses(this.color, {
                [mode]: true,
            }) }, h("slot", { key: '5adeaccfabb4bee7b84ea5c5de804bd255b29255' })));
    }
    static get style() { return {
        ios: IonNoteIosStyle0,
        md: IonNoteMdStyle0
    }; }
}, [33, "ion-note", {
        "color": [513]
    }]);
function defineCustomElement() {
    if (typeof customElements === "undefined") {
        return;
    }
    const components = ["ion-note"];
    components.forEach(tagName => { switch (tagName) {
        case "ion-note":
            if (!customElements.get(tagName)) {
                customElements.define(tagName, Note);
            }
            break;
    } });
}

export { Note as N, defineCustomElement as d };
