{"version": 3, "file": "skipWhile.js", "sources": ["../../../src/internal/operators/skipWhile.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,UAAU,EAAE,MAAM,eAAe,CAAC;AAe3C,MAAM,UAAU,SAAS,CAAI,SAA+C;IAC1E,OAAO,CAAC,MAAqB,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,iBAAiB,CAAC,SAAS,CAAC,CAAC,CAAC;AAClF,CAAC;AAED,MAAM,iBAAiB;IACrB,YAAoB,SAA+C;QAA/C,cAAS,GAAT,SAAS,CAAsC;IACnE,CAAC;IAED,IAAI,CAAC,UAAyB,EAAE,MAAW;QACzC,OAAO,MAAM,CAAC,SAAS,CAAC,IAAI,mBAAmB,CAAC,UAAU,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;IAC/E,CAAC;CACF;AAOD,MAAM,mBAAuB,SAAQ,UAAa;IAIhD,YAAY,WAA0B,EAClB,SAA+C;QACjE,KAAK,CAAC,WAAW,CAAC,CAAC;QADD,cAAS,GAAT,SAAS,CAAsC;QAJ3D,aAAQ,GAAY,IAAI,CAAC;QACzB,UAAK,GAAW,CAAC,CAAC;IAK1B,CAAC;IAES,KAAK,CAAC,KAAQ;QACtB,MAAM,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC;QACrC,IAAI,IAAI,CAAC,QAAQ,EAAE;YACjB,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;SAC9B;QAED,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YAClB,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;SACzB;IACH,CAAC;IAEO,gBAAgB,CAAC,KAAQ;QAC/B,IAAI;YACF,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC;YACnD,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC;SACjC;QAAC,OAAO,GAAG,EAAE;YACZ,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;SAC7B;IACH,CAAC;CACF"}