{"name": "quran-vid-gen", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test"}, "private": true, "dependencies": {"@angular/animations": "^16.2.0", "@angular/common": "^16.2.0", "@angular/compiler": "^16.2.0", "@angular/core": "^16.2.0", "@angular/forms": "^16.2.0", "@angular/platform-browser": "^16.2.0", "@angular/platform-browser-dynamic": "^16.2.0", "@angular/router": "^16.2.0", "@capacitor/android": "6.1.0", "@capacitor/cli": "^6.1.0", "@capacitor/core": "^6.1.0", "@capacitor/dialog": "^6.0.0", "@capacitor/filesystem": "^6.0.0", "@ffmpeg/ffmpeg": "^0.12.10", "@ffmpeg/util": "^0.12.1", "@himeka/capacitor-ffmpeg-kit": "https://github.com/Ra1d7/capacitor-plugin-ffmpeg.git", "@ionic/cli": "^7.2.0", "bootstrap": "^5.2.3", "ionic": "^5.4.16", "ngx-bootstrap": "^11.0.2", "primeicons": "^7.0.0", "primeng": "^16.9.1", "rxjs": "~7.8.0", "tslib": "^2.3.0", "zone.js": "~0.13.0"}, "devDependencies": {"@angular-devkit/build-angular": "^16.2.1", "@angular/cli": "~16.2.1", "@angular/compiler-cli": "^16.2.0", "@types/jasmine": "~4.3.0", "jasmine-core": "~4.6.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "typescript": "~5.1.3"}}