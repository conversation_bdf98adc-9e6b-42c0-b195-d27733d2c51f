{"ast": null, "code": "export * from \"./classes.js\";\nexport * from \"./types.js\";", "map": {"version": 3, "names": [], "sources": ["C:/Users/<USER>/Desktop/تطبيق ناشر ايات قئانية/QuranVidGen/node_modules/@ffmpeg/ffmpeg/dist/esm/index.js"], "sourcesContent": ["export * from \"./classes.js\";\nexport * from \"./types.js\";\n"], "mappings": "AAAA,cAAc,cAAc;AAC5B,cAAc,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}