{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"./Services/quran.service\";\nimport * as i2 from \"@angular/router\";\nexport class AppComponent {\n  constructor(quranService) {\n    this.quranService = quranService;\n  }\n  ngOnInit() {}\n  static {\n    this.ɵfac = function AppComponent_Factory(t) {\n      return new (t || AppComponent)(i0.ɵɵdirectiveInject(i1.QuranService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AppComponent,\n      selectors: [[\"app-root\"]],\n      decls: 1,\n      vars: 0,\n      template: function AppComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelement(0, \"router-outlet\");\n        }\n      },\n      dependencies: [i2.RouterOutlet],\n      styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["AppComponent", "constructor", "quranService", "ngOnInit", "i0", "ɵɵdirectiveInject", "i1", "QuranService", "selectors", "decls", "vars", "template", "AppComponent_Template", "rf", "ctx", "ɵɵelement"], "sources": ["C:\\Users\\<USER>\\Desktop\\تطبيق ناشر ايات قئانية\\QuranVidGen\\src\\app\\app.component.ts", "C:\\Users\\<USER>\\Desktop\\تطبيق ناشر ايات قئانية\\QuranVidGen\\src\\app\\app.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\r\nimport { QuranService } from './Services/quran.service';\r\nimport { catchError, of } from 'rxjs';\r\nimport { Ayah } from './Interfaces/ayah';\r\nimport { Surah } from './Interfaces/surah';\r\n\r\n@Component({\r\n  selector: 'app-root',\r\n  templateUrl: './app.component.html',\r\n  styleUrls: ['./app.component.scss']\r\n})\r\nexport class AppComponent {\r\n  constructor(private quranService:QuranService){}\r\n  ngOnInit(){ }\r\n}\r\n", "<router-outlet></router-outlet>\r\n"], "mappings": ";;;AAWA,OAAM,MAAOA,YAAY;EACvBC,YAAoBC,YAAyB;IAAzB,KAAAA,YAAY,GAAZA,YAAY;EAAe;EAC/CC,QAAQA,CAAA,GAAI;;;uBAFDH,YAAY,EAAAI,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,YAAA;IAAA;EAAA;;;YAAZP,YAAY;MAAAQ,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,QAAA,WAAAC,sBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCXzBT,EAAA,CAAAW,SAAA,oBAA+B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}