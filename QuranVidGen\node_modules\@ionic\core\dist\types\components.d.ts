/* eslint-disable */
/* tslint:disable */
/**
 * This is an autogenerated file created by the Stencil compiler.
 * It contains typing information for all components that exist in this project.
 */
import { HTMLStencilElement, JSXBase } from "./stencil-public-runtime";
import { AccordionGroupChangeEventDetail } from "./components/accordion-group/accordion-group-interface";
import { AnimationBuilder, AutocompleteTypes, Color, ComponentProps, ComponentRef, FrameworkDelegate, StyleEventDetail, TextFieldTypes } from "./interface";
import { ActionSheetButton } from "./components/action-sheet/action-sheet-interface";
import { OverlayEventDetail } from "./utils/overlays-interface";
import { IonicSafeString } from "./utils/sanitization";
import { AlertButton, AlertInput } from "./components/alert/alert-interface";
import { RouteID, RouterDirection, RouterEventDetail, RouteWrite } from "./components/router/utils/interface";
import { BreadcrumbCollapsedClickEventDetail } from "./components/breadcrumb/breadcrumb-interface";
import { CheckboxChangeEventDetail } from "./components/checkbox/checkbox-interface";
import { ScrollBaseDetail, ScrollDetail } from "./components/content/content-interface";
import { DatetimeChangeEventDetail, DatetimeHighlight, DatetimeHighlightCallback, DatetimeHourCycle, DatetimePresentation, FormatOptions, TitleSelectedDatesFormatter } from "./components/datetime/datetime-interface";
import { SpinnerTypes } from "./components/spinner/spinner-configs";
import { InputChangeEventDetail, InputInputEventDetail } from "./components/input/input-interface";
import { CounterFormatter } from "./components/item/item-interface";
import { MenuChangeEventDetail, Side } from "./components/menu/menu-interface";
import { ModalBreakpointChangeEventDetail, ModalHandleBehavior } from "./components/modal/modal-interface";
import { NavComponent, NavComponentWithProps, NavOptions, RouterOutletOptions, SwipeGestureHandler, TransitionDoneFn, TransitionInstruction } from "./components/nav/nav-interface";
import { ViewController } from "./components/nav/view-controller";
import { PickerButton, PickerColumn } from "./components/picker/picker-interface";
import { PickerColumnItem } from "./components/picker-column-internal/picker-column-internal-interfaces";
import { PickerInternalChangeEventDetail } from "./components/picker-internal/picker-internal-interfaces";
import { PopoverSize, PositionAlign, PositionReference, PositionSide, TriggerAction } from "./components/popover/popover-interface";
import { RadioGroupChangeEventDetail, RadioGroupCompareFn } from "./components/radio-group/radio-group-interface";
import { PinFormatter, RangeChangeEventDetail, RangeKnobMoveEndEventDetail, RangeKnobMoveStartEventDetail, RangeValue } from "./components/range/range-interface";
import { RefresherEventDetail } from "./components/refresher/refresher-interface";
import { ItemReorderEventDetail } from "./components/reorder-group/reorder-group-interface";
import { NavigationHookCallback } from "./components/route/route-interface";
import { SearchbarChangeEventDetail, SearchbarInputEventDetail } from "./components/searchbar/searchbar-interface";
import { SegmentChangeEventDetail, SegmentValue } from "./components/segment/segment-interface";
import { SegmentButtonLayout } from "./components/segment-button/segment-button-interface";
import { SelectChangeEventDetail, SelectCompareFn, SelectInterface } from "./components/select/select-interface";
import { SelectPopoverOption } from "./components/select-popover/select-popover-interface";
import { TabBarChangedEventDetail, TabButtonClickEventDetail, TabButtonLayout } from "./components/tab-bar/tab-bar-interface";
import { TextareaChangeEventDetail, TextareaInputEventDetail } from "./components/textarea/textarea-interface";
import { ToastButton, ToastDismissOptions, ToastLayout, ToastPosition, ToastPresentOptions, ToastSwipeGestureDirection } from "./components/toast/toast-interface";
import { ToggleChangeEventDetail } from "./components/toggle/toggle-interface";
export { AccordionGroupChangeEventDetail } from "./components/accordion-group/accordion-group-interface";
export { AnimationBuilder, AutocompleteTypes, Color, ComponentProps, ComponentRef, FrameworkDelegate, StyleEventDetail, TextFieldTypes } from "./interface";
export { ActionSheetButton } from "./components/action-sheet/action-sheet-interface";
export { OverlayEventDetail } from "./utils/overlays-interface";
export { IonicSafeString } from "./utils/sanitization";
export { AlertButton, AlertInput } from "./components/alert/alert-interface";
export { RouteID, RouterDirection, RouterEventDetail, RouteWrite } from "./components/router/utils/interface";
export { BreadcrumbCollapsedClickEventDetail } from "./components/breadcrumb/breadcrumb-interface";
export { CheckboxChangeEventDetail } from "./components/checkbox/checkbox-interface";
export { ScrollBaseDetail, ScrollDetail } from "./components/content/content-interface";
export { DatetimeChangeEventDetail, DatetimeHighlight, DatetimeHighlightCallback, DatetimeHourCycle, DatetimePresentation, FormatOptions, TitleSelectedDatesFormatter } from "./components/datetime/datetime-interface";
export { SpinnerTypes } from "./components/spinner/spinner-configs";
export { InputChangeEventDetail, InputInputEventDetail } from "./components/input/input-interface";
export { CounterFormatter } from "./components/item/item-interface";
export { MenuChangeEventDetail, Side } from "./components/menu/menu-interface";
export { ModalBreakpointChangeEventDetail, ModalHandleBehavior } from "./components/modal/modal-interface";
export { NavComponent, NavComponentWithProps, NavOptions, RouterOutletOptions, SwipeGestureHandler, TransitionDoneFn, TransitionInstruction } from "./components/nav/nav-interface";
export { ViewController } from "./components/nav/view-controller";
export { PickerButton, PickerColumn } from "./components/picker/picker-interface";
export { PickerColumnItem } from "./components/picker-column-internal/picker-column-internal-interfaces";
export { PickerInternalChangeEventDetail } from "./components/picker-internal/picker-internal-interfaces";
export { PopoverSize, PositionAlign, PositionReference, PositionSide, TriggerAction } from "./components/popover/popover-interface";
export { RadioGroupChangeEventDetail, RadioGroupCompareFn } from "./components/radio-group/radio-group-interface";
export { PinFormatter, RangeChangeEventDetail, RangeKnobMoveEndEventDetail, RangeKnobMoveStartEventDetail, RangeValue } from "./components/range/range-interface";
export { RefresherEventDetail } from "./components/refresher/refresher-interface";
export { ItemReorderEventDetail } from "./components/reorder-group/reorder-group-interface";
export { NavigationHookCallback } from "./components/route/route-interface";
export { SearchbarChangeEventDetail, SearchbarInputEventDetail } from "./components/searchbar/searchbar-interface";
export { SegmentChangeEventDetail, SegmentValue } from "./components/segment/segment-interface";
export { SegmentButtonLayout } from "./components/segment-button/segment-button-interface";
export { SelectChangeEventDetail, SelectCompareFn, SelectInterface } from "./components/select/select-interface";
export { SelectPopoverOption } from "./components/select-popover/select-popover-interface";
export { TabBarChangedEventDetail, TabButtonClickEventDetail, TabButtonLayout } from "./components/tab-bar/tab-bar-interface";
export { TextareaChangeEventDetail, TextareaInputEventDetail } from "./components/textarea/textarea-interface";
export { ToastButton, ToastDismissOptions, ToastLayout, ToastPosition, ToastPresentOptions, ToastSwipeGestureDirection } from "./components/toast/toast-interface";
export { ToggleChangeEventDetail } from "./components/toggle/toggle-interface";
export namespace Components {
    interface IonAccordion {
        /**
          * If `true`, the accordion cannot be interacted with.
         */
        "disabled": boolean;
        /**
          * The mode determines which platform styles to use.
         */
        "mode"?: "ios" | "md";
        /**
          * If `true`, the accordion cannot be interacted with, but does not alter the opacity.
         */
        "readonly": boolean;
        /**
          * The toggle icon to use. This icon will be rotated when the accordion is expanded or collapsed.
         */
        "toggleIcon": string;
        /**
          * The slot inside of `ion-item` to place the toggle icon. Defaults to `"end"`.
         */
        "toggleIconSlot": 'start' | 'end';
        /**
          * The value of the accordion. Defaults to an autogenerated value.
         */
        "value": string;
    }
    interface IonAccordionGroup {
        /**
          * If `true`, all accordions inside of the accordion group will animate when expanding or collapsing.
         */
        "animated": boolean;
        /**
          * If `true`, the accordion group cannot be interacted with.
         */
        "disabled": boolean;
        /**
          * Describes the expansion behavior for each accordion. Possible values are `"compact"` and `"inset"`. Defaults to `"compact"`.
         */
        "expand": 'compact' | 'inset';
        "getAccordions": () => Promise<HTMLIonAccordionElement[]>;
        /**
          * The mode determines which platform styles to use.
         */
        "mode"?: "ios" | "md";
        /**
          * If `true`, the accordion group can have multiple accordion components expanded at the same time.
         */
        "multiple"?: boolean;
        /**
          * If `true`, the accordion group cannot be interacted with, but does not alter the opacity.
         */
        "readonly": boolean;
        /**
          * This method is used to ensure that the value of ion-accordion-group is being set in a valid way. This method should only be called in response to a user generated action.
         */
        "requestAccordionToggle": (accordionValue: string | undefined, accordionExpand: boolean) => Promise<void>;
        /**
          * The value of the accordion group. This controls which accordions are expanded. This should be an array of strings only when `multiple="true"`
         */
        "value"?: string | string[] | null;
    }
    interface IonActionSheet {
        /**
          * If `true`, the action sheet will animate.
         */
        "animated": boolean;
        /**
          * If `true`, the action sheet will be dismissed when the backdrop is clicked.
         */
        "backdropDismiss": boolean;
        /**
          * An array of buttons for the action sheet.
         */
        "buttons": (ActionSheetButton | string)[];
        /**
          * Additional classes to apply for custom CSS. If multiple classes are provided they should be separated by spaces.
         */
        "cssClass"?: string | string[];
        "delegate"?: FrameworkDelegate;
        /**
          * Dismiss the action sheet overlay after it has been presented.
          * @param data Any data to emit in the dismiss events.
          * @param role The role of the element that is dismissing the action sheet. This can be useful in a button handler for determining which button was clicked to dismiss the action sheet. Some examples include: ``"cancel"`, `"destructive"`, "selected"`, and `"backdrop"`.  This is a no-op if the overlay has not been presented yet. If you want to remove an overlay from the DOM that was never presented, use the [remove](https://developer.mozilla.org/en-US/docs/Web/API/Element/remove) method.
         */
        "dismiss": (data?: any, role?: string) => Promise<boolean>;
        /**
          * Animation to use when the action sheet is presented.
         */
        "enterAnimation"?: AnimationBuilder;
        "hasController": boolean;
        /**
          * Title for the action sheet.
         */
        "header"?: string;
        /**
          * Additional attributes to pass to the action sheet.
         */
        "htmlAttributes"?: { [key: string]: any };
        /**
          * If `true`, the action sheet will open. If `false`, the action sheet will close. Use this if you need finer grained control over presentation, otherwise just use the actionSheetController or the `trigger` property. Note: `isOpen` will not automatically be set back to `false` when the action sheet dismisses. You will need to do that in your code.
         */
        "isOpen": boolean;
        /**
          * If `true`, the keyboard will be automatically dismissed when the overlay is presented.
         */
        "keyboardClose": boolean;
        /**
          * Animation to use when the action sheet is dismissed.
         */
        "leaveAnimation"?: AnimationBuilder;
        /**
          * The mode determines which platform styles to use.
         */
        "mode"?: "ios" | "md";
        /**
          * Returns a promise that resolves when the action sheet did dismiss.
         */
        "onDidDismiss": <T = any>() => Promise<OverlayEventDetail<T>>;
        /**
          * Returns a promise that resolves when the action sheet will dismiss.
         */
        "onWillDismiss": <T = any>() => Promise<OverlayEventDetail<T>>;
        "overlayIndex": number;
        /**
          * Present the action sheet overlay after it has been created.
         */
        "present": () => Promise<void>;
        /**
          * Subtitle for the action sheet.
         */
        "subHeader"?: string;
        /**
          * If `true`, the action sheet will be translucent. Only applies when the mode is `"ios"` and the device supports [`backdrop-filter`](https://developer.mozilla.org/en-US/docs/Web/CSS/backdrop-filter#Browser_compatibility).
         */
        "translucent": boolean;
        /**
          * An ID corresponding to the trigger element that causes the action sheet to open when clicked.
         */
        "trigger": string | undefined;
    }
    interface IonAlert {
        /**
          * If `true`, the alert will animate.
         */
        "animated": boolean;
        /**
          * If `true`, the alert will be dismissed when the backdrop is clicked.
         */
        "backdropDismiss": boolean;
        /**
          * Array of buttons to be added to the alert.
         */
        "buttons": (AlertButton | string)[];
        /**
          * Additional classes to apply for custom CSS. If multiple classes are provided they should be separated by spaces.
         */
        "cssClass"?: string | string[];
        "delegate"?: FrameworkDelegate;
        /**
          * Dismiss the alert overlay after it has been presented.
          * @param data Any data to emit in the dismiss events.
          * @param role The role of the element that is dismissing the alert. This can be useful in a button handler for determining which button was clicked to dismiss the alert. Some examples include: ``"cancel"`, `"destructive"`, "selected"`, and `"backdrop"`.  This is a no-op if the overlay has not been presented yet. If you want to remove an overlay from the DOM that was never presented, use the [remove](https://developer.mozilla.org/en-US/docs/Web/API/Element/remove) method.
         */
        "dismiss": (data?: any, role?: string) => Promise<boolean>;
        /**
          * Animation to use when the alert is presented.
         */
        "enterAnimation"?: AnimationBuilder;
        "hasController": boolean;
        /**
          * The main title in the heading of the alert.
         */
        "header"?: string;
        /**
          * Additional attributes to pass to the alert.
         */
        "htmlAttributes"?: { [key: string]: any };
        /**
          * Array of input to show in the alert.
         */
        "inputs": AlertInput[];
        /**
          * If `true`, the alert will open. If `false`, the alert will close. Use this if you need finer grained control over presentation, otherwise just use the alertController or the `trigger` property. Note: `isOpen` will not automatically be set back to `false` when the alert dismisses. You will need to do that in your code.
         */
        "isOpen": boolean;
        /**
          * If `true`, the keyboard will be automatically dismissed when the overlay is presented.
         */
        "keyboardClose": boolean;
        /**
          * Animation to use when the alert is dismissed.
         */
        "leaveAnimation"?: AnimationBuilder;
        /**
          * The main message to be displayed in the alert. `message` can accept either plaintext or HTML as a string. To display characters normally reserved for HTML, they must be escaped. For example `<Ionic>` would become `&lt;Ionic&gt;`  For more information: [Security Documentation](https://ionicframework.com/docs/faq/security)  This property accepts custom HTML as a string. Content is parsed as plaintext by default. `innerHTMLTemplatesEnabled` must be set to `true` in the Ionic config before custom HTML can be used.
         */
        "message"?: string | IonicSafeString;
        /**
          * The mode determines which platform styles to use.
         */
        "mode"?: "ios" | "md";
        /**
          * Returns a promise that resolves when the alert did dismiss.
         */
        "onDidDismiss": <T = any>() => Promise<OverlayEventDetail<T>>;
        /**
          * Returns a promise that resolves when the alert will dismiss.
         */
        "onWillDismiss": <T = any>() => Promise<OverlayEventDetail<T>>;
        "overlayIndex": number;
        /**
          * Present the alert overlay after it has been created.
         */
        "present": () => Promise<void>;
        /**
          * The subtitle in the heading of the alert. Displayed under the title.
         */
        "subHeader"?: string;
        /**
          * If `true`, the alert will be translucent. Only applies when the mode is `"ios"` and the device supports [`backdrop-filter`](https://developer.mozilla.org/en-US/docs/Web/CSS/backdrop-filter#Browser_compatibility).
         */
        "translucent": boolean;
        /**
          * An ID corresponding to the trigger element that causes the alert to open when clicked.
         */
        "trigger": string | undefined;
    }
    interface IonApp {
        "setFocus": (elements: HTMLElement[]) => Promise<void>;
    }
    interface IonAvatar {
    }
    interface IonBackButton {
        /**
          * The color to use from your application's color palette. Default options are: `"primary"`, `"secondary"`, `"tertiary"`, `"success"`, `"warning"`, `"danger"`, `"light"`, `"medium"`, and `"dark"`. For more information on colors, see [theming](/docs/theming/basics).
         */
        "color"?: Color;
        /**
          * The url to navigate back to by default when there is no history.
         */
        "defaultHref"?: string;
        /**
          * If `true`, the user cannot interact with the button.
         */
        "disabled": boolean;
        /**
          * The built-in named SVG icon name or the exact `src` of an SVG file to use for the back button.
         */
        "icon"?: string | null;
        /**
          * The mode determines which platform styles to use.
         */
        "mode"?: "ios" | "md";
        /**
          * When using a router, it specifies the transition animation when navigating to another page.
         */
        "routerAnimation": AnimationBuilder | undefined;
        /**
          * The text to display in the back button.
         */
        "text"?: string | null;
        /**
          * The type of the button.
         */
        "type": 'submit' | 'reset' | 'button';
    }
    interface IonBackdrop {
        /**
          * If `true`, the backdrop will stop propagation on tap.
         */
        "stopPropagation": boolean;
        /**
          * If `true`, the backdrop will can be clicked and will emit the `ionBackdropTap` event.
         */
        "tappable": boolean;
        /**
          * If `true`, the backdrop will be visible.
         */
        "visible": boolean;
    }
    interface IonBadge {
        /**
          * The color to use from your application's color palette. Default options are: `"primary"`, `"secondary"`, `"tertiary"`, `"success"`, `"warning"`, `"danger"`, `"light"`, `"medium"`, and `"dark"`. For more information on colors, see [theming](/docs/theming/basics).
         */
        "color"?: Color;
        /**
          * The mode determines which platform styles to use.
         */
        "mode"?: "ios" | "md";
    }
    interface IonBreadcrumb {
        /**
          * If `true`, the breadcrumb will take on a different look to show that it is the currently active breadcrumb. Defaults to `true` for the last breadcrumb if it is not set on any.
         */
        "active": boolean;
        "collapsed": boolean;
        /**
          * The color to use from your application's color palette. Default options are: `"primary"`, `"secondary"`, `"tertiary"`, `"success"`, `"warning"`, `"danger"`, `"light"`, `"medium"`, and `"dark"`. For more information on colors, see [theming](/docs/theming/basics).
         */
        "color"?: Color;
        /**
          * If `true`, the user cannot interact with the breadcrumb.
         */
        "disabled": boolean;
        /**
          * This attribute instructs browsers to download a URL instead of navigating to it, so the user will be prompted to save it as a local file. If the attribute has a value, it is used as the pre-filled file name in the Save prompt (the user can still change the file name if they want).
         */
        "download": string | undefined;
        /**
          * Contains a URL or a URL fragment that the hyperlink points to. If this property is set, an anchor tag will be rendered.
         */
        "href": string | undefined;
        "last": boolean;
        /**
          * The mode determines which platform styles to use.
         */
        "mode"?: "ios" | "md";
        /**
          * Specifies the relationship of the target object to the link object. The value is a space-separated list of [link types](https://developer.mozilla.org/en-US/docs/Web/HTML/Link_types).
         */
        "rel": string | undefined;
        /**
          * When using a router, it specifies the transition animation when navigating to another page using `href`.
         */
        "routerAnimation": AnimationBuilder | undefined;
        /**
          * When using a router, it specifies the transition direction when navigating to another page using `href`.
         */
        "routerDirection": RouterDirection;
        /**
          * If true, show a separator between this breadcrumb and the next. Defaults to `true` for all breadcrumbs except the last.
         */
        "separator"?: boolean | undefined;
        "showCollapsedIndicator": boolean;
        /**
          * Specifies where to display the linked URL. Only applies when an `href` is provided. Special keywords: `"_blank"`, `"_self"`, `"_parent"`, `"_top"`.
         */
        "target": string | undefined;
    }
    interface IonBreadcrumbs {
        /**
          * The color to use from your application's color palette. Default options are: `"primary"`, `"secondary"`, `"tertiary"`, `"success"`, `"warning"`, `"danger"`, `"light"`, `"medium"`, and `"dark"`. For more information on colors, see [theming](/docs/theming/basics).
         */
        "color"?: Color;
        /**
          * The number of breadcrumbs to show after the collapsed indicator. If `itemsBeforeCollapse` + `itemsAfterCollapse` is greater than `maxItems`, the breadcrumbs will not be collapsed.
         */
        "itemsAfterCollapse": number;
        /**
          * The number of breadcrumbs to show before the collapsed indicator. If `itemsBeforeCollapse` + `itemsAfterCollapse` is greater than `maxItems`, the breadcrumbs will not be collapsed.
         */
        "itemsBeforeCollapse": number;
        /**
          * The maximum number of breadcrumbs to show before collapsing.
         */
        "maxItems"?: number;
        /**
          * The mode determines which platform styles to use.
         */
        "mode"?: "ios" | "md";
    }
    interface IonButton {
        /**
          * The type of button.
         */
        "buttonType": string;
        /**
          * The color to use from your application's color palette. Default options are: `"primary"`, `"secondary"`, `"tertiary"`, `"success"`, `"warning"`, `"danger"`, `"light"`, `"medium"`, and `"dark"`. For more information on colors, see [theming](/docs/theming/basics).
         */
        "color"?: Color;
        /**
          * If `true`, the user cannot interact with the button.
         */
        "disabled": boolean;
        /**
          * This attribute instructs browsers to download a URL instead of navigating to it, so the user will be prompted to save it as a local file. If the attribute has a value, it is used as the pre-filled file name in the Save prompt (the user can still change the file name if they want).
         */
        "download": string | undefined;
        /**
          * Set to `"block"` for a full-width button or to `"full"` for a full-width button with square corners and no left or right borders.
         */
        "expand"?: 'full' | 'block';
        /**
          * Set to `"clear"` for a transparent button that resembles a flat button, to `"outline"` for a transparent button with a border, or to `"solid"` for a button with a filled background. The default fill is `"solid"` except inside of a toolbar, where the default is `"clear"`.
         */
        "fill"?: 'clear' | 'outline' | 'solid' | 'default';
        /**
          * The HTML form element or form element id. Used to submit a form when the button is not a child of the form.
         */
        "form"?: string | HTMLFormElement;
        /**
          * Contains a URL or a URL fragment that the hyperlink points to. If this property is set, an anchor tag will be rendered.
         */
        "href": string | undefined;
        /**
          * The mode determines which platform styles to use.
         */
        "mode"?: "ios" | "md";
        /**
          * Specifies the relationship of the target object to the link object. The value is a space-separated list of [link types](https://developer.mozilla.org/en-US/docs/Web/HTML/Link_types).
         */
        "rel": string | undefined;
        /**
          * When using a router, it specifies the transition animation when navigating to another page using `href`.
         */
        "routerAnimation": AnimationBuilder | undefined;
        /**
          * When using a router, it specifies the transition direction when navigating to another page using `href`.
         */
        "routerDirection": RouterDirection;
        /**
          * Set to `"round"` for a button with more rounded corners.
         */
        "shape"?: 'round';
        /**
          * Set to `"small"` for a button with less height and padding, to `"default"` for a button with the default height and padding, or to `"large"` for a button with more height and padding. By default the size is unset, unless the button is inside of an item, where the size is `"small"` by default. Set the size to `"default"` inside of an item to make it a standard size button.
         */
        "size"?: 'small' | 'default' | 'large';
        /**
          * If `true`, activates a button with a heavier font weight.
         */
        "strong": boolean;
        /**
          * Specifies where to display the linked URL. Only applies when an `href` is provided. Special keywords: `"_blank"`, `"_self"`, `"_parent"`, `"_top"`.
         */
        "target": string | undefined;
        /**
          * The type of the button.
         */
        "type": 'submit' | 'reset' | 'button';
    }
    interface IonButtons {
        /**
          * If true, buttons will disappear when its parent toolbar has fully collapsed if the toolbar is not the first toolbar. If the toolbar is the first toolbar, the buttons will be hidden and will only be shown once all toolbars have fully collapsed.  Only applies in `ios` mode with `collapse` set to `true` on `ion-header`.  Typically used for [Collapsible Large Titles](https://ionicframework.com/docs/api/title#collapsible-large-titles)
         */
        "collapse": boolean;
    }
    interface IonCard {
        /**
          * If `true`, a button tag will be rendered and the card will be tappable.
         */
        "button": boolean;
        /**
          * The color to use from your application's color palette. Default options are: `"primary"`, `"secondary"`, `"tertiary"`, `"success"`, `"warning"`, `"danger"`, `"light"`, `"medium"`, and `"dark"`. For more information on colors, see [theming](/docs/theming/basics).
         */
        "color"?: Color;
        /**
          * If `true`, the user cannot interact with the card.
         */
        "disabled": boolean;
        /**
          * This attribute instructs browsers to download a URL instead of navigating to it, so the user will be prompted to save it as a local file. If the attribute has a value, it is used as the pre-filled file name in the Save prompt (the user can still change the file name if they want).
         */
        "download": string | undefined;
        /**
          * Contains a URL or a URL fragment that the hyperlink points to. If this property is set, an anchor tag will be rendered.
         */
        "href": string | undefined;
        /**
          * The mode determines which platform styles to use.
         */
        "mode"?: "ios" | "md";
        /**
          * Specifies the relationship of the target object to the link object. The value is a space-separated list of [link types](https://developer.mozilla.org/en-US/docs/Web/HTML/Link_types).
         */
        "rel": string | undefined;
        /**
          * When using a router, it specifies the transition animation when navigating to another page using `href`.
         */
        "routerAnimation": AnimationBuilder | undefined;
        /**
          * When using a router, it specifies the transition direction when navigating to another page using `href`.
         */
        "routerDirection": RouterDirection;
        /**
          * Specifies where to display the linked URL. Only applies when an `href` is provided. Special keywords: `"_blank"`, `"_self"`, `"_parent"`, `"_top"`.
         */
        "target": string | undefined;
        /**
          * The type of the button. Only used when an `onclick` or `button` property is present.
         */
        "type": 'submit' | 'reset' | 'button';
    }
    interface IonCardContent {
        /**
          * The mode determines which platform styles to use.
         */
        "mode"?: "ios" | "md";
    }
    interface IonCardHeader {
        /**
          * The color to use from your application's color palette. Default options are: `"primary"`, `"secondary"`, `"tertiary"`, `"success"`, `"warning"`, `"danger"`, `"light"`, `"medium"`, and `"dark"`. For more information on colors, see [theming](/docs/theming/basics).
         */
        "color"?: Color;
        /**
          * The mode determines which platform styles to use.
         */
        "mode"?: "ios" | "md";
        /**
          * If `true`, the card header will be translucent. Only applies when the mode is `"ios"` and the device supports [`backdrop-filter`](https://developer.mozilla.org/en-US/docs/Web/CSS/backdrop-filter#Browser_compatibility).
         */
        "translucent": boolean;
    }
    interface IonCardSubtitle {
        /**
          * The color to use from your application's color palette. Default options are: `"primary"`, `"secondary"`, `"tertiary"`, `"success"`, `"warning"`, `"danger"`, `"light"`, `"medium"`, and `"dark"`. For more information on colors, see [theming](/docs/theming/basics).
         */
        "color"?: Color;
        /**
          * The mode determines which platform styles to use.
         */
        "mode"?: "ios" | "md";
    }
    interface IonCardTitle {
        /**
          * The color to use from your application's color palette. Default options are: `"primary"`, `"secondary"`, `"tertiary"`, `"success"`, `"warning"`, `"danger"`, `"light"`, `"medium"`, and `"dark"`. For more information on colors, see [theming](/docs/theming/basics).
         */
        "color"?: Color;
        /**
          * The mode determines which platform styles to use.
         */
        "mode"?: "ios" | "md";
    }
    interface IonCheckbox {
        /**
          * How to control the alignment of the checkbox and label on the cross axis. `"start"`: The label and control will appear on the left of the cross axis in LTR, and on the right side in RTL. `"center"`: The label and control will appear at the center of the cross axis in both LTR and RTL.
         */
        "alignment": 'start' | 'center';
        /**
          * If `true`, the checkbox is selected.
         */
        "checked": boolean;
        /**
          * The color to use from your application's color palette. Default options are: `"primary"`, `"secondary"`, `"tertiary"`, `"success"`, `"warning"`, `"danger"`, `"light"`, `"medium"`, and `"dark"`. For more information on colors, see [theming](/docs/theming/basics).
         */
        "color"?: Color;
        /**
          * If `true`, the user cannot interact with the checkbox.
         */
        "disabled": boolean;
        /**
          * If `true`, the checkbox will visually appear as indeterminate.
         */
        "indeterminate": boolean;
        /**
          * How to pack the label and checkbox within a line. `"start"`: The label and checkbox will appear on the left in LTR and on the right in RTL. `"end"`: The label and checkbox will appear on the right in LTR and on the left in RTL. `"space-between"`: The label and checkbox will appear on opposite ends of the line with space between the two elements.
         */
        "justify": 'start' | 'end' | 'space-between';
        /**
          * Where to place the label relative to the checkbox. `"start"`: The label will appear to the left of the checkbox in LTR and to the right in RTL. `"end"`: The label will appear to the right of the checkbox in LTR and to the left in RTL. `"fixed"`: The label has the same behavior as `"start"` except it also has a fixed width. Long text will be truncated with ellipses ("..."). `"stacked"`: The label will appear above the checkbox regardless of the direction. The alignment of the label can be controlled with the `alignment` property.
         */
        "labelPlacement": 'start' | 'end' | 'fixed' | 'stacked';
        /**
          * Set the `legacy` property to `true` to forcibly use the legacy form control markup. Ionic will only opt checkboxes in to the modern form markup when they are using either the `aria-label` attribute or have text in the default slot. As a result, the `legacy` property should only be used as an escape hatch when you want to avoid this automatic opt-in behavior.  Note that this property will be removed in an upcoming major release of Ionic, and all form components will be opted-in to using the modern form markup.
         */
        "legacy"?: boolean;
        /**
          * The mode determines which platform styles to use.
         */
        "mode"?: "ios" | "md";
        /**
          * The name of the control, which is submitted with the form data.
         */
        "name": string;
        /**
          * The value of the checkbox does not mean if it's checked or not, use the `checked` property for that.  The value of a checkbox is analogous to the value of an `<input type="checkbox">`, it's only used when the checkbox participates in a native `<form>`.
         */
        "value": any | null;
    }
    interface IonChip {
        /**
          * The color to use from your application's color palette. Default options are: `"primary"`, `"secondary"`, `"tertiary"`, `"success"`, `"warning"`, `"danger"`, `"light"`, `"medium"`, and `"dark"`. For more information on colors, see [theming](/docs/theming/basics).
         */
        "color"?: Color;
        /**
          * If `true`, the user cannot interact with the chip.
         */
        "disabled": boolean;
        /**
          * The mode determines which platform styles to use.
         */
        "mode"?: "ios" | "md";
        /**
          * Display an outline style button.
         */
        "outline": boolean;
    }
    interface IonCol {
        /**
          * The amount to offset the column, in terms of how many columns it should shift to the end of the total available.
         */
        "offset"?: string;
        /**
          * The amount to offset the column for lg screens, in terms of how many columns it should shift to the end of the total available.
         */
        "offsetLg"?: string;
        /**
          * The amount to offset the column for md screens, in terms of how many columns it should shift to the end of the total available.
         */
        "offsetMd"?: string;
        /**
          * The amount to offset the column for sm screens, in terms of how many columns it should shift to the end of the total available.
         */
        "offsetSm"?: string;
        /**
          * The amount to offset the column for xl screens, in terms of how many columns it should shift to the end of the total available.
         */
        "offsetXl"?: string;
        /**
          * The amount to offset the column for xs screens, in terms of how many columns it should shift to the end of the total available.
         */
        "offsetXs"?: string;
        /**
          * The amount to pull the column, in terms of how many columns it should shift to the start of the total available.
         */
        "pull"?: string;
        /**
          * The amount to pull the column for lg screens, in terms of how many columns it should shift to the start of the total available.
         */
        "pullLg"?: string;
        /**
          * The amount to pull the column for md screens, in terms of how many columns it should shift to the start of the total available.
         */
        "pullMd"?: string;
        /**
          * The amount to pull the column for sm screens, in terms of how many columns it should shift to the start of the total available.
         */
        "pullSm"?: string;
        /**
          * The amount to pull the column for xl screens, in terms of how many columns it should shift to the start of the total available.
         */
        "pullXl"?: string;
        /**
          * The amount to pull the column for xs screens, in terms of how many columns it should shift to the start of the total available.
         */
        "pullXs"?: string;
        /**
          * The amount to push the column, in terms of how many columns it should shift to the end of the total available.
         */
        "push"?: string;
        /**
          * The amount to push the column for lg screens, in terms of how many columns it should shift to the end of the total available.
         */
        "pushLg"?: string;
        /**
          * The amount to push the column for md screens, in terms of how many columns it should shift to the end of the total available.
         */
        "pushMd"?: string;
        /**
          * The amount to push the column for sm screens, in terms of how many columns it should shift to the end of the total available.
         */
        "pushSm"?: string;
        /**
          * The amount to push the column for xl screens, in terms of how many columns it should shift to the end of the total available.
         */
        "pushXl"?: string;
        /**
          * The amount to push the column for xs screens, in terms of how many columns it should shift to the end of the total available.
         */
        "pushXs"?: string;
        /**
          * The size of the column, in terms of how many columns it should take up out of the total available. If `"auto"` is passed, the column will be the size of its content.
         */
        "size"?: string;
        /**
          * The size of the column for lg screens, in terms of how many columns it should take up out of the total available. If `"auto"` is passed, the column will be the size of its content.
         */
        "sizeLg"?: string;
        /**
          * The size of the column for md screens, in terms of how many columns it should take up out of the total available. If `"auto"` is passed, the column will be the size of its content.
         */
        "sizeMd"?: string;
        /**
          * The size of the column for sm screens, in terms of how many columns it should take up out of the total available. If `"auto"` is passed, the column will be the size of its content.
         */
        "sizeSm"?: string;
        /**
          * The size of the column for xl screens, in terms of how many columns it should take up out of the total available. If `"auto"` is passed, the column will be the size of its content.
         */
        "sizeXl"?: string;
        /**
          * The size of the column for xs screens, in terms of how many columns it should take up out of the total available. If `"auto"` is passed, the column will be the size of its content.
         */
        "sizeXs"?: string;
    }
    interface IonContent {
        /**
          * The color to use from your application's color palette. Default options are: `"primary"`, `"secondary"`, `"tertiary"`, `"success"`, `"warning"`, `"danger"`, `"light"`, `"medium"`, and `"dark"`. For more information on colors, see [theming](/docs/theming/basics).
         */
        "color"?: Color;
        /**
          * If `true` and the content does not cause an overflow scroll, the scroll interaction will cause a bounce. If the content exceeds the bounds of ionContent, nothing will change. Note, this does not disable the system bounce on iOS. That is an OS level setting.
         */
        "forceOverscroll"?: boolean;
        /**
          * If `true`, the content will scroll behind the headers and footers. This effect can easily be seen by setting the toolbar to transparent.
         */
        "fullscreen": boolean;
        /**
          * Returns the background content element.
         */
        "getBackgroundElement": () => Promise<HTMLElement>;
        /**
          * Get the element where the actual scrolling takes place. This element can be used to subscribe to `scroll` events or manually modify `scrollTop`. However, it's recommended to use the API provided by `ion-content`:  i.e. Using `ionScroll`, `ionScrollStart`, `ionScrollEnd` for scrolling events and `scrollToPoint()` to scroll the content into a certain point.
         */
        "getScrollElement": () => Promise<HTMLElement>;
        /**
          * Scroll by a specified X/Y distance in the component.
          * @param x The amount to scroll by on the horizontal axis.
          * @param y The amount to scroll by on the vertical axis.
          * @param duration The amount of time to take scrolling by that amount.
         */
        "scrollByPoint": (x: number, y: number, duration: number) => Promise<void>;
        /**
          * Because of performance reasons, ionScroll events are disabled by default, in order to enable them and start listening from (ionScroll), set this property to `true`.
         */
        "scrollEvents": boolean;
        /**
          * Scroll to the bottom of the component.
          * @param duration The amount of time to take scrolling to the bottom. Defaults to `0`.
         */
        "scrollToBottom": (duration?: number) => Promise<void>;
        /**
          * Scroll to a specified X/Y location in the component.
          * @param x The point to scroll to on the horizontal axis.
          * @param y The point to scroll to on the vertical axis.
          * @param duration The amount of time to take scrolling to that point. Defaults to `0`.
         */
        "scrollToPoint": (x: number | undefined | null, y: number | undefined | null, duration?: number) => Promise<void>;
        /**
          * Scroll to the top of the component.
          * @param duration The amount of time to take scrolling to the top. Defaults to `0`.
         */
        "scrollToTop": (duration?: number) => Promise<void>;
        /**
          * If you want to enable the content scrolling in the X axis, set this property to `true`.
         */
        "scrollX": boolean;
        /**
          * If you want to disable the content scrolling in the Y axis, set this property to `false`.
         */
        "scrollY": boolean;
    }
    interface IonDatetime {
        /**
          * Emits the ionCancel event and optionally closes the popover or modal that the datetime was presented in.
         */
        "cancel": (closeOverlay?: boolean) => Promise<void>;
        /**
          * The text to display on the picker's cancel button.
         */
        "cancelText": string;
        /**
          * The text to display on the picker's "Clear" button.
         */
        "clearText": string;
        /**
          * The color to use from your application's color palette. Default options are: `"primary"`, `"secondary"`, `"tertiary"`, `"success"`, `"warning"`, `"danger"`, `"light"`, `"medium"`, and `"dark"`. For more information on colors, see [theming](/docs/theming/basics).
         */
        "color"?: Color;
        /**
          * Confirms the selected datetime value, updates the `value` property, and optionally closes the popover or modal that the datetime was presented in.
         */
        "confirm": (closeOverlay?: boolean) => Promise<void>;
        /**
          * Values used to create the list of selectable days. By default every day is shown for the given month. However, to control exactly which days of the month to display, the `dayValues` input can take a number, an array of numbers, or a string of comma separated numbers. Note that even if the array days have an invalid number for the selected month, like `31` in February, it will correctly not show days which are not valid for the selected month.
         */
        "dayValues"?: number[] | number | string;
        /**
          * If `true`, the user cannot interact with the datetime.
         */
        "disabled": boolean;
        /**
          * The text to display on the picker's "Done" button.
         */
        "doneText": string;
        /**
          * The first day of the week to use for `ion-datetime`. The default value is `0` and represents Sunday.
         */
        "firstDayOfWeek": number;
        /**
          * Formatting options for dates and times. Should include a 'date' and/or 'time' object, each of which is of type [Intl.DateTimeFormatOptions](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Intl/DateTimeFormat/DateTimeFormat#options).
         */
        "formatOptions"?: FormatOptions;
        /**
          * Used to apply custom text and background colors to specific dates.  Can be either an array of objects containing ISO strings and colors, or a callback that receives an ISO string and returns the colors.  Only applies to the `date`, `date-time`, and `time-date` presentations, with `preferWheel="false"`.
         */
        "highlightedDates"?: DatetimeHighlight[] | DatetimeHighlightCallback;
        /**
          * The hour cycle of the `ion-datetime`. If no value is set, this is specified by the current locale.
         */
        "hourCycle"?: DatetimeHourCycle;
        /**
          * Values used to create the list of selectable hours. By default the hour values range from `0` to `23` for 24-hour, or `1` to `12` for 12-hour. However, to control exactly which hours to display, the `hourValues` input can take a number, an array of numbers, or a string of comma separated numbers.
         */
        "hourValues"?: number[] | number | string;
        /**
          * Returns if an individual date (calendar day) is enabled or disabled.  If `true`, the day will be enabled/interactive. If `false`, the day will be disabled/non-interactive.  The function accepts an ISO 8601 date string of a given day. By default, all days are enabled. Developers can use this function to write custom logic to disable certain days.  The function is called for each rendered calendar day, for the previous, current and next month. Custom implementations should be optimized for performance to avoid jank.
         */
        "isDateEnabled"?: (dateIsoString: string) => boolean;
        /**
          * The locale to use for `ion-datetime`. This impacts month and day name formatting. The `"default"` value refers to the default locale set by your device.
         */
        "locale": string;
        /**
          * The maximum datetime allowed. Value must be a date string following the [ISO 8601 datetime format standard](https://www.w3.org/TR/NOTE-datetime), `1996-12-19`. The format does not have to be specific to an exact datetime. For example, the maximum could just be the year, such as `1994`. Defaults to the end of this year.
         */
        "max"?: string;
        /**
          * The minimum datetime allowed. Value must be a date string following the [ISO 8601 datetime format standard](https://www.w3.org/TR/NOTE-datetime), such as `1996-12-19`. The format does not have to be specific to an exact datetime. For example, the minimum could just be the year, such as `1994`. Defaults to the beginning of the year, 100 years ago from today.
         */
        "min"?: string;
        /**
          * Values used to create the list of selectable minutes. By default the minutes range from `0` to `59`. However, to control exactly which minutes to display, the `minuteValues` input can take a number, an array of numbers, or a string of comma separated numbers. For example, if the minute selections should only be every 15 minutes, then this input value would be `minuteValues="0,15,30,45"`.
         */
        "minuteValues"?: number[] | number | string;
        /**
          * The mode determines which platform styles to use.
         */
        "mode"?: "ios" | "md";
        /**
          * Values used to create the list of selectable months. By default the month values range from `1` to `12`. However, to control exactly which months to display, the `monthValues` input can take a number, an array of numbers, or a string of comma separated numbers. For example, if only summer months should be shown, then this input value would be `monthValues="6,7,8"`. Note that month numbers do *not* have a zero-based index, meaning January's value is `1`, and December's is `12`.
         */
        "monthValues"?: number[] | number | string;
        /**
          * If `true`, multiple dates can be selected at once. Only applies to `presentation="date"` and `preferWheel="false"`.
         */
        "multiple": boolean;
        /**
          * The name of the control, which is submitted with the form data.
         */
        "name": string;
        /**
          * If `true`, a wheel picker will be rendered instead of a calendar grid where possible. If `false`, a calendar grid will be rendered instead of a wheel picker where possible.  A wheel picker can be rendered instead of a grid when `presentation` is one of the following values: `"date"`, `"date-time"`, or `"time-date"`.  A wheel picker will always be rendered regardless of the `preferWheel` value when `presentation` is one of the following values: `"time"`, `"month"`, `"month-year"`, or `"year"`.
         */
        "preferWheel": boolean;
        /**
          * Which values you want to select. `"date"` will show a calendar picker to select the month, day, and year. `"time"` will show a time picker to select the hour, minute, and (optionally) AM/PM. `"date-time"` will show the date picker first and time picker second. `"time-date"` will show the time picker first and date picker second.
         */
        "presentation": DatetimePresentation;
        /**
          * If `true`, the datetime appears normal but the selected date cannot be changed.
         */
        "readonly": boolean;
        /**
          * Resets the internal state of the datetime but does not update the value. Passing a valid ISO-8601 string will reset the state of the component to the provided date. If no value is provided, the internal state will be reset to the clamped value of the min, max and today.
         */
        "reset": (startDate?: string) => Promise<void>;
        /**
          * If `true`, a "Clear" button will be rendered alongside the default "Cancel" and "OK" buttons at the bottom of the `ion-datetime` component. Developers can also use the `button` slot if they want to customize these buttons. If custom buttons are set in the `button` slot then the default buttons will not be rendered.
         */
        "showClearButton": boolean;
        /**
          * If `true`, the default "Cancel" and "OK" buttons will be rendered at the bottom of the `ion-datetime` component. Developers can also use the `button` slot if they want to customize these buttons. If custom buttons are set in the `button` slot then the default buttons will not be rendered.
         */
        "showDefaultButtons": boolean;
        /**
          * If `true`, the default "Time" label will be rendered for the time selector of the `ion-datetime` component. Developers can also use the `time-label` slot if they want to customize this label. If a custom label is set in the `time-label` slot then the default label will not be rendered.
         */
        "showDefaultTimeLabel": boolean;
        /**
          * If `true`, a header will be shown above the calendar picker. This will include both the slotted title, and the selected date.
         */
        "showDefaultTitle": boolean;
        /**
          * If `cover`, the `ion-datetime` will expand to cover the full width of its container. If `fixed`, the `ion-datetime` will have a fixed width.
         */
        "size": 'cover' | 'fixed';
        /**
          * A callback used to format the header text that shows how many dates are selected. Only used if there are 0 or more than 1 selected (i.e. unused for exactly 1). By default, the header text is set to "numberOfDates days".  See https://ionicframework.com/docs/troubleshooting/runtime#accessing-this if you need to access `this` from within the callback.
         */
        "titleSelectedDatesFormatter"?: TitleSelectedDatesFormatter;
        /**
          * The value of the datetime as a valid ISO 8601 datetime string. This should be an array of strings only when `multiple="true"`.
         */
        "value"?: string | string[] | null;
        /**
          * Values used to create the list of selectable years. By default the year values range between the `min` and `max` datetime inputs. However, to control exactly which years to display, the `yearValues` input can take a number, an array of numbers, or string of comma separated numbers. For example, to show upcoming and recent leap years, then this input's value would be `yearValues="2008,2012,2016,2020,2024"`.
         */
        "yearValues"?: number[] | number | string;
    }
    interface IonDatetimeButton {
        /**
          * The color to use from your application's color palette. Default options are: `"primary"`, `"secondary"`, `"tertiary"`, `"success"`, `"warning"`, `"danger"`, `"light"`, `"medium"`, and `"dark"`. For more information on colors, see [theming](/docs/theming/basics).
         */
        "color"?: Color;
        /**
          * The ID of the `ion-datetime` instance associated with the datetime button.
         */
        "datetime"?: string;
        /**
          * If `true`, the user cannot interact with the button.
         */
        "disabled": boolean;
        /**
          * The mode determines which platform styles to use.
         */
        "mode"?: "ios" | "md";
    }
    interface IonFab {
        /**
          * If `true`, both the `ion-fab-button` and all `ion-fab-list` inside `ion-fab` will become active. That means `ion-fab-button` will become a `close` icon and `ion-fab-list` will become visible.
         */
        "activated": boolean;
        /**
          * Close an active FAB list container.
         */
        "close": () => Promise<void>;
        /**
          * If `true`, the fab will display on the edge of the header if `vertical` is `"top"`, and on the edge of the footer if it is `"bottom"`. Should be used with a `fixed` slot.
         */
        "edge": boolean;
        /**
          * Where to align the fab horizontally in the viewport.
         */
        "horizontal"?: 'start' | 'end' | 'center';
        /**
          * Opens/Closes the FAB list container.
         */
        "toggle": () => Promise<void>;
        /**
          * Where to align the fab vertically in the viewport.
         */
        "vertical"?: 'top' | 'bottom' | 'center';
    }
    interface IonFabButton {
        /**
          * If `true`, the fab button will be show a close icon.
         */
        "activated": boolean;
        /**
          * The icon name to use for the close icon. This will appear when the fab button is pressed. Only applies if it is the main button inside of a fab containing a fab list.
         */
        "closeIcon": string;
        /**
          * The color to use from your application's color palette. Default options are: `"primary"`, `"secondary"`, `"tertiary"`, `"success"`, `"warning"`, `"danger"`, `"light"`, `"medium"`, and `"dark"`. For more information on colors, see [theming](/docs/theming/basics).
         */
        "color"?: Color;
        /**
          * If `true`, the user cannot interact with the fab button.
         */
        "disabled": boolean;
        /**
          * This attribute instructs browsers to download a URL instead of navigating to it, so the user will be prompted to save it as a local file. If the attribute has a value, it is used as the pre-filled file name in the Save prompt (the user can still change the file name if they want).
         */
        "download": string | undefined;
        /**
          * Contains a URL or a URL fragment that the hyperlink points to. If this property is set, an anchor tag will be rendered.
         */
        "href": string | undefined;
        /**
          * The mode determines which platform styles to use.
         */
        "mode"?: "ios" | "md";
        /**
          * Specifies the relationship of the target object to the link object. The value is a space-separated list of [link types](https://developer.mozilla.org/en-US/docs/Web/HTML/Link_types).
         */
        "rel": string | undefined;
        /**
          * When using a router, it specifies the transition animation when navigating to another page using `href`.
         */
        "routerAnimation": AnimationBuilder | undefined;
        /**
          * When using a router, it specifies the transition direction when navigating to another page using `href`.
         */
        "routerDirection": RouterDirection;
        /**
          * If `true`, the fab button will show when in a fab-list.
         */
        "show": boolean;
        /**
          * The size of the button. Set this to `small` in order to have a mini fab button.
         */
        "size"?: 'small';
        /**
          * Specifies where to display the linked URL. Only applies when an `href` is provided. Special keywords: `"_blank"`, `"_self"`, `"_parent"`, `"_top"`.
         */
        "target": string | undefined;
        /**
          * If `true`, the fab button will be translucent. Only applies when the mode is `"ios"` and the device supports [`backdrop-filter`](https://developer.mozilla.org/en-US/docs/Web/CSS/backdrop-filter#Browser_compatibility).
         */
        "translucent": boolean;
        /**
          * The type of the button.
         */
        "type": 'submit' | 'reset' | 'button';
    }
    interface IonFabList {
        /**
          * If `true`, the fab list will show all fab buttons in the list.
         */
        "activated": boolean;
        /**
          * The side the fab list will show on relative to the main fab button.
         */
        "side": 'start' | 'end' | 'top' | 'bottom';
    }
    interface IonFooter {
        /**
          * Describes the scroll effect that will be applied to the footer. Only applies in iOS mode.
         */
        "collapse"?: 'fade';
        /**
          * The mode determines which platform styles to use.
         */
        "mode"?: "ios" | "md";
        /**
          * If `true`, the footer will be translucent. Only applies when the mode is `"ios"` and the device supports [`backdrop-filter`](https://developer.mozilla.org/en-US/docs/Web/CSS/backdrop-filter#Browser_compatibility).  Note: In order to scroll content behind the footer, the `fullscreen` attribute needs to be set on the content.
         */
        "translucent": boolean;
    }
    interface IonGrid {
        /**
          * If `true`, the grid will have a fixed width based on the screen size.
         */
        "fixed": boolean;
    }
    interface IonHeader {
        /**
          * Describes the scroll effect that will be applied to the header. Only applies in iOS mode.  Typically used for [Collapsible Large Titles](https://ionicframework.com/docs/api/title#collapsible-large-titles)
         */
        "collapse"?: 'condense' | 'fade';
        /**
          * The mode determines which platform styles to use.
         */
        "mode"?: "ios" | "md";
        /**
          * If `true`, the header will be translucent. Only applies when the mode is `"ios"` and the device supports [`backdrop-filter`](https://developer.mozilla.org/en-US/docs/Web/CSS/backdrop-filter#Browser_compatibility).  Note: In order to scroll content behind the header, the `fullscreen` attribute needs to be set on the content.
         */
        "translucent": boolean;
    }
    interface IonImg {
        /**
          * This attribute defines the alternative text describing the image. Users will see this text displayed if the image URL is wrong, the image is not in one of the supported formats, or if the image is not yet downloaded.
         */
        "alt"?: string;
        /**
          * The image URL. This attribute is mandatory for the `<img>` element.
         */
        "src"?: string;
    }
    interface IonInfiniteScroll {
        /**
          * Call `complete()` within the `ionInfinite` output event handler when your async operation has completed. For example, the `loading` state is while the app is performing an asynchronous operation, such as receiving more data from an AJAX request to add more items to a data list. Once the data has been received and UI updated, you then call this method to signify that the loading has completed. This method will change the infinite scroll's state from `loading` to `enabled`.
         */
        "complete": () => Promise<void>;
        /**
          * If `true`, the infinite scroll will be hidden and scroll event listeners will be removed.  Set this to true to disable the infinite scroll from actively trying to receive new data while scrolling. This is useful when it is known that there is no more data that can be added, and the infinite scroll is no longer needed.
         */
        "disabled": boolean;
        /**
          * The position of the infinite scroll element. The value can be either `top` or `bottom`.
         */
        "position": 'top' | 'bottom';
        /**
          * The threshold distance from the bottom of the content to call the `infinite` output event when scrolled. The threshold value can be either a percent, or in pixels. For example, use the value of `10%` for the `infinite` output event to get called when the user has scrolled 10% from the bottom of the page. Use the value `100px` when the scroll is within 100 pixels from the bottom of the page.
         */
        "threshold": string;
    }
    interface IonInfiniteScrollContent {
        /**
          * An animated SVG spinner that shows while loading.
         */
        "loadingSpinner"?: SpinnerTypes | null;
        /**
          * Optional text to display while loading. `loadingText` can accept either plaintext or HTML as a string. To display characters normally reserved for HTML, they must be escaped. For example `<Ionic>` would become `&lt;Ionic&gt;`  For more information: [Security Documentation](https://ionicframework.com/docs/faq/security)  This property accepts custom HTML as a string. Content is parsed as plaintext by default. `innerHTMLTemplatesEnabled` must be set to `true` in the Ionic config before custom HTML can be used.
         */
        "loadingText"?: string | IonicSafeString;
    }
    interface IonInput {
        /**
          * This attribute is ignored.
          * @deprecated
         */
        "accept"?: string;
        /**
          * Indicates whether and how the text value should be automatically capitalized as it is entered/edited by the user. Available options: `"off"`, `"none"`, `"on"`, `"sentences"`, `"words"`, `"characters"`.
         */
        "autocapitalize": string;
        /**
          * Indicates whether the value of the control can be automatically completed by the browser.
         */
        "autocomplete": AutocompleteTypes;
        /**
          * Whether auto correction should be enabled when the user is entering/editing the text value.
         */
        "autocorrect": 'on' | 'off';
        /**
          * Sets the [`autofocus` attribute](https://developer.mozilla.org/en-US/docs/Web/HTML/Global_attributes/autofocus) on the native input element.  This may not be sufficient for the element to be focused on page load. See [managing focus](/docs/developing/managing-focus) for more information.
         */
        "autofocus": boolean;
        /**
          * If `true`, a clear icon will appear in the input when there is a value. Clicking it clears the input.
         */
        "clearInput": boolean;
        /**
          * If `true`, the value will be cleared after focus upon edit. Defaults to `true` when `type` is `"password"`, `false` for all other types.
         */
        "clearOnEdit"?: boolean;
        /**
          * The color to use from your application's color palette. Default options are: `"primary"`, `"secondary"`, `"tertiary"`, `"success"`, `"warning"`, `"danger"`, `"light"`, `"medium"`, and `"dark"`. For more information on colors, see [theming](/docs/theming/basics).
         */
        "color"?: Color;
        /**
          * If `true`, a character counter will display the ratio of characters used and the total character limit. Developers must also set the `maxlength` property for the counter to be calculated correctly.
         */
        "counter": boolean;
        /**
          * A callback used to format the counter text. By default the counter text is set to "itemLength / maxLength".  See https://ionicframework.com/docs/troubleshooting/runtime#accessing-this if you need to access `this` from within the callback.
         */
        "counterFormatter"?: (inputLength: number, maxLength: number) => string;
        /**
          * Set the amount of time, in milliseconds, to wait to trigger the `ionInput` event after each keystroke.
         */
        "debounce"?: number;
        /**
          * If `true`, the user cannot interact with the input.
         */
        "disabled": boolean;
        /**
          * A hint to the browser for which enter key to display. Possible values: `"enter"`, `"done"`, `"go"`, `"next"`, `"previous"`, `"search"`, and `"send"`.
         */
        "enterkeyhint"?: 'enter' | 'done' | 'go' | 'next' | 'previous' | 'search' | 'send';
        /**
          * Text that is placed under the input and displayed when an error is detected.
         */
        "errorText"?: string;
        /**
          * The fill for the item. If `"solid"` the item will have a background. If `"outline"` the item will be transparent with a border. Only available in `md` mode.
         */
        "fill"?: 'outline' | 'solid';
        /**
          * Returns the native `<input>` element used under the hood.
         */
        "getInputElement": () => Promise<HTMLInputElement>;
        /**
          * Text that is placed under the input and displayed when no error is detected.
         */
        "helperText"?: string;
        /**
          * A hint to the browser for which keyboard to display. Possible values: `"none"`, `"text"`, `"tel"`, `"url"`, `"email"`, `"numeric"`, `"decimal"`, and `"search"`.
         */
        "inputmode"?: 'none' | 'text' | 'tel' | 'url' | 'email' | 'numeric' | 'decimal' | 'search';
        /**
          * The visible label associated with the input.  Use this if you need to render a plaintext label.  The `label` property will take priority over the `label` slot if both are used.
         */
        "label"?: string;
        /**
          * Where to place the label relative to the input. `"start"`: The label will appear to the left of the input in LTR and to the right in RTL. `"end"`: The label will appear to the right of the input in LTR and to the left in RTL. `"floating"`: The label will appear smaller and above the input when the input is focused or it has a value. Otherwise it will appear on top of the input. `"stacked"`: The label will appear smaller and above the input regardless even when the input is blurred or has no value. `"fixed"`: The label has the same behavior as `"start"` except it also has a fixed width. Long text will be truncated with ellipses ("...").
         */
        "labelPlacement": 'start' | 'end' | 'floating' | 'stacked' | 'fixed';
        /**
          * Set the `legacy` property to `true` to forcibly use the legacy form control markup. Ionic will only opt components in to the modern form markup when they are using either the `aria-label` attribute or the `label` property. As a result, the `legacy` property should only be used as an escape hatch when you want to avoid this automatic opt-in behavior. Note that this property will be removed in an upcoming major release of Ionic, and all form components will be opted-in to using the modern form markup.
         */
        "legacy"?: boolean;
        /**
          * The maximum value, which must not be less than its minimum (min attribute) value.
         */
        "max"?: string | number;
        /**
          * If the value of the type attribute is `text`, `email`, `search`, `password`, `tel`, or `url`, this attribute specifies the maximum number of characters that the user can enter.
         */
        "maxlength"?: number;
        /**
          * The minimum value, which must not be greater than its maximum (max attribute) value.
         */
        "min"?: string | number;
        /**
          * If the value of the type attribute is `text`, `email`, `search`, `password`, `tel`, or `url`, this attribute specifies the minimum number of characters that the user can enter.
         */
        "minlength"?: number;
        /**
          * The mode determines which platform styles to use.
         */
        "mode"?: "ios" | "md";
        /**
          * If `true`, the user can enter more than one value. This attribute applies when the type attribute is set to `"email"`, otherwise it is ignored.
         */
        "multiple"?: boolean;
        /**
          * The name of the control, which is submitted with the form data.
         */
        "name": string;
        /**
          * A regular expression that the value is checked against. The pattern must match the entire value, not just some subset. Use the title attribute to describe the pattern to help the user. This attribute applies when the value of the type attribute is `"text"`, `"search"`, `"tel"`, `"url"`, `"email"`, `"date"`, or `"password"`, otherwise it is ignored. When the type attribute is `"date"`, `pattern` will only be used in browsers that do not support the `"date"` input type natively. See https://developer.mozilla.org/en-US/docs/Web/HTML/Element/input/date for more information.
         */
        "pattern"?: string;
        /**
          * Instructional text that shows before the input has a value. This property applies only when the `type` property is set to `"email"`, `"number"`, `"password"`, `"search"`, `"tel"`, `"text"`, or `"url"`, otherwise it is ignored.
         */
        "placeholder"?: string;
        /**
          * If `true`, the user cannot modify the value.
         */
        "readonly": boolean;
        /**
          * If `true`, the user must fill in a value before submitting a form.
         */
        "required": boolean;
        /**
          * Sets focus on the native `input` in `ion-input`. Use this method instead of the global `input.focus()`.  Developers who wish to focus an input when a page enters should call `setFocus()` in the `ionViewDidEnter()` lifecycle method.  Developers who wish to focus an input when an overlay is presented should call `setFocus` after `didPresent` has resolved.  See [managing focus](/docs/developing/managing-focus) for more information.
         */
        "setFocus": () => Promise<void>;
        /**
          * The shape of the input. If "round" it will have an increased border radius.
         */
        "shape"?: 'round';
        "size"?: number;
        /**
          * If `true`, the element will have its spelling and grammar checked.
         */
        "spellcheck": boolean;
        /**
          * Works with the min and max attributes to limit the increments at which a value can be set. Possible values are: `"any"` or a positive floating point number.
         */
        "step"?: string;
        /**
          * The type of control to display. The default type is text.
         */
        "type": TextFieldTypes;
        /**
          * The value of the input.
         */
        "value"?: string | number | null;
    }
    interface IonItem {
        /**
          * If `true`, a button tag will be rendered and the item will be tappable.
         */
        "button": boolean;
        /**
          * The color to use from your application's color palette. Default options are: `"primary"`, `"secondary"`, `"tertiary"`, `"success"`, `"warning"`, `"danger"`, `"light"`, `"medium"`, and `"dark"`. For more information on colors, see [theming](/docs/theming/basics).
         */
        "color"?: Color;
        /**
          * If `true`, a character counter will display the ratio of characters used and the total character limit. Only applies when the `maxlength` property is set on the inner `ion-input` or `ion-textarea`.
          * @deprecated Use the `counter` property on `ion-input` or `ion-textarea` instead.
         */
        "counter": boolean;
        /**
          * A callback used to format the counter text. By default the counter text is set to "itemLength / maxLength".
          * @deprecated Use the `counterFormatter` property on `ion-input` or `ion-textarea` instead.
         */
        "counterFormatter"?: CounterFormatter;
        /**
          * If `true`, a detail arrow will appear on the item. Defaults to `false` unless the `mode` is `ios` and an `href` or `button` property is present.
         */
        "detail"?: boolean;
        /**
          * The icon to use when `detail` is set to `true`.
         */
        "detailIcon": string;
        /**
          * If `true`, the user cannot interact with the item.
         */
        "disabled": boolean;
        /**
          * This attribute instructs browsers to download a URL instead of navigating to it, so the user will be prompted to save it as a local file. If the attribute has a value, it is used as the pre-filled file name in the Save prompt (the user can still change the file name if they want).
         */
        "download": string | undefined;
        /**
          * The fill for the item. If `"solid"` the item will have a background. If `"outline"` the item will be transparent with a border. Only available in `md` mode.
          * @deprecated Use the `fill` property on `ion-input` or `ion-textarea` instead.
         */
        "fill"?: 'outline' | 'solid';
        /**
          * Contains a URL or a URL fragment that the hyperlink points to. If this property is set, an anchor tag will be rendered.
         */
        "href": string | undefined;
        /**
          * How the bottom border should be displayed on the item.
         */
        "lines"?: 'full' | 'inset' | 'none';
        /**
          * The mode determines which platform styles to use.
         */
        "mode"?: "ios" | "md";
        /**
          * Specifies the relationship of the target object to the link object. The value is a space-separated list of [link types](https://developer.mozilla.org/en-US/docs/Web/HTML/Link_types).
         */
        "rel": string | undefined;
        /**
          * When using a router, it specifies the transition animation when navigating to another page using `href`.
         */
        "routerAnimation": AnimationBuilder | undefined;
        /**
          * When using a router, it specifies the transition direction when navigating to another page using `href`.
         */
        "routerDirection": RouterDirection;
        /**
          * The shape of the item. If "round" it will have increased border radius.
         */
        "shape"?: 'round';
        /**
          * Specifies where to display the linked URL. Only applies when an `href` is provided. Special keywords: `"_blank"`, `"_self"`, `"_parent"`, `"_top"`.
         */
        "target": string | undefined;
        /**
          * The type of the button. Only used when an `onclick` or `button` property is present.
         */
        "type": 'submit' | 'reset' | 'button';
    }
    interface IonItemDivider {
        /**
          * The color to use from your application's color palette. Default options are: `"primary"`, `"secondary"`, `"tertiary"`, `"success"`, `"warning"`, `"danger"`, `"light"`, `"medium"`, and `"dark"`. For more information on colors, see [theming](/docs/theming/basics).
         */
        "color"?: Color;
        /**
          * The mode determines which platform styles to use.
         */
        "mode"?: "ios" | "md";
        /**
          * When it's set to `true`, the item-divider will stay visible when it reaches the top of the viewport until the next `ion-item-divider` replaces it.  This feature relies in `position:sticky`: https://caniuse.com/#feat=css-sticky
         */
        "sticky": boolean;
    }
    interface IonItemGroup {
    }
    interface IonItemOption {
        /**
          * The color to use from your application's color palette. Default options are: `"primary"`, `"secondary"`, `"tertiary"`, `"success"`, `"warning"`, `"danger"`, `"light"`, `"medium"`, and `"dark"`. For more information on colors, see [theming](/docs/theming/basics).
         */
        "color"?: Color;
        /**
          * If `true`, the user cannot interact with the item option.
         */
        "disabled": boolean;
        /**
          * This attribute instructs browsers to download a URL instead of navigating to it, so the user will be prompted to save it as a local file. If the attribute has a value, it is used as the pre-filled file name in the Save prompt (the user can still change the file name if they want).
         */
        "download": string | undefined;
        /**
          * If `true`, the option will expand to take up the available width and cover any other options.
         */
        "expandable": boolean;
        /**
          * Contains a URL or a URL fragment that the hyperlink points to. If this property is set, an anchor tag will be rendered.
         */
        "href": string | undefined;
        /**
          * The mode determines which platform styles to use.
         */
        "mode"?: "ios" | "md";
        /**
          * Specifies the relationship of the target object to the link object. The value is a space-separated list of [link types](https://developer.mozilla.org/en-US/docs/Web/HTML/Link_types).
         */
        "rel": string | undefined;
        /**
          * Specifies where to display the linked URL. Only applies when an `href` is provided. Special keywords: `"_blank"`, `"_self"`, `"_parent"`, `"_top"`.
         */
        "target": string | undefined;
        /**
          * The type of the button.
         */
        "type": 'submit' | 'reset' | 'button';
    }
    interface IonItemOptions {
        "fireSwipeEvent": () => Promise<void>;
        /**
          * The side the option button should be on. Possible values: `"start"` and `"end"`. If you have multiple `ion-item-options`, a side must be provided for each.
         */
        "side": Side;
    }
    interface IonItemSliding {
        /**
          * Close the sliding item. Items can also be closed from the [List](./list).
         */
        "close": () => Promise<void>;
        /**
          * Close all of the sliding items in the list. Items can also be closed from the [List](./list).
         */
        "closeOpened": () => Promise<boolean>;
        /**
          * If `true`, the user cannot interact with the sliding item.
         */
        "disabled": boolean;
        /**
          * Get the amount the item is open in pixels.
         */
        "getOpenAmount": () => Promise<number>;
        /**
          * Get the ratio of the open amount of the item compared to the width of the options. If the number returned is positive, then the options on the right side are open. If the number returned is negative, then the options on the left side are open. If the absolute value of the number is greater than 1, the item is open more than the width of the options.
         */
        "getSlidingRatio": () => Promise<number>;
        /**
          * Open the sliding item.
          * @param side The side of the options to open. If a side is not provided, it will open the first set of options it finds within the item.
         */
        "open": (side: Side | undefined) => Promise<void>;
    }
    interface IonLabel {
        /**
          * The color to use from your application's color palette. Default options are: `"primary"`, `"secondary"`, `"tertiary"`, `"success"`, `"warning"`, `"danger"`, `"light"`, `"medium"`, and `"dark"`. For more information on colors, see [theming](/docs/theming/basics).
         */
        "color"?: Color;
        /**
          * The mode determines which platform styles to use.
         */
        "mode"?: "ios" | "md";
        /**
          * The position determines where and how the label behaves inside an item.
         */
        "position"?: 'fixed' | 'stacked' | 'floating';
    }
    interface IonList {
        /**
          * If `ion-item-sliding` are used inside the list, this method closes any open sliding item.  Returns `true` if an actual `ion-item-sliding` is closed.
         */
        "closeSlidingItems": () => Promise<boolean>;
        /**
          * If `true`, the list will have margin around it and rounded corners.
         */
        "inset": boolean;
        /**
          * How the bottom border should be displayed on all items.
         */
        "lines"?: 'full' | 'inset' | 'none';
        /**
          * The mode determines which platform styles to use.
         */
        "mode"?: "ios" | "md";
    }
    interface IonListHeader {
        /**
          * The color to use from your application's color palette. Default options are: `"primary"`, `"secondary"`, `"tertiary"`, `"success"`, `"warning"`, `"danger"`, `"light"`, `"medium"`, and `"dark"`. For more information on colors, see [theming](/docs/theming/basics).
         */
        "color"?: Color;
        /**
          * How the bottom border should be displayed on the list header.
         */
        "lines"?: 'full' | 'inset' | 'none';
        /**
          * The mode determines which platform styles to use.
         */
        "mode"?: "ios" | "md";
    }
    interface IonLoading {
        /**
          * If `true`, the loading indicator will animate.
         */
        "animated": boolean;
        /**
          * If `true`, the loading indicator will be dismissed when the backdrop is clicked.
         */
        "backdropDismiss": boolean;
        /**
          * Additional classes to apply for custom CSS. If multiple classes are provided they should be separated by spaces.
         */
        "cssClass"?: string | string[];
        "delegate"?: FrameworkDelegate;
        /**
          * Dismiss the loading overlay after it has been presented.
          * @param data Any data to emit in the dismiss events.
          * @param role The role of the element that is dismissing the loading. This can be useful in a button handler for determining which button was clicked to dismiss the loading. Some examples include: ``"cancel"`, `"destructive"`, "selected"`, and `"backdrop"`.  This is a no-op if the overlay has not been presented yet. If you want to remove an overlay from the DOM that was never presented, use the [remove](https://developer.mozilla.org/en-US/docs/Web/API/Element/remove) method.
         */
        "dismiss": (data?: any, role?: string) => Promise<boolean>;
        /**
          * Number of milliseconds to wait before dismissing the loading indicator.
         */
        "duration": number;
        /**
          * Animation to use when the loading indicator is presented.
         */
        "enterAnimation"?: AnimationBuilder;
        "hasController": boolean;
        /**
          * Additional attributes to pass to the loader.
         */
        "htmlAttributes"?: { [key: string]: any };
        /**
          * If `true`, the loading indicator will open. If `false`, the loading indicator will close. Use this if you need finer grained control over presentation, otherwise just use the loadingController or the `trigger` property. Note: `isOpen` will not automatically be set back to `false` when the loading indicator dismisses. You will need to do that in your code.
         */
        "isOpen": boolean;
        /**
          * If `true`, the keyboard will be automatically dismissed when the overlay is presented.
         */
        "keyboardClose": boolean;
        /**
          * Animation to use when the loading indicator is dismissed.
         */
        "leaveAnimation"?: AnimationBuilder;
        /**
          * Optional text content to display in the loading indicator.  This property accepts custom HTML as a string. Content is parsed as plaintext by default. `innerHTMLTemplatesEnabled` must be set to `true` in the Ionic config before custom HTML can be used.
         */
        "message"?: string | IonicSafeString;
        /**
          * The mode determines which platform styles to use.
         */
        "mode"?: "ios" | "md";
        /**
          * Returns a promise that resolves when the loading did dismiss.
         */
        "onDidDismiss": <T = any>() => Promise<OverlayEventDetail<T>>;
        /**
          * Returns a promise that resolves when the loading will dismiss.
         */
        "onWillDismiss": <T = any>() => Promise<OverlayEventDetail<T>>;
        "overlayIndex": number;
        /**
          * Present the loading overlay after it has been created.
         */
        "present": () => Promise<void>;
        /**
          * If `true`, a backdrop will be displayed behind the loading indicator.
         */
        "showBackdrop": boolean;
        /**
          * The name of the spinner to display.
         */
        "spinner"?: SpinnerTypes | null;
        /**
          * If `true`, the loading indicator will be translucent. Only applies when the mode is `"ios"` and the device supports [`backdrop-filter`](https://developer.mozilla.org/en-US/docs/Web/CSS/backdrop-filter#Browser_compatibility).
         */
        "translucent": boolean;
        /**
          * An ID corresponding to the trigger element that causes the loading indicator to open when clicked.
         */
        "trigger": string | undefined;
    }
    interface IonMenu {
        /**
          * Closes the menu. If the menu is already closed or it can't be closed, it returns `false`.
         */
        "close": (animated?: boolean) => Promise<boolean>;
        /**
          * The `id` of the main content. When using a router this is typically `ion-router-outlet`. When not using a router, this is typically your main view's `ion-content`. This is not the id of the `ion-content` inside of your `ion-menu`.
         */
        "contentId"?: string;
        /**
          * If `true`, the menu is disabled.
         */
        "disabled": boolean;
        /**
          * Returns `true` is the menu is active.  A menu is active when it can be opened or closed, meaning it's enabled and it's not part of a `ion-split-pane`.
         */
        "isActive": () => Promise<boolean>;
        /**
          * Returns `true` is the menu is open.
         */
        "isOpen": () => Promise<boolean>;
        /**
          * The edge threshold for dragging the menu open. If a drag/swipe happens over this value, the menu is not triggered.
         */
        "maxEdgeStart": number;
        /**
          * An id for the menu.
         */
        "menuId"?: string;
        /**
          * Opens the menu. If the menu is already open or it can't be opened, it returns `false`.
         */
        "open": (animated?: boolean) => Promise<boolean>;
        /**
          * Opens or closes the button. If the operation can't be completed successfully, it returns `false`.
         */
        "setOpen": (shouldOpen: boolean, animated?: boolean) => Promise<boolean>;
        /**
          * Which side of the view the menu should be placed.
         */
        "side": Side;
        /**
          * If `true`, swiping the menu is enabled.
         */
        "swipeGesture": boolean;
        /**
          * Toggles the menu. If the menu is already open, it will try to close, otherwise it will try to open it. If the operation can't be completed successfully, it returns `false`.
         */
        "toggle": (animated?: boolean) => Promise<boolean>;
        /**
          * The display type of the menu. Available options: `"overlay"`, `"reveal"`, `"push"`.
         */
        "type"?: string;
    }
    interface IonMenuButton {
        /**
          * Automatically hides the menu button when the corresponding menu is not active
         */
        "autoHide": boolean;
        /**
          * The color to use from your application's color palette. Default options are: `"primary"`, `"secondary"`, `"tertiary"`, `"success"`, `"warning"`, `"danger"`, `"light"`, `"medium"`, and `"dark"`. For more information on colors, see [theming](/docs/theming/basics).
         */
        "color"?: Color;
        /**
          * If `true`, the user cannot interact with the menu button.
         */
        "disabled": boolean;
        /**
          * Optional property that maps to a Menu's `menuId` prop. Can also be `start` or `end` for the menu side. This is used to find the correct menu to toggle
         */
        "menu"?: string;
        /**
          * The mode determines which platform styles to use.
         */
        "mode"?: "ios" | "md";
        /**
          * The type of the button.
         */
        "type": 'submit' | 'reset' | 'button';
    }
    interface IonMenuToggle {
        /**
          * Automatically hides the content when the corresponding menu is not active.  By default, it's `true`. Change it to `false` in order to keep `ion-menu-toggle` always visible regardless the state of the menu.
         */
        "autoHide": boolean;
        /**
          * Optional property that maps to a Menu's `menuId` prop. Can also be `start` or `end` for the menu side. This is used to find the correct menu to toggle.  If this property is not used, `ion-menu-toggle` will toggle the first menu that is active.
         */
        "menu"?: string;
    }
    interface IonModal {
        /**
          * If `true`, the modal will animate.
         */
        "animated": boolean;
        /**
          * A decimal value between 0 and 1 that indicates the point after which the backdrop will begin to fade in when using a sheet modal. Prior to this point, the backdrop will be hidden and the content underneath the sheet can be interacted with. This value is exclusive meaning the backdrop will become active after the value specified.
         */
        "backdropBreakpoint": number;
        /**
          * If `true`, the modal will be dismissed when the backdrop is clicked.
         */
        "backdropDismiss": boolean;
        /**
          * The breakpoints to use when creating a sheet modal. Each value in the array must be a decimal between 0 and 1 where 0 indicates the modal is fully closed and 1 indicates the modal is fully open. Values are relative to the height of the modal, not the height of the screen. One of the values in this array must be the value of the `initialBreakpoint` property. For example: [0, .25, .5, 1]
         */
        "breakpoints"?: number[];
        /**
          * Determines whether or not a modal can dismiss when calling the `dismiss` method.  If the value is `true` or the value's function returns `true`, the modal will close when trying to dismiss. If the value is `false` or the value's function returns `false`, the modal will not close when trying to dismiss.  See https://ionicframework.com/docs/troubleshooting/runtime#accessing-this if you need to access `this` from within the callback.
         */
        "canDismiss": boolean | ((data?: any, role?: string) => Promise<boolean>);
        /**
          * The component to display inside of the modal.
         */
        "component"?: ComponentRef;
        /**
          * The data to pass to the modal component.
         */
        "componentProps"?: ComponentProps;
        /**
          * Additional classes to apply for custom CSS. If multiple classes are provided they should be separated by spaces.
         */
        "cssClass"?: string | string[];
        "delegate"?: FrameworkDelegate;
        /**
          * Dismiss the modal overlay after it has been presented.
          * @param data Any data to emit in the dismiss events.
          * @param role The role of the element that is dismissing the modal. For example, 'cancel' or 'backdrop'.  This is a no-op if the overlay has not been presented yet. If you want to remove an overlay from the DOM that was never presented, use the [remove](https://developer.mozilla.org/en-US/docs/Web/API/Element/remove) method.
         */
        "dismiss": (data?: any, role?: string) => Promise<boolean>;
        /**
          * Animation to use when the modal is presented.
         */
        "enterAnimation"?: AnimationBuilder;
        /**
          * Returns the current breakpoint of a sheet style modal
         */
        "getCurrentBreakpoint": () => Promise<number | undefined>;
        /**
          * The horizontal line that displays at the top of a sheet modal. It is `true` by default when setting the `breakpoints` and `initialBreakpoint` properties.
         */
        "handle"?: boolean;
        /**
          * The interaction behavior for the sheet modal when the handle is pressed.  Defaults to `"none"`, which  means the modal will not change size or position when the handle is pressed. Set to `"cycle"` to let the modal cycle between available breakpoints when pressed.  Handle behavior is unavailable when the `handle` property is set to `false` or when the `breakpoints` property is not set (using a fullscreen or card modal).
         */
        "handleBehavior"?: ModalHandleBehavior;
        "hasController": boolean;
        /**
          * Additional attributes to pass to the modal.
         */
        "htmlAttributes"?: { [key: string]: any };
        /**
          * A decimal value between 0 and 1 that indicates the initial point the modal will open at when creating a sheet modal. This value must also be listed in the `breakpoints` array.
         */
        "initialBreakpoint"?: number;
        /**
          * If `true`, the modal will open. If `false`, the modal will close. Use this if you need finer grained control over presentation, otherwise just use the modalController or the `trigger` property. Note: `isOpen` will not automatically be set back to `false` when the modal dismisses. You will need to do that in your code.
         */
        "isOpen": boolean;
        /**
          * If `true`, the component passed into `ion-modal` will automatically be mounted when the modal is created. The component will remain mounted even when the modal is dismissed. However, the component will be destroyed when the modal is destroyed. This property is not reactive and should only be used when initially creating a modal.  Note: This feature only applies to inline modals in JavaScript frameworks such as Angular, React, and Vue.
         */
        "keepContentsMounted": boolean;
        /**
          * If `true`, the keyboard will be automatically dismissed when the overlay is presented.
         */
        "keyboardClose": boolean;
        /**
          * Animation to use when the modal is dismissed.
         */
        "leaveAnimation"?: AnimationBuilder;
        /**
          * The mode determines which platform styles to use.
         */
        "mode"?: "ios" | "md";
        /**
          * Returns a promise that resolves when the modal did dismiss.
         */
        "onDidDismiss": <T = any>() => Promise<OverlayEventDetail<T>>;
        /**
          * Returns a promise that resolves when the modal will dismiss.
         */
        "onWillDismiss": <T = any>() => Promise<OverlayEventDetail<T>>;
        "overlayIndex": number;
        /**
          * Present the modal overlay after it has been created.
         */
        "present": () => Promise<void>;
        /**
          * The element that presented the modal. This is used for card presentation effects and for stacking multiple modals on top of each other. Only applies in iOS mode.
         */
        "presentingElement"?: HTMLElement;
        /**
          * Move a sheet style modal to a specific breakpoint. The breakpoint value must be a value defined in your `breakpoints` array.
         */
        "setCurrentBreakpoint": (breakpoint: number) => Promise<void>;
        /**
          * If `true`, a backdrop will be displayed behind the modal. This property controls whether or not the backdrop darkens the screen when the modal is presented. It does not control whether or not the backdrop is active or present in the DOM.
         */
        "showBackdrop": boolean;
        /**
          * An ID corresponding to the trigger element that causes the modal to open when clicked.
         */
        "trigger": string | undefined;
    }
    interface IonNav {
        /**
          * If `true`, the nav should animate the transition of components.
         */
        "animated": boolean;
        /**
          * By default `ion-nav` animates transition between pages based in the mode (ios or material design). However, this property allows to create custom transition using `AnimationBuilder` functions.
         */
        "animation"?: AnimationBuilder;
        /**
          * Returns `true` if the current view can go back.
          * @param view The view to check.
         */
        "canGoBack": (view?: ViewController) => Promise<boolean>;
        "delegate"?: FrameworkDelegate;
        /**
          * Get the active view.
         */
        "getActive": () => Promise<ViewController | undefined>;
        /**
          * Get the view at the specified index.
          * @param index The index of the view.
         */
        "getByIndex": (index: number) => Promise<ViewController | undefined>;
        /**
          * Get the previous view.
          * @param view The view to get.
         */
        "getPrevious": (view?: ViewController) => Promise<ViewController | undefined>;
        /**
          * Called by <ion-router> to retrieve the current component.
         */
        "getRouteId": () => Promise<RouteID | undefined>;
        /**
          * Inserts a component into the navigation stack at the specified index. This is useful to add a component at any point in the navigation stack.
          * @param insertIndex The index to insert the component at in the stack.
          * @param component The component to insert into the navigation stack.
          * @param componentProps Any properties of the component.
          * @param opts The navigation options.
          * @param done The transition complete function.
         */
        "insert": <T extends NavComponent>(insertIndex: number, component: T, componentProps?: ComponentProps<T> | null, opts?: NavOptions | null, done?: TransitionDoneFn) => Promise<boolean>;
        /**
          * Inserts an array of components into the navigation stack at the specified index. The last component in the array will become instantiated as a view, and animate in to become the active view.
          * @param insertIndex The index to insert the components at in the stack.
          * @param insertComponents The components to insert into the navigation stack.
          * @param opts The navigation options.
          * @param done The transition complete function.
         */
        "insertPages": (insertIndex: number, insertComponents: NavComponent[] | NavComponentWithProps[], opts?: NavOptions | null, done?: TransitionDoneFn) => Promise<boolean>;
        /**
          * Pop a component off of the navigation stack. Navigates back from the current component.
          * @param opts The navigation options.
          * @param done The transition complete function.
         */
        "pop": (opts?: NavOptions | null, done?: TransitionDoneFn) => Promise<boolean>;
        /**
          * Pop to a specific index in the navigation stack.
          * @param indexOrViewCtrl The index or view controller to pop to.
          * @param opts The navigation options.
          * @param done The transition complete function.
         */
        "popTo": (indexOrViewCtrl: number | ViewController, opts?: NavOptions | null, done?: TransitionDoneFn) => Promise<boolean>;
        /**
          * Navigate back to the root of the stack, no matter how far back that is.
          * @param opts The navigation options.
          * @param done The transition complete function.
         */
        "popToRoot": (opts?: NavOptions | null, done?: TransitionDoneFn) => Promise<boolean>;
        /**
          * Push a new component onto the current navigation stack. Pass any additional information along as an object. This additional information is accessible through NavParams.
          * @param component The component to push onto the navigation stack.
          * @param componentProps Any properties of the component.
          * @param opts The navigation options.
          * @param done The transition complete function.
         */
        "push": <T extends NavComponent>(component: T, componentProps?: ComponentProps<T> | null, opts?: NavOptions | null, done?: TransitionDoneFn) => Promise<boolean>;
        /**
          * Removes a component from the navigation stack at the specified index.
          * @param startIndex The number to begin removal at.
          * @param removeCount The number of components to remove.
          * @param opts The navigation options.
          * @param done The transition complete function.
         */
        "removeIndex": (startIndex: number, removeCount?: number, opts?: NavOptions | null, done?: TransitionDoneFn) => Promise<boolean>;
        /**
          * Root NavComponent to load
         */
        "root"?: NavComponent;
        /**
          * Any parameters for the root component
         */
        "rootParams"?: ComponentProps;
        /**
          * Set the views of the current navigation stack and navigate to the last view. By default animations are disabled, but they can be enabled by passing options to the navigation controller. Navigation parameters can also be passed to the individual pages in the array.
          * @param views The list of views to set as the navigation stack.
          * @param opts The navigation options.
          * @param done The transition complete function.
         */
        "setPages": (views: NavComponent[] | NavComponentWithProps[], opts?: NavOptions | null, done?: TransitionDoneFn) => Promise<boolean>;
        /**
          * Set the root for the current navigation stack to a component.
          * @param component The component to set as the root of the navigation stack.
          * @param componentProps Any properties of the component.
          * @param opts The navigation options.
          * @param done The transition complete function.
         */
        "setRoot": <T extends NavComponent>(component: T, componentProps?: ComponentProps<T> | null, opts?: NavOptions | null, done?: TransitionDoneFn) => Promise<boolean>;
        /**
          * Called by the router to update the view.
          * @param id The component tag.
          * @param params The component params.
          * @param direction A direction hint.
          * @param animation an AnimationBuilder.
          * @return the status.
         */
        "setRouteId": (id: string, params: ComponentProps | undefined, direction: RouterDirection, animation?: AnimationBuilder) => Promise<RouteWrite>;
        /**
          * If the nav component should allow for swipe-to-go-back.
         */
        "swipeGesture"?: boolean;
    }
    interface IonNavLink {
        /**
          * Component to navigate to. Only used if the `routerDirection` is `"forward"` or `"root"`.
         */
        "component"?: NavComponent;
        /**
          * Data you want to pass to the component as props. Only used if the `"routerDirection"` is `"forward"` or `"root"`.
         */
        "componentProps"?: ComponentProps;
        /**
          * The transition animation when navigating to another page.
         */
        "routerAnimation"?: AnimationBuilder;
        /**
          * The transition direction when navigating to another page.
         */
        "routerDirection": RouterDirection;
    }
    interface IonNote {
        /**
          * The color to use from your application's color palette. Default options are: `"primary"`, `"secondary"`, `"tertiary"`, `"success"`, `"warning"`, `"danger"`, `"light"`, `"medium"`, and `"dark"`. For more information on colors, see [theming](/docs/theming/basics).
         */
        "color"?: Color;
        /**
          * The mode determines which platform styles to use.
         */
        "mode"?: "ios" | "md";
    }
    interface IonPicker {
        /**
          * If `true`, the picker will animate.
         */
        "animated": boolean;
        /**
          * If `true`, the picker will be dismissed when the backdrop is clicked.
         */
        "backdropDismiss": boolean;
        /**
          * Array of buttons to be displayed at the top of the picker.
         */
        "buttons": PickerButton[];
        /**
          * Array of columns to be displayed in the picker.
         */
        "columns": PickerColumn[];
        /**
          * Additional classes to apply for custom CSS. If multiple classes are provided they should be separated by spaces.
         */
        "cssClass"?: string | string[];
        "delegate"?: FrameworkDelegate;
        /**
          * Dismiss the picker overlay after it has been presented.
          * @param data Any data to emit in the dismiss events.
          * @param role The role of the element that is dismissing the picker. This can be useful in a button handler for determining which button was clicked to dismiss the picker. Some examples include: ``"cancel"`, `"destructive"`, "selected"`, and `"backdrop"`.  This is a no-op if the overlay has not been presented yet. If you want to remove an overlay from the DOM that was never presented, use the [remove](https://developer.mozilla.org/en-US/docs/Web/API/Element/remove) method.
         */
        "dismiss": (data?: any, role?: string) => Promise<boolean>;
        /**
          * Number of milliseconds to wait before dismissing the picker.
         */
        "duration": number;
        /**
          * Animation to use when the picker is presented.
         */
        "enterAnimation"?: AnimationBuilder;
        /**
          * Get the column that matches the specified name.
          * @param name The name of the column.
         */
        "getColumn": (name: string) => Promise<PickerColumn | undefined>;
        "hasController": boolean;
        /**
          * Additional attributes to pass to the picker.
         */
        "htmlAttributes"?: { [key: string]: any };
        /**
          * If `true`, the picker will open. If `false`, the picker will close. Use this if you need finer grained control over presentation, otherwise just use the pickerController or the `trigger` property. Note: `isOpen` will not automatically be set back to `false` when the picker dismisses. You will need to do that in your code.
         */
        "isOpen": boolean;
        /**
          * If `true`, the keyboard will be automatically dismissed when the overlay is presented.
         */
        "keyboardClose": boolean;
        /**
          * Animation to use when the picker is dismissed.
         */
        "leaveAnimation"?: AnimationBuilder;
        /**
          * The mode determines which platform styles to use.
         */
        "mode"?: "ios" | "md";
        /**
          * Returns a promise that resolves when the picker did dismiss.
         */
        "onDidDismiss": <T = any>() => Promise<OverlayEventDetail<T>>;
        /**
          * Returns a promise that resolves when the picker will dismiss.
         */
        "onWillDismiss": <T = any>() => Promise<OverlayEventDetail<T>>;
        "overlayIndex": number;
        /**
          * Present the picker overlay after it has been created.
         */
        "present": () => Promise<void>;
        /**
          * If `true`, a backdrop will be displayed behind the picker.
         */
        "showBackdrop": boolean;
        /**
          * An ID corresponding to the trigger element that causes the picker to open when clicked.
         */
        "trigger": string | undefined;
    }
    interface IonPickerColumn {
        /**
          * Picker column data
         */
        "col": PickerColumn;
    }
    interface IonPickerColumnInternal {
        /**
          * The color to use from your application's color palette. Default options are: `"primary"`, `"secondary"`, `"tertiary"`, `"success"`, `"warning"`, `"danger"`, `"light"`, `"medium"`, and `"dark"`. For more information on colors, see [theming](/docs/theming/basics).
         */
        "color"?: Color;
        /**
          * If `true`, the user cannot interact with the picker.
         */
        "disabled": boolean;
        /**
          * A list of options to be displayed in the picker
         */
        "items": PickerColumnItem[];
        /**
          * The mode determines which platform styles to use.
         */
        "mode"?: "ios" | "md";
        /**
          * If `true`, tapping the picker will reveal a number input keyboard that lets the user type in values for each picker column. This is useful when working with time pickers.
         */
        "numericInput": boolean;
        "scrollActiveItemIntoView": () => Promise<void>;
        /**
          * Sets the value prop and fires the ionChange event. This is used when we need to fire ionChange from user-generated events that cannot be caught with normal input/change event listeners.
         */
        "setValue": (value?: string | number) => Promise<void>;
        /**
          * The selected option in the picker.
         */
        "value"?: string | number;
    }
    interface IonPickerInternal {
        "exitInputMode": () => Promise<void>;
        /**
          * The mode determines which platform styles to use.
         */
        "mode"?: "ios" | "md";
    }
    interface IonPopover {
        /**
          * Describes how to align the popover content with the `reference` point. Defaults to `"center"` for `ios` mode, and `"start"` for `md` mode.
         */
        "alignment"?: PositionAlign;
        /**
          * If `true`, the popover will animate.
         */
        "animated": boolean;
        /**
          * If `true`, the popover will display an arrow that points at the `reference` when running in `ios` mode. Does not apply in `md` mode.
         */
        "arrow": boolean;
        /**
          * If `true`, the popover will be dismissed when the backdrop is clicked.
         */
        "backdropDismiss": boolean;
        /**
          * The component to display inside of the popover. You only need to use this if you are not using a JavaScript framework. Otherwise, you can just slot your component inside of `ion-popover`.
         */
        "component"?: ComponentRef;
        /**
          * The data to pass to the popover component. You only need to use this if you are not using a JavaScript framework. Otherwise, you can just set the props directly on your component.
         */
        "componentProps"?: ComponentProps;
        /**
          * Additional classes to apply for custom CSS. If multiple classes are provided they should be separated by spaces.
         */
        "cssClass"?: string | string[];
        "delegate"?: FrameworkDelegate;
        /**
          * Dismiss the popover overlay after it has been presented.
          * @param data Any data to emit in the dismiss events.
          * @param role The role of the element that is dismissing the popover. For example, 'cancel' or 'backdrop'.
          * @param dismissParentPopover If `true`, dismissing this popover will also dismiss a parent popover if this popover is nested. Defaults to `true`.  This is a no-op if the overlay has not been presented yet. If you want to remove an overlay from the DOM that was never presented, use the [remove](https://developer.mozilla.org/en-US/docs/Web/API/Element/remove) method.
         */
        "dismiss": (data?: any, role?: string, dismissParentPopover?: boolean) => Promise<boolean>;
        /**
          * If `true`, the popover will be automatically dismissed when the content has been clicked.
         */
        "dismissOnSelect": boolean;
        /**
          * Animation to use when the popover is presented.
         */
        "enterAnimation"?: AnimationBuilder;
        /**
          * The event to pass to the popover animation.
         */
        "event": any;
        "getParentPopover": () => Promise<HTMLIonPopoverElement | null>;
        "hasController": boolean;
        /**
          * Additional attributes to pass to the popover.
         */
        "htmlAttributes"?: { [key: string]: any };
        /**
          * If `true`, the popover will open. If `false`, the popover will close. Use this if you need finer grained control over presentation, otherwise just use the popoverController or the `trigger` property. Note: `isOpen` will not automatically be set back to `false` when the popover dismisses. You will need to do that in your code.
         */
        "isOpen": boolean;
        /**
          * If `true`, the component passed into `ion-popover` will automatically be mounted when the popover is created. The component will remain mounted even when the popover is dismissed. However, the component will be destroyed when the popover is destroyed. This property is not reactive and should only be used when initially creating a popover.  Note: This feature only applies to inline popovers in JavaScript frameworks such as Angular, React, and Vue.
         */
        "keepContentsMounted": boolean;
        /**
          * If `true`, the keyboard will be automatically dismissed when the overlay is presented.
         */
        "keyboardClose": boolean;
        "keyboardEvents": boolean;
        /**
          * Animation to use when the popover is dismissed.
         */
        "leaveAnimation"?: AnimationBuilder;
        /**
          * The mode determines which platform styles to use.
         */
        "mode"?: "ios" | "md";
        /**
          * Returns a promise that resolves when the popover did dismiss.
         */
        "onDidDismiss": <T = any>() => Promise<OverlayEventDetail<T>>;
        /**
          * Returns a promise that resolves when the popover will dismiss.
         */
        "onWillDismiss": <T = any>() => Promise<OverlayEventDetail<T>>;
        "overlayIndex": number;
        /**
          * Present the popover overlay after it has been created. Developers can pass a mouse, touch, or pointer event to position the popover relative to where that event was dispatched.
         */
        "present": (event?: MouseEvent | TouchEvent | PointerEvent | CustomEvent) => Promise<void>;
        /**
          * When opening a popover from a trigger, we should not be modifying the `event` prop from inside the component. Additionally, when pressing the "Right" arrow key, we need to shift focus to the first descendant in the newly presented popover.
         */
        "presentFromTrigger": (event?: any, focusDescendant?: boolean) => Promise<void>;
        /**
          * Describes what to position the popover relative to. If `"trigger"`, the popover will be positioned relative to the trigger button. If passing in an event, this is determined via event.target. If `"event"`, the popover will be positioned relative to the x/y coordinates of the trigger action. If passing in an event, this is determined via event.clientX and event.clientY.
         */
        "reference": PositionReference;
        /**
          * If `true`, a backdrop will be displayed behind the popover. This property controls whether or not the backdrop darkens the screen when the popover is presented. It does not control whether or not the backdrop is active or present in the DOM.
         */
        "showBackdrop": boolean;
        /**
          * Describes which side of the `reference` point to position the popover on. The `"start"` and `"end"` values are RTL-aware, and the `"left"` and `"right"` values are not.
         */
        "side": PositionSide;
        /**
          * Describes how to calculate the popover width. If `"cover"`, the popover width will match the width of the trigger. If `"auto"`, the popover width will be set to a static default value.
         */
        "size": PopoverSize;
        /**
          * If `true`, the popover will be translucent. Only applies when the mode is `"ios"` and the device supports [`backdrop-filter`](https://developer.mozilla.org/en-US/docs/Web/CSS/backdrop-filter#Browser_compatibility).
         */
        "translucent": boolean;
        /**
          * An ID corresponding to the trigger element that causes the popover to open. Use the `trigger-action` property to customize the interaction that results in the popover opening.
         */
        "trigger": string | undefined;
        /**
          * Describes what kind of interaction with the trigger that should cause the popover to open. Does not apply when the `trigger` property is `undefined`. If `"click"`, the popover will be presented when the trigger is left clicked. If `"hover"`, the popover will be presented when a pointer hovers over the trigger. If `"context-menu"`, the popover will be presented when the trigger is right clicked on desktop and long pressed on mobile. This will also prevent your device's normal context menu from appearing.
         */
        "triggerAction": TriggerAction;
    }
    interface IonProgressBar {
        /**
          * If the buffer and value are smaller than 1, the buffer circles will show. The buffer should be between [0, 1].
         */
        "buffer": number;
        /**
          * The color to use from your application's color palette. Default options are: `"primary"`, `"secondary"`, `"tertiary"`, `"success"`, `"warning"`, `"danger"`, `"light"`, `"medium"`, and `"dark"`. For more information on colors, see [theming](/docs/theming/basics).
         */
        "color"?: Color;
        /**
          * The mode determines which platform styles to use.
         */
        "mode"?: "ios" | "md";
        /**
          * If true, reverse the progress bar direction.
         */
        "reversed": boolean;
        /**
          * The state of the progress bar, based on if the time the process takes is known or not. Default options are: `"determinate"` (no animation), `"indeterminate"` (animate from left to right).
         */
        "type": 'determinate' | 'indeterminate';
        /**
          * The value determines how much of the active bar should display when the `type` is `"determinate"`. The value should be between [0, 1].
         */
        "value": number;
    }
    interface IonRadio {
        /**
          * How to control the alignment of the radio and label on the cross axis. `"start"`: The label and control will appear on the left of the cross axis in LTR, and on the right side in RTL. `"center"`: The label and control will appear at the center of the cross axis in both LTR and RTL.
         */
        "alignment": 'start' | 'center';
        /**
          * The color to use from your application's color palette. Default options are: `"primary"`, `"secondary"`, `"tertiary"`, `"success"`, `"warning"`, `"danger"`, `"light"`, `"medium"`, and `"dark"`. For more information on colors, see [theming](/docs/theming/basics).
         */
        "color"?: Color;
        /**
          * If `true`, the user cannot interact with the radio.
         */
        "disabled": boolean;
        /**
          * How to pack the label and radio within a line. `"start"`: The label and radio will appear on the left in LTR and on the right in RTL. `"end"`: The label and radio will appear on the right in LTR and on the left in RTL. `"space-between"`: The label and radio will appear on opposite ends of the line with space between the two elements.
         */
        "justify": 'start' | 'end' | 'space-between';
        /**
          * Where to place the label relative to the radio. `"start"`: The label will appear to the left of the radio in LTR and to the right in RTL. `"end"`: The label will appear to the right of the radio in LTR and to the left in RTL. `"fixed"`: The label has the same behavior as `"start"` except it also has a fixed width. Long text will be truncated with ellipses ("..."). `"stacked"`: The label will appear above the radio regardless of the direction. The alignment of the label can be controlled with the `alignment` property.
         */
        "labelPlacement": 'start' | 'end' | 'fixed' | 'stacked';
        /**
          * Set the `legacy` property to `true` to forcibly use the legacy form control markup. Ionic will only opt components in to the modern form markup when they are using either the `aria-label` attribute or the default slot that contains the label text. As a result, the `legacy` property should only be used as an escape hatch when you want to avoid this automatic opt-in behavior. Note that this property will be removed in an upcoming major release of Ionic, and all form components will be opted-in to using the modern form markup.
         */
        "legacy"?: boolean;
        /**
          * The mode determines which platform styles to use.
         */
        "mode"?: "ios" | "md";
        /**
          * The name of the control, which is submitted with the form data.
         */
        "name": string;
        "setButtonTabindex": (value: number) => Promise<void>;
        "setFocus": (ev: globalThis.Event) => Promise<void>;
        /**
          * the value of the radio.
         */
        "value"?: any | null;
    }
    interface IonRadioGroup {
        /**
          * If `true`, the radios can be deselected.
         */
        "allowEmptySelection": boolean;
        /**
          * This property allows developers to specify a custom function or property name for comparing objects when determining the selected option in the ion-radio-group. When not specified, the default behavior will use strict equality (===) for comparison.
         */
        "compareWith"?: string | RadioGroupCompareFn | null;
        /**
          * The name of the control, which is submitted with the form data.
         */
        "name": string;
        /**
          * the value of the radio group.
         */
        "value"?: any | null;
    }
    interface IonRange {
        /**
          * The start position of the range active bar. This feature is only available with a single knob (dualKnobs="false"). Valid values are greater than or equal to the min value and less than or equal to the max value.
         */
        "activeBarStart"?: number;
        /**
          * The color to use from your application's color palette. Default options are: `"primary"`, `"secondary"`, `"tertiary"`, `"success"`, `"warning"`, `"danger"`, `"light"`, `"medium"`, and `"dark"`. For more information on colors, see [theming](/docs/theming/basics).
         */
        "color"?: Color;
        /**
          * How long, in milliseconds, to wait to trigger the `ionInput` event after each change in the range value.
         */
        "debounce"?: number;
        /**
          * If `true`, the user cannot interact with the range.
         */
        "disabled": boolean;
        /**
          * Show two knobs.
         */
        "dualKnobs": boolean;
        /**
          * The text to display as the control's label. Use this over the `label` slot if you only need plain text. The `label` property will take priority over the `label` slot if both are used.
         */
        "label"?: string;
        /**
          * Where to place the label relative to the range. `"start"`: The label will appear to the left of the range in LTR and to the right in RTL. `"end"`: The label will appear to the right of the range in LTR and to the left in RTL. `"fixed"`: The label has the same behavior as `"start"` except it also has a fixed width. Long text will be truncated with ellipses ("..."). `"stacked"`: The label will appear above the range regardless of the direction.
         */
        "labelPlacement": 'start' | 'end' | 'fixed' | 'stacked';
        /**
          * Set the `legacy` property to `true` to forcibly use the legacy form control markup. Ionic will only opt components in to the modern form markup when they are using either the `aria-label` attribute or the `label` property. As a result, the `legacy` property should only be used as an escape hatch when you want to avoid this automatic opt-in behavior. Note that this property will be removed in an upcoming major release of Ionic, and all form components will be opted-in to using the modern form markup.
         */
        "legacy"?: boolean;
        /**
          * Maximum integer value of the range.
         */
        "max": number;
        /**
          * Minimum integer value of the range.
         */
        "min": number;
        /**
          * The mode determines which platform styles to use.
         */
        "mode"?: "ios" | "md";
        /**
          * The name of the control, which is submitted with the form data.
         */
        "name": string;
        /**
          * If `true`, a pin with integer value is shown when the knob is pressed.
         */
        "pin": boolean;
        /**
          * A callback used to format the pin text. By default the pin text is set to `Math.round(value)`.  See https://ionicframework.com/docs/troubleshooting/runtime#accessing-this if you need to access `this` from within the callback.
         */
        "pinFormatter": PinFormatter;
        /**
          * If `true`, the knob snaps to tick marks evenly spaced based on the step property value.
         */
        "snaps": boolean;
        /**
          * Specifies the value granularity.
         */
        "step": number;
        /**
          * If `true`, tick marks are displayed based on the step value. Only applies when `snaps` is `true`.
         */
        "ticks": boolean;
        /**
          * the value of the range.
         */
        "value": RangeValue;
    }
    interface IonRefresher {
        /**
          * Changes the refresher's state from `refreshing` to `cancelling`.
         */
        "cancel": () => Promise<void>;
        /**
          * Time it takes to close the refresher. Does not apply when the refresher content uses a spinner, enabling the native refresher.
         */
        "closeDuration": string;
        /**
          * Call `complete()` when your async operation has completed. For example, the `refreshing` state is while the app is performing an asynchronous operation, such as receiving more data from an AJAX request. Once the data has been received, you then call this method to signify that the refreshing has completed and to close the refresher. This method also changes the refresher's state from `refreshing` to `completing`.
         */
        "complete": () => Promise<void>;
        /**
          * If `true`, the refresher will be hidden.
         */
        "disabled": boolean;
        /**
          * A number representing how far down the user has pulled. The number `0` represents the user hasn't pulled down at all. The number `1`, and anything greater than `1`, represents that the user has pulled far enough down that when they let go then the refresh will happen. If they let go and the number is less than `1`, then the refresh will not happen, and the content will return to it's original position.
         */
        "getProgress": () => Promise<number>;
        /**
          * The mode determines which platform styles to use.
         */
        "mode"?: "ios" | "md";
        /**
          * How much to multiply the pull speed by. To slow the pull animation down, pass a number less than `1`. To speed up the pull, pass a number greater than `1`. The default value is `1` which is equal to the speed of the cursor. If a negative value is passed in, the factor will be `1` instead.  For example: If the value passed is `1.2` and the content is dragged by `10` pixels, instead of `10` pixels the content will be pulled by `12` pixels (an increase of 20 percent). If the value passed is `0.8`, the dragged amount will be `8` pixels, less than the amount the cursor has moved.  Does not apply when the refresher content uses a spinner, enabling the native refresher.
         */
        "pullFactor": number;
        /**
          * The maximum distance of the pull until the refresher will automatically go into the `refreshing` state. Defaults to the result of `pullMin + 60`. Does not apply when  the refresher content uses a spinner, enabling the native refresher.
         */
        "pullMax": number;
        /**
          * The minimum distance the user must pull down until the refresher will go into the `refreshing` state. Does not apply when the refresher content uses a spinner, enabling the native refresher.
         */
        "pullMin": number;
        /**
          * Time it takes the refresher to snap back to the `refreshing` state. Does not apply when the refresher content uses a spinner, enabling the native refresher.
         */
        "snapbackDuration": string;
    }
    interface IonRefresherContent {
        /**
          * A static icon or a spinner to display when you begin to pull down. A spinner name can be provided to gradually show tick marks when pulling down on iOS devices.
         */
        "pullingIcon"?: SpinnerTypes | string | null;
        /**
          * The text you want to display when you begin to pull down. `pullingText` can accept either plaintext or HTML as a string. To display characters normally reserved for HTML, they must be escaped. For example `<Ionic>` would become `&lt;Ionic&gt;`  For more information: [Security Documentation](https://ionicframework.com/docs/faq/security)  Content is parsed as plaintext by default. `innerHTMLTemplatesEnabled` must be set to `true` in the Ionic config before custom HTML can be used.
         */
        "pullingText"?: string | IonicSafeString;
        /**
          * An animated SVG spinner that shows when refreshing begins
         */
        "refreshingSpinner"?: SpinnerTypes | null;
        /**
          * The text you want to display when performing a refresh. `refreshingText` can accept either plaintext or HTML as a string. To display characters normally reserved for HTML, they must be escaped. For example `<Ionic>` would become `&lt;Ionic&gt;`  For more information: [Security Documentation](https://ionicframework.com/docs/faq/security)  Content is parsed as plaintext by default. `innerHTMLTemplatesEnabled` must be set to `true` in the Ionic config before custom HTML can be used.
         */
        "refreshingText"?: string | IonicSafeString;
    }
    interface IonReorder {
    }
    interface IonReorderGroup {
        /**
          * Completes the reorder operation. Must be called by the `ionItemReorder` event.  If a list of items is passed, the list will be reordered and returned in the proper order.  If no parameters are passed or if `true` is passed in, the reorder will complete and the item will remain in the position it was dragged to. If `false` is passed, the reorder will complete and the item will bounce back to its original position.
          * @param listOrReorder A list of items to be sorted and returned in the new order or a boolean of whether or not the reorder should reposition the item.
         */
        "complete": (listOrReorder?: boolean | any[]) => Promise<any>;
        /**
          * If `true`, the reorder will be hidden.
         */
        "disabled": boolean;
    }
    interface IonRippleEffect {
        /**
          * Adds the ripple effect to the parent element.
          * @param x The horizontal coordinate of where the ripple should start.
          * @param y The vertical coordinate of where the ripple should start.
         */
        "addRipple": (x: number, y: number) => Promise<() => void>;
        /**
          * Sets the type of ripple-effect:  - `bounded`: the ripple effect expands from the user's click position - `unbounded`: the ripple effect expands from the center of the button and overflows the container.  NOTE: Surfaces for bounded ripples should have the overflow property set to hidden, while surfaces for unbounded ripples should have it set to visible.
         */
        "type": 'bounded' | 'unbounded';
    }
    interface IonRoute {
        /**
          * A navigation hook that is fired when the route tries to enter. Returning `true` allows the navigation to proceed, while returning `false` causes it to be cancelled. Returning a `NavigationHookOptions` object causes the router to redirect to the path specified.
         */
        "beforeEnter"?: NavigationHookCallback;
        /**
          * A navigation hook that is fired when the route tries to leave. Returning `true` allows the navigation to proceed, while returning `false` causes it to be cancelled. Returning a `NavigationHookOptions` object causes the router to redirect to the path specified.
         */
        "beforeLeave"?: NavigationHookCallback;
        /**
          * Name of the component to load/select in the navigation outlet (`ion-tabs`, `ion-nav`) when the route matches.  The value of this property is not always the tagname of the component to load, in `ion-tabs` it actually refers to the name of the `ion-tab` to select.
         */
        "component": string;
        /**
          * A key value `{ 'red': true, 'blue': 'white'}` containing props that should be passed to the defined component when rendered.
         */
        "componentProps"?: { [key: string]: any };
        /**
          * Relative path that needs to match in order for this route to apply.  Accepts paths similar to expressjs so that you can define parameters in the url /foo/:bar where bar would be available in incoming props.
         */
        "url": string;
    }
    interface IonRouteRedirect {
        /**
          * A redirect route, redirects "from" a URL "to" another URL. This property is that "from" URL. It needs to be an exact match of the navigated URL in order to apply.  The path specified in this value is always an absolute path, even if the initial `/` slash is not specified.
         */
        "from": string;
        /**
          * A redirect route, redirects "from" a URL "to" another URL. This property is that "to" URL. When the defined `ion-route-redirect` rule matches, the router will redirect to the path specified in this property.  The value of this property is always an absolute path inside the scope of routes defined in `ion-router` it can't be used with another router or to perform a redirection to a different domain.  Note that this is a virtual redirect, it will not cause a real browser refresh, again, it's a redirect inside the context of ion-router.  When this property is not specified or his value is `undefined` the whole redirect route is noop, even if the "from" value matches.
         */
        "to": string | undefined | null;
    }
    interface IonRouter {
        /**
          * Go back to previous page in the window.history.
         */
        "back": () => Promise<void>;
        "canTransition": () => Promise<string | boolean>;
        "navChanged": (direction: RouterDirection) => Promise<boolean>;
        "printDebug": () => Promise<void>;
        /**
          * Navigate to the specified path.
          * @param path The path to navigate to.
          * @param direction The direction of the animation. Defaults to `"forward"`.
         */
        "push": (path: string, direction?: RouterDirection, animation?: AnimationBuilder) => Promise<boolean>;
        /**
          * The root path to use when matching URLs. By default, this is set to "/", but you can specify an alternate prefix for all URL paths.
         */
        "root": string;
        /**
          * The router can work in two "modes": - With hash: `/index.html#/path/to/page` - Without hash: `/path/to/page`  Using one or another might depend in the requirements of your app and/or where it's deployed.  Usually "hash-less" navigation works better for SEO and it's more user friendly too, but it might requires additional server-side configuration in order to properly work.  On the other side hash-navigation is much easier to deploy, it even works over the file protocol.  By default, this property is `true`, change to `false` to allow hash-less URLs.
         */
        "useHash": boolean;
    }
    interface IonRouterLink {
        /**
          * The color to use from your application's color palette. Default options are: `"primary"`, `"secondary"`, `"tertiary"`, `"success"`, `"warning"`, `"danger"`, `"light"`, `"medium"`, and `"dark"`. For more information on colors, see [theming](/docs/theming/basics).
         */
        "color"?: Color;
        /**
          * Contains a URL or a URL fragment that the hyperlink points to. If this property is set, an anchor tag will be rendered.
         */
        "href": string | undefined;
        /**
          * Specifies the relationship of the target object to the link object. The value is a space-separated list of [link types](https://developer.mozilla.org/en-US/docs/Web/HTML/Link_types).
         */
        "rel": string | undefined;
        /**
          * When using a router, it specifies the transition animation when navigating to another page using `href`.
         */
        "routerAnimation": AnimationBuilder | undefined;
        /**
          * When using a router, it specifies the transition direction when navigating to another page using `href`.
         */
        "routerDirection": RouterDirection;
        /**
          * Specifies where to display the linked URL. Only applies when an `href` is provided. Special keywords: `"_blank"`, `"_self"`, `"_parent"`, `"_top"`.
         */
        "target": string | undefined;
    }
    interface IonRouterOutlet {
        /**
          * If `true`, the router-outlet should animate the transition of components.
         */
        "animated": boolean;
        /**
          * This property allows to create custom transition using AnimationBuilder functions.
         */
        "animation"?: AnimationBuilder;
        "commit": (enteringEl: HTMLElement, leavingEl: HTMLElement | undefined, opts?: RouterOutletOptions) => Promise<boolean>;
        "delegate"?: FrameworkDelegate;
        "getRouteId": () => Promise<RouteID | undefined>;
        /**
          * The mode determines which platform styles to use.
         */
        "mode": "ios" | "md";
        "setRouteId": (id: string, params: ComponentProps | undefined, direction: RouterDirection, animation?: AnimationBuilder) => Promise<RouteWrite>;
        "swipeHandler"?: SwipeGestureHandler;
    }
    interface IonRow {
    }
    interface IonSearchbar {
        /**
          * If `true`, enable searchbar animation.
         */
        "animated": boolean;
        /**
          * Indicates whether and how the text value should be automatically capitalized as it is entered/edited by the user. Available options: `"off"`, `"none"`, `"on"`, `"sentences"`, `"words"`, `"characters"`.
         */
        "autocapitalize": string;
        /**
          * Set the input's autocomplete property.
         */
        "autocomplete": AutocompleteTypes;
        /**
          * Set the input's autocorrect property.
         */
        "autocorrect": 'on' | 'off';
        /**
          * Set the cancel button icon. Only applies to `md` mode. Defaults to `arrow-back-sharp`.
         */
        "cancelButtonIcon": string;
        /**
          * Set the the cancel button text. Only applies to `ios` mode.
         */
        "cancelButtonText": string;
        /**
          * Set the clear icon. Defaults to `close-circle` for `ios` and `close-sharp` for `md`.
         */
        "clearIcon"?: string;
        /**
          * The color to use from your application's color palette. Default options are: `"primary"`, `"secondary"`, `"tertiary"`, `"success"`, `"warning"`, `"danger"`, `"light"`, `"medium"`, and `"dark"`. For more information on colors, see [theming](/docs/theming/basics).
         */
        "color"?: Color;
        /**
          * Set the amount of time, in milliseconds, to wait to trigger the `ionInput` event after each keystroke.
         */
        "debounce"?: number;
        /**
          * If `true`, the user cannot interact with the input.
         */
        "disabled": boolean;
        /**
          * A hint to the browser for which enter key to display. Possible values: `"enter"`, `"done"`, `"go"`, `"next"`, `"previous"`, `"search"`, and `"send"`.
         */
        "enterkeyhint"?: 'enter' | 'done' | 'go' | 'next' | 'previous' | 'search' | 'send';
        /**
          * Returns the native `<input>` element used under the hood.
         */
        "getInputElement": () => Promise<HTMLInputElement>;
        /**
          * A hint to the browser for which keyboard to display. Possible values: `"none"`, `"text"`, `"tel"`, `"url"`, `"email"`, `"numeric"`, `"decimal"`, and `"search"`.
         */
        "inputmode"?: 'none' | 'text' | 'tel' | 'url' | 'email' | 'numeric' | 'decimal' | 'search';
        /**
          * This attribute specifies the maximum number of characters that the user can enter.
         */
        "maxlength"?: number;
        /**
          * This attribute specifies the minimum number of characters that the user can enter.
         */
        "minlength"?: number;
        /**
          * The mode determines which platform styles to use.
         */
        "mode"?: "ios" | "md";
        /**
          * If used in a form, set the name of the control, which is submitted with the form data.
         */
        "name": string;
        /**
          * Set the input's placeholder. `placeholder` can accept either plaintext or HTML as a string. To display characters normally reserved for HTML, they must be escaped. For example `<Ionic>` would become `&lt;Ionic&gt;`  For more information: [Security Documentation](https://ionicframework.com/docs/faq/security)
         */
        "placeholder": string;
        /**
          * The icon to use as the search icon. Defaults to `search-outline` in `ios` mode and `search-sharp` in `md` mode.
         */
        "searchIcon"?: string;
        /**
          * Sets focus on the native `input` in `ion-searchbar`. Use this method instead of the global `input.focus()`.  Developers who wish to focus an input when a page enters should call `setFocus()` in the `ionViewDidEnter()` lifecycle method.  Developers who wish to focus an input when an overlay is presented should call `setFocus` after `didPresent` has resolved.  See [managing focus](/docs/developing/managing-focus) for more information.
         */
        "setFocus": () => Promise<void>;
        /**
          * Sets the behavior for the cancel button. Defaults to `"never"`. Setting to `"focus"` shows the cancel button on focus. Setting to `"never"` hides the cancel button. Setting to `"always"` shows the cancel button regardless of focus state.
         */
        "showCancelButton": 'never' | 'focus' | 'always';
        /**
          * Sets the behavior for the clear button. Defaults to `"focus"`. Setting to `"focus"` shows the clear button on focus if the input is not empty. Setting to `"never"` hides the clear button. Setting to `"always"` shows the clear button regardless of focus state, but only if the input is not empty.
         */
        "showClearButton": 'never' | 'focus' | 'always';
        /**
          * If `true`, enable spellcheck on the input.
         */
        "spellcheck": boolean;
        /**
          * Set the type of the input.
         */
        "type": 'text' | 'password' | 'email' | 'number' | 'search' | 'tel' | 'url';
        /**
          * the value of the searchbar.
         */
        "value"?: string | null;
    }
    interface IonSegment {
        /**
          * The color to use from your application's color palette. Default options are: `"primary"`, `"secondary"`, `"tertiary"`, `"success"`, `"warning"`, `"danger"`, `"light"`, `"medium"`, and `"dark"`. For more information on colors, see [theming](/docs/theming/basics).
         */
        "color"?: Color;
        /**
          * If `true`, the user cannot interact with the segment.
         */
        "disabled": boolean;
        /**
          * The mode determines which platform styles to use.
         */
        "mode"?: "ios" | "md";
        /**
          * If `true`, the segment buttons will overflow and the user can swipe to see them. In addition, this will disable the gesture to drag the indicator between the buttons in order to swipe to see hidden buttons.
         */
        "scrollable": boolean;
        /**
          * If `true`, navigating to an `ion-segment-button` with the keyboard will focus and select the element. If `false`, keyboard navigation will only focus the `ion-segment-button` element.
         */
        "selectOnFocus": boolean;
        /**
          * If `true`, users will be able to swipe between segment buttons to activate them.
         */
        "swipeGesture": boolean;
        /**
          * the value of the segment.
         */
        "value"?: SegmentValue;
    }
    interface IonSegmentButton {
        /**
          * If `true`, the user cannot interact with the segment button.
         */
        "disabled": boolean;
        /**
          * Set the layout of the text and icon in the segment.
         */
        "layout"?: SegmentButtonLayout;
        /**
          * The mode determines which platform styles to use.
         */
        "mode"?: "ios" | "md";
        "setFocus": () => Promise<void>;
        /**
          * The type of the button.
         */
        "type": 'submit' | 'reset' | 'button';
        /**
          * The value of the segment button.
         */
        "value": SegmentValue;
    }
    interface IonSelect {
        /**
          * The text to display on the cancel button.
         */
        "cancelText": string;
        /**
          * The color to use from your application's color palette. Default options are: `"primary"`, `"secondary"`, `"tertiary"`, `"success"`, `"warning"`, `"danger"`, `"light"`, `"medium"`, and `"dark"`. For more information on colors, see [theming](/docs/theming/basics).  This property is only available when using the modern select syntax.
         */
        "color"?: Color;
        /**
          * This property allows developers to specify a custom function or property name for comparing objects when determining the selected option in the ion-select. When not specified, the default behavior will use strict equality (===) for comparison.
         */
        "compareWith"?: string | SelectCompareFn | null;
        /**
          * If `true`, the user cannot interact with the select.
         */
        "disabled": boolean;
        /**
          * The toggle icon to show when the select is open. If defined, the icon rotation behavior in `md` mode will be disabled. If undefined, `toggleIcon` will be used for when the select is both open and closed.
         */
        "expandedIcon"?: string;
        /**
          * The fill for the item. If `"solid"` the item will have a background. If `"outline"` the item will be transparent with a border. Only available in `md` mode.
         */
        "fill"?: 'outline' | 'solid';
        /**
          * The interface the select should use: `action-sheet`, `popover` or `alert`.
         */
        "interface": SelectInterface;
        /**
          * Any additional options that the `alert`, `action-sheet` or `popover` interface can take. See the [ion-alert docs](./alert), the [ion-action-sheet docs](./action-sheet) and the [ion-popover docs](./popover) for the create options for each interface.  Note: `interfaceOptions` will not override `inputs` or `buttons` with the `alert` interface.
         */
        "interfaceOptions": any;
        /**
          * How to pack the label and select within a line. `justify` does not apply when the label and select are on different lines when `labelPlacement` is set to `"floating"` or `"stacked"`. `"start"`: The label and select will appear on the left in LTR and on the right in RTL. `"end"`: The label and select will appear on the right in LTR and on the left in RTL. `"space-between"`: The label and select will appear on opposite ends of the line with space between the two elements.
         */
        "justify": 'start' | 'end' | 'space-between';
        /**
          * The visible label associated with the select.  Use this if you need to render a plaintext label.  The `label` property will take priority over the `label` slot if both are used.
         */
        "label"?: string;
        /**
          * Where to place the label relative to the select. `"start"`: The label will appear to the left of the select in LTR and to the right in RTL. `"end"`: The label will appear to the right of the select in LTR and to the left in RTL. `"floating"`: The label will appear smaller and above the select when the select is focused or it has a value. Otherwise it will appear on top of the select. `"stacked"`: The label will appear smaller and above the select regardless even when the select is blurred or has no value. `"fixed"`: The label has the same behavior as `"start"` except it also has a fixed width. Long text will be truncated with ellipses ("..."). When using `"floating"` or `"stacked"` we recommend initializing the select with either a `value` or a `placeholder`.
         */
        "labelPlacement"?: 'start' | 'end' | 'floating' | 'stacked' | 'fixed';
        /**
          * Set the `legacy` property to `true` to forcibly use the legacy form control markup. Ionic will only opt components in to the modern form markup when they are using either the `aria-label` attribute or the `label` property. As a result, the `legacy` property should only be used as an escape hatch when you want to avoid this automatic opt-in behavior. Note that this property will be removed in an upcoming major release of Ionic, and all form components will be opted-in to using the modern form markup.
         */
        "legacy"?: boolean;
        /**
          * The mode determines which platform styles to use.
         */
        "mode"?: "ios" | "md";
        /**
          * If `true`, the select can accept multiple values.
         */
        "multiple": boolean;
        /**
          * The name of the control, which is submitted with the form data.
         */
        "name": string;
        /**
          * The text to display on the ok button.
         */
        "okText": string;
        /**
          * Open the select overlay. The overlay is either an alert, action sheet, or popover, depending on the `interface` property on the `ion-select`.
          * @param event The user interface event that called the open.
         */
        "open": (event?: UIEvent) => Promise<any>;
        /**
          * The text to display when the select is empty.
         */
        "placeholder"?: string;
        /**
          * The text to display instead of the selected option's value.
         */
        "selectedText"?: string | null;
        /**
          * The shape of the select. If "round" it will have an increased border radius.
         */
        "shape"?: 'round';
        /**
          * The toggle icon to use. Defaults to `chevronExpand` for `ios` mode, or `caretDownSharp` for `md` mode.
         */
        "toggleIcon"?: string;
        /**
          * The value of the select.
         */
        "value"?: any | null;
    }
    interface IonSelectOption {
        /**
          * If `true`, the user cannot interact with the select option. This property does not apply when `interface="action-sheet"` as `ion-action-sheet` does not allow for disabled buttons.
         */
        "disabled": boolean;
        /**
          * The text value of the option.
         */
        "value"?: any | null;
    }
    interface IonSelectPopover {
        /**
          * The header text of the popover
         */
        "header"?: string;
        /**
          * The text content of the popover body
         */
        "message"?: string;
        /**
          * If true, the select accepts multiple values
         */
        "multiple"?: boolean;
        /**
          * An array of options for the popover
         */
        "options": SelectPopoverOption[];
        /**
          * The subheader text of the popover
         */
        "subHeader"?: string;
    }
    interface IonSkeletonText {
        /**
          * If `true`, the skeleton text will animate.
         */
        "animated": boolean;
    }
    interface IonSpinner {
        /**
          * The color to use from your application's color palette. Default options are: `"primary"`, `"secondary"`, `"tertiary"`, `"success"`, `"warning"`, `"danger"`, `"light"`, `"medium"`, and `"dark"`. For more information on colors, see [theming](/docs/theming/basics).
         */
        "color"?: Color;
        /**
          * Duration of the spinner animation in milliseconds. The default varies based on the spinner.
         */
        "duration"?: number;
        /**
          * The name of the SVG spinner to use. If a name is not provided, the platform's default spinner will be used.
         */
        "name"?: SpinnerTypes;
        /**
          * If `true`, the spinner's animation will be paused.
         */
        "paused": boolean;
    }
    interface IonSplitPane {
        /**
          * The `id` of the main content. When using a router this is typically `ion-router-outlet`. When not using a router, this is typically your main view's `ion-content`. This is not the id of the `ion-content` inside of your `ion-menu`.
         */
        "contentId"?: string;
        /**
          * If `true`, the split pane will be hidden.
         */
        "disabled": boolean;
        /**
          * When the split-pane should be shown. Can be a CSS media query expression, or a shortcut expression. Can also be a boolean expression.
         */
        "when": string | boolean;
    }
    interface IonTab {
        "active": boolean;
        /**
          * The component to display inside of the tab.
         */
        "component"?: ComponentRef;
        "delegate"?: FrameworkDelegate;
        /**
          * Set the active component for the tab
         */
        "setActive": () => Promise<void>;
        /**
          * A tab id must be provided for each `ion-tab`. It's used internally to reference the selected tab or by the router to switch between them.
         */
        "tab": string;
    }
    interface IonTabBar {
        /**
          * The color to use from your application's color palette. Default options are: `"primary"`, `"secondary"`, `"tertiary"`, `"success"`, `"warning"`, `"danger"`, `"light"`, `"medium"`, and `"dark"`. For more information on colors, see [theming](/docs/theming/basics).
         */
        "color"?: Color;
        /**
          * The mode determines which platform styles to use.
         */
        "mode"?: "ios" | "md";
        /**
          * The selected tab component
         */
        "selectedTab"?: string;
        /**
          * If `true`, the tab bar will be translucent. Only applies when the mode is `"ios"` and the device supports [`backdrop-filter`](https://developer.mozilla.org/en-US/docs/Web/CSS/backdrop-filter#Browser_compatibility).
         */
        "translucent": boolean;
    }
    interface IonTabButton {
        /**
          * If `true`, the user cannot interact with the tab button.
         */
        "disabled": boolean;
        /**
          * This attribute instructs browsers to download a URL instead of navigating to it, so the user will be prompted to save it as a local file. If the attribute has a value, it is used as the pre-filled file name in the Save prompt (the user can still change the file name if they want).
         */
        "download": string | undefined;
        /**
          * Contains a URL or a URL fragment that the hyperlink points to. If this property is set, an anchor tag will be rendered.
         */
        "href": string | undefined;
        /**
          * Set the layout of the text and icon in the tab bar. It defaults to `"icon-top"`.
         */
        "layout"?: TabButtonLayout;
        /**
          * The mode determines which platform styles to use.
         */
        "mode"?: "ios" | "md";
        /**
          * Specifies the relationship of the target object to the link object. The value is a space-separated list of [link types](https://developer.mozilla.org/en-US/docs/Web/HTML/Link_types).
         */
        "rel": string | undefined;
        /**
          * The selected tab component
         */
        "selected": boolean;
        /**
          * A tab id must be provided for each `ion-tab`. It's used internally to reference the selected tab or by the router to switch between them.
         */
        "tab"?: string;
        /**
          * Specifies where to display the linked URL. Only applies when an `href` is provided. Special keywords: `"_blank"`, `"_self"`, `"_parent"`, `"_top"`.
         */
        "target": string | undefined;
    }
    interface IonTabs {
        "getRouteId": () => Promise<RouteID | undefined>;
        /**
          * Get the currently selected tab. This method is only available for vanilla JavaScript projects. The Angular, React, and Vue implementations of tabs are coupled to each framework's router.
         */
        "getSelected": () => Promise<string | undefined>;
        /**
          * Get a specific tab by the value of its `tab` property or an element reference. This method is only available for vanilla JavaScript projects. The Angular, React, and Vue implementations of tabs are coupled to each framework's router.
          * @param tab The tab instance to select. If passed a string, it should be the value of the tab's `tab` property.
         */
        "getTab": (tab: string | HTMLIonTabElement) => Promise<HTMLIonTabElement | undefined>;
        /**
          * Select a tab by the value of its `tab` property or an element reference. This method is only available for vanilla JavaScript projects. The Angular, React, and Vue implementations of tabs are coupled to each framework's router.
          * @param tab The tab instance to select. If passed a string, it should be the value of the tab's `tab` property.
         */
        "select": (tab: string | HTMLIonTabElement) => Promise<boolean>;
        "setRouteId": (id: string) => Promise<RouteWrite>;
        "useRouter": boolean;
    }
    interface IonText {
        /**
          * The color to use from your application's color palette. Default options are: `"primary"`, `"secondary"`, `"tertiary"`, `"success"`, `"warning"`, `"danger"`, `"light"`, `"medium"`, and `"dark"`. For more information on colors, see [theming](/docs/theming/basics).
         */
        "color"?: Color;
        /**
          * The mode determines which platform styles to use.
         */
        "mode"?: "ios" | "md";
    }
    interface IonTextarea {
        /**
          * If `true`, the textarea container will grow and shrink based on the contents of the textarea.
         */
        "autoGrow": boolean;
        /**
          * Indicates whether and how the text value should be automatically capitalized as it is entered/edited by the user. Available options: `"off"`, `"none"`, `"on"`, `"sentences"`, `"words"`, `"characters"`.
         */
        "autocapitalize": string;
        /**
          * Sets the [`autofocus` attribute](https://developer.mozilla.org/en-US/docs/Web/HTML/Global_attributes/autofocus) on the native input element.  This may not be sufficient for the element to be focused on page load. See [managing focus](/docs/developing/managing-focus) for more information.
         */
        "autofocus": boolean;
        /**
          * If `true`, the value will be cleared after focus upon edit.
         */
        "clearOnEdit": boolean;
        /**
          * The color to use from your application's color palette. Default options are: `"primary"`, `"secondary"`, `"tertiary"`, `"success"`, `"warning"`, `"danger"`, `"light"`, `"medium"`, and `"dark"`. For more information on colors, see [theming](/docs/theming/basics).
         */
        "color"?: Color;
        /**
          * The visible width of the text control, in average character widths. If it is specified, it must be a positive integer.
         */
        "cols"?: number;
        /**
          * If `true`, a character counter will display the ratio of characters used and the total character limit. Developers must also set the `maxlength` property for the counter to be calculated correctly.
         */
        "counter": boolean;
        /**
          * A callback used to format the counter text. By default the counter text is set to "itemLength / maxLength".  See https://ionicframework.com/docs/troubleshooting/runtime#accessing-this if you need to access `this` from within the callback.
         */
        "counterFormatter"?: (inputLength: number, maxLength: number) => string;
        /**
          * Set the amount of time, in milliseconds, to wait to trigger the `ionInput` event after each keystroke.
         */
        "debounce"?: number;
        /**
          * If `true`, the user cannot interact with the textarea.
         */
        "disabled": boolean;
        /**
          * A hint to the browser for which enter key to display. Possible values: `"enter"`, `"done"`, `"go"`, `"next"`, `"previous"`, `"search"`, and `"send"`.
         */
        "enterkeyhint"?: 'enter' | 'done' | 'go' | 'next' | 'previous' | 'search' | 'send';
        /**
          * Text that is placed under the textarea and displayed when an error is detected.
         */
        "errorText"?: string;
        /**
          * The fill for the item. If `"solid"` the item will have a background. If `"outline"` the item will be transparent with a border. Only available in `md` mode.
         */
        "fill"?: 'outline' | 'solid';
        /**
          * Returns the native `<textarea>` element used under the hood.
         */
        "getInputElement": () => Promise<HTMLTextAreaElement>;
        /**
          * Text that is placed under the textarea and displayed when no error is detected.
         */
        "helperText"?: string;
        /**
          * A hint to the browser for which keyboard to display. Possible values: `"none"`, `"text"`, `"tel"`, `"url"`, `"email"`, `"numeric"`, `"decimal"`, and `"search"`.
         */
        "inputmode"?: 'none' | 'text' | 'tel' | 'url' | 'email' | 'numeric' | 'decimal' | 'search';
        /**
          * The visible label associated with the textarea.  Use this if you need to render a plaintext label.  The `label` property will take priority over the `label` slot if both are used.
         */
        "label"?: string;
        /**
          * Where to place the label relative to the textarea. `"start"`: The label will appear to the left of the textarea in LTR and to the right in RTL. `"end"`: The label will appear to the right of the textarea in LTR and to the left in RTL. `"floating"`: The label will appear smaller and above the textarea when the textarea is focused or it has a value. Otherwise it will appear on top of the textarea. `"stacked"`: The label will appear smaller and above the textarea regardless even when the textarea is blurred or has no value. `"fixed"`: The label has the same behavior as `"start"` except it also has a fixed width. Long text will be truncated with ellipses ("...").
         */
        "labelPlacement": 'start' | 'end' | 'floating' | 'stacked' | 'fixed';
        /**
          * Set the `legacy` property to `true` to forcibly use the legacy form control markup. Ionic will only opt components in to the modern form markup when they are using either the `aria-label` attribute or the default slot that contains the label text. As a result, the `legacy` property should only be used as an escape hatch when you want to avoid this automatic opt-in behavior. Note that this property will be removed in an upcoming major release of Ionic, and all form components will be opted-in to using the modern form markup.
         */
        "legacy"?: boolean;
        /**
          * This attribute specifies the maximum number of characters that the user can enter.
         */
        "maxlength"?: number;
        /**
          * This attribute specifies the minimum number of characters that the user can enter.
         */
        "minlength"?: number;
        /**
          * The mode determines which platform styles to use.
         */
        "mode"?: "ios" | "md";
        /**
          * The name of the control, which is submitted with the form data.
         */
        "name": string;
        /**
          * Instructional text that shows before the input has a value.
         */
        "placeholder"?: string;
        /**
          * If `true`, the user cannot modify the value.
         */
        "readonly": boolean;
        /**
          * If `true`, the user must fill in a value before submitting a form.
         */
        "required": boolean;
        /**
          * The number of visible text lines for the control.
         */
        "rows"?: number;
        /**
          * Sets focus on the native `textarea` in `ion-textarea`. Use this method instead of the global `textarea.focus()`.  See [managing focus](/docs/developing/managing-focus) for more information.
         */
        "setFocus": () => Promise<void>;
        /**
          * The shape of the textarea. If "round" it will have an increased border radius.
         */
        "shape"?: 'round';
        /**
          * If `true`, the element will have its spelling and grammar checked.
         */
        "spellcheck": boolean;
        /**
          * The value of the textarea.
         */
        "value"?: string | null;
        /**
          * Indicates how the control wraps text.
         */
        "wrap"?: 'hard' | 'soft' | 'off';
    }
    interface IonThumbnail {
    }
    interface IonTitle {
        /**
          * The color to use from your application's color palette. Default options are: `"primary"`, `"secondary"`, `"tertiary"`, `"success"`, `"warning"`, `"danger"`, `"light"`, `"medium"`, and `"dark"`. For more information on colors, see [theming](/docs/theming/basics).
         */
        "color"?: Color;
        /**
          * The size of the toolbar title.
         */
        "size"?: 'large' | 'small';
    }
    interface IonToast {
        /**
          * If `true`, the toast will animate.
         */
        "animated": boolean;
        /**
          * An array of buttons for the toast.
         */
        "buttons"?: (ToastButton | string)[];
        /**
          * The color to use from your application's color palette. Default options are: `"primary"`, `"secondary"`, `"tertiary"`, `"success"`, `"warning"`, `"danger"`, `"light"`, `"medium"`, and `"dark"`. For more information on colors, see [theming](/docs/theming/basics).
         */
        "color"?: Color;
        /**
          * Additional classes to apply for custom CSS. If multiple classes are provided they should be separated by spaces.
         */
        "cssClass"?: string | string[];
        "delegate"?: FrameworkDelegate;
        /**
          * Dismiss the toast overlay after it has been presented.
          * @param data Any data to emit in the dismiss events.
          * @param role The role of the element that is dismissing the toast. This can be useful in a button handler for determining which button was clicked to dismiss the toast. Some examples include: ``"cancel"`, `"destructive"`, "selected"`, and `"backdrop"`.  This is a no-op if the overlay has not been presented yet. If you want to remove an overlay from the DOM that was never presented, use the [remove](https://developer.mozilla.org/en-US/docs/Web/API/Element/remove) method.
         */
        "dismiss": (data?: any, role?: string) => Promise<boolean>;
        /**
          * How many milliseconds to wait before hiding the toast. By default, it will show until `dismiss()` is called.
         */
        "duration": number;
        /**
          * Animation to use when the toast is presented.
         */
        "enterAnimation"?: AnimationBuilder;
        "hasController": boolean;
        /**
          * Header to be shown in the toast.
         */
        "header"?: string;
        /**
          * Additional attributes to pass to the toast.
         */
        "htmlAttributes"?: { [key: string]: any };
        /**
          * The name of the icon to display, or the path to a valid SVG file. See `ion-icon`. https://ionic.io/ionicons
         */
        "icon"?: string;
        /**
          * If `true`, the toast will open. If `false`, the toast will close. Use this if you need finer grained control over presentation, otherwise just use the toastController or the `trigger` property. Note: `isOpen` will not automatically be set back to `false` when the toast dismisses. You will need to do that in your code.
         */
        "isOpen": boolean;
        /**
          * If `true`, the keyboard will be automatically dismissed when the overlay is presented.
         */
        "keyboardClose": boolean;
        /**
          * Defines how the message and buttons are laid out in the toast. 'baseline': The message and the buttons will appear on the same line. Message text may wrap within the message container. 'stacked': The buttons containers and message will stack on top of each other. Use this if you have long text in your buttons.
         */
        "layout": ToastLayout;
        /**
          * Animation to use when the toast is dismissed.
         */
        "leaveAnimation"?: AnimationBuilder;
        /**
          * Message to be shown in the toast. This property accepts custom HTML as a string. Content is parsed as plaintext by default. `innerHTMLTemplatesEnabled` must be set to `true` in the Ionic config before custom HTML can be used.
         */
        "message"?: string | IonicSafeString;
        /**
          * The mode determines which platform styles to use.
         */
        "mode"?: "ios" | "md";
        /**
          * Returns a promise that resolves when the toast did dismiss.
         */
        "onDidDismiss": <T = any>() => Promise<OverlayEventDetail<T>>;
        /**
          * Returns a promise that resolves when the toast will dismiss.
         */
        "onWillDismiss": <T = any>() => Promise<OverlayEventDetail<T>>;
        "overlayIndex": number;
        /**
          * The starting position of the toast on the screen. Can be tweaked further using the `positionAnchor` property.
         */
        "position": ToastPosition;
        /**
          * The element to anchor the toast's position to. Can be set as a direct reference or the ID of the element. With `position="bottom"`, the toast will sit above the chosen element. With `position="top"`, the toast will sit below the chosen element. With `position="middle"`, the value of `positionAnchor` is ignored.
         */
        "positionAnchor"?: HTMLElement | string;
        /**
          * Present the toast overlay after it has been created.
         */
        "present": () => Promise<void>;
        /**
          * If set to 'vertical', the Toast can be dismissed with a swipe gesture. The swipe direction is determined by the value of the `position` property: `top`: The Toast can be swiped up to dismiss. `bottom`: The Toast can be swiped down to dismiss. `middle`: The Toast can be swiped up or down to dismiss.
         */
        "swipeGesture"?: ToastSwipeGestureDirection;
        /**
          * If `true`, the toast will be translucent. Only applies when the mode is `"ios"` and the device supports [`backdrop-filter`](https://developer.mozilla.org/en-US/docs/Web/CSS/backdrop-filter#Browser_compatibility).
         */
        "translucent": boolean;
        /**
          * An ID corresponding to the trigger element that causes the toast to open when clicked.
         */
        "trigger": string | undefined;
    }
    interface IonToggle {
        /**
          * How to control the alignment of the toggle and label on the cross axis. `"start"`: The label and control will appear on the left of the cross axis in LTR, and on the right side in RTL. `"center"`: The label and control will appear at the center of the cross axis in both LTR and RTL.
         */
        "alignment": 'start' | 'center';
        /**
          * If `true`, the toggle is selected.
         */
        "checked": boolean;
        /**
          * The color to use from your application's color palette. Default options are: `"primary"`, `"secondary"`, `"tertiary"`, `"success"`, `"warning"`, `"danger"`, `"light"`, `"medium"`, and `"dark"`. For more information on colors, see [theming](/docs/theming/basics).
         */
        "color"?: Color;
        /**
          * If `true`, the user cannot interact with the toggle.
         */
        "disabled": boolean;
        /**
          * Enables the on/off accessibility switch labels within the toggle.
         */
        "enableOnOffLabels": boolean | undefined;
        /**
          * How to pack the label and toggle within a line. `"start"`: The label and toggle will appear on the left in LTR and on the right in RTL. `"end"`: The label and toggle will appear on the right in LTR and on the left in RTL. `"space-between"`: The label and toggle will appear on opposite ends of the line with space between the two elements.
         */
        "justify": 'start' | 'end' | 'space-between';
        /**
          * Where to place the label relative to the input. `"start"`: The label will appear to the left of the toggle in LTR and to the right in RTL. `"end"`: The label will appear to the right of the toggle in LTR and to the left in RTL. `"fixed"`: The label has the same behavior as `"start"` except it also has a fixed width. Long text will be truncated with ellipses ("..."). `"stacked"`: The label will appear above the toggle regardless of the direction. The alignment of the label can be controlled with the `alignment` property.
         */
        "labelPlacement": 'start' | 'end' | 'fixed' | 'stacked';
        /**
          * Set the `legacy` property to `true` to forcibly use the legacy form control markup. Ionic will only opt components in to the modern form markup when they are using either the `aria-label` attribute or the default slot that contains the label text. As a result, the `legacy` property should only be used as an escape hatch when you want to avoid this automatic opt-in behavior. Note that this property will be removed in an upcoming major release of Ionic, and all form components will be opted-in to using the modern form markup.
         */
        "legacy"?: boolean;
        /**
          * The mode determines which platform styles to use.
         */
        "mode"?: "ios" | "md";
        /**
          * The name of the control, which is submitted with the form data.
         */
        "name": string;
        /**
          * The value of the toggle does not mean if it's checked or not, use the `checked` property for that.  The value of a toggle is analogous to the value of a `<input type="checkbox">`, it's only used when the toggle participates in a native `<form>`.
         */
        "value"?: string | null;
    }
    interface IonToolbar {
        /**
          * The color to use from your application's color palette. Default options are: `"primary"`, `"secondary"`, `"tertiary"`, `"success"`, `"warning"`, `"danger"`, `"light"`, `"medium"`, and `"dark"`. For more information on colors, see [theming](/docs/theming/basics).
         */
        "color"?: Color;
        /**
          * The mode determines which platform styles to use.
         */
        "mode"?: "ios" | "md";
    }
}
export interface IonAccordionGroupCustomEvent<T> extends CustomEvent<T> {
    detail: T;
    target: HTMLIonAccordionGroupElement;
}
export interface IonActionSheetCustomEvent<T> extends CustomEvent<T> {
    detail: T;
    target: HTMLIonActionSheetElement;
}
export interface IonAlertCustomEvent<T> extends CustomEvent<T> {
    detail: T;
    target: HTMLIonAlertElement;
}
export interface IonBackdropCustomEvent<T> extends CustomEvent<T> {
    detail: T;
    target: HTMLIonBackdropElement;
}
export interface IonBreadcrumbCustomEvent<T> extends CustomEvent<T> {
    detail: T;
    target: HTMLIonBreadcrumbElement;
}
export interface IonBreadcrumbsCustomEvent<T> extends CustomEvent<T> {
    detail: T;
    target: HTMLIonBreadcrumbsElement;
}
export interface IonButtonCustomEvent<T> extends CustomEvent<T> {
    detail: T;
    target: HTMLIonButtonElement;
}
export interface IonCheckboxCustomEvent<T> extends CustomEvent<T> {
    detail: T;
    target: HTMLIonCheckboxElement;
}
export interface IonContentCustomEvent<T> extends CustomEvent<T> {
    detail: T;
    target: HTMLIonContentElement;
}
export interface IonDatetimeCustomEvent<T> extends CustomEvent<T> {
    detail: T;
    target: HTMLIonDatetimeElement;
}
export interface IonFabButtonCustomEvent<T> extends CustomEvent<T> {
    detail: T;
    target: HTMLIonFabButtonElement;
}
export interface IonImgCustomEvent<T> extends CustomEvent<T> {
    detail: T;
    target: HTMLIonImgElement;
}
export interface IonInfiniteScrollCustomEvent<T> extends CustomEvent<T> {
    detail: T;
    target: HTMLIonInfiniteScrollElement;
}
export interface IonInputCustomEvent<T> extends CustomEvent<T> {
    detail: T;
    target: HTMLIonInputElement;
}
export interface IonItemOptionsCustomEvent<T> extends CustomEvent<T> {
    detail: T;
    target: HTMLIonItemOptionsElement;
}
export interface IonItemSlidingCustomEvent<T> extends CustomEvent<T> {
    detail: T;
    target: HTMLIonItemSlidingElement;
}
export interface IonLabelCustomEvent<T> extends CustomEvent<T> {
    detail: T;
    target: HTMLIonLabelElement;
}
export interface IonLoadingCustomEvent<T> extends CustomEvent<T> {
    detail: T;
    target: HTMLIonLoadingElement;
}
export interface IonMenuCustomEvent<T> extends CustomEvent<T> {
    detail: T;
    target: HTMLIonMenuElement;
}
export interface IonModalCustomEvent<T> extends CustomEvent<T> {
    detail: T;
    target: HTMLIonModalElement;
}
export interface IonNavCustomEvent<T> extends CustomEvent<T> {
    detail: T;
    target: HTMLIonNavElement;
}
export interface IonPickerCustomEvent<T> extends CustomEvent<T> {
    detail: T;
    target: HTMLIonPickerElement;
}
export interface IonPickerColumnCustomEvent<T> extends CustomEvent<T> {
    detail: T;
    target: HTMLIonPickerColumnElement;
}
export interface IonPickerColumnInternalCustomEvent<T> extends CustomEvent<T> {
    detail: T;
    target: HTMLIonPickerColumnInternalElement;
}
export interface IonPickerInternalCustomEvent<T> extends CustomEvent<T> {
    detail: T;
    target: HTMLIonPickerInternalElement;
}
export interface IonPopoverCustomEvent<T> extends CustomEvent<T> {
    detail: T;
    target: HTMLIonPopoverElement;
}
export interface IonRadioCustomEvent<T> extends CustomEvent<T> {
    detail: T;
    target: HTMLIonRadioElement;
}
export interface IonRadioGroupCustomEvent<T> extends CustomEvent<T> {
    detail: T;
    target: HTMLIonRadioGroupElement;
}
export interface IonRangeCustomEvent<T> extends CustomEvent<T> {
    detail: T;
    target: HTMLIonRangeElement;
}
export interface IonRefresherCustomEvent<T> extends CustomEvent<T> {
    detail: T;
    target: HTMLIonRefresherElement;
}
export interface IonReorderGroupCustomEvent<T> extends CustomEvent<T> {
    detail: T;
    target: HTMLIonReorderGroupElement;
}
export interface IonRouteCustomEvent<T> extends CustomEvent<T> {
    detail: T;
    target: HTMLIonRouteElement;
}
export interface IonRouteRedirectCustomEvent<T> extends CustomEvent<T> {
    detail: T;
    target: HTMLIonRouteRedirectElement;
}
export interface IonRouterCustomEvent<T> extends CustomEvent<T> {
    detail: T;
    target: HTMLIonRouterElement;
}
export interface IonRouterOutletCustomEvent<T> extends CustomEvent<T> {
    detail: T;
    target: HTMLIonRouterOutletElement;
}
export interface IonSearchbarCustomEvent<T> extends CustomEvent<T> {
    detail: T;
    target: HTMLIonSearchbarElement;
}
export interface IonSegmentCustomEvent<T> extends CustomEvent<T> {
    detail: T;
    target: HTMLIonSegmentElement;
}
export interface IonSelectCustomEvent<T> extends CustomEvent<T> {
    detail: T;
    target: HTMLIonSelectElement;
}
export interface IonSkeletonTextCustomEvent<T> extends CustomEvent<T> {
    detail: T;
    target: HTMLIonSkeletonTextElement;
}
export interface IonSplitPaneCustomEvent<T> extends CustomEvent<T> {
    detail: T;
    target: HTMLIonSplitPaneElement;
}
export interface IonTabBarCustomEvent<T> extends CustomEvent<T> {
    detail: T;
    target: HTMLIonTabBarElement;
}
export interface IonTabButtonCustomEvent<T> extends CustomEvent<T> {
    detail: T;
    target: HTMLIonTabButtonElement;
}
export interface IonTabsCustomEvent<T> extends CustomEvent<T> {
    detail: T;
    target: HTMLIonTabsElement;
}
export interface IonTextareaCustomEvent<T> extends CustomEvent<T> {
    detail: T;
    target: HTMLIonTextareaElement;
}
export interface IonTitleCustomEvent<T> extends CustomEvent<T> {
    detail: T;
    target: HTMLIonTitleElement;
}
export interface IonToastCustomEvent<T> extends CustomEvent<T> {
    detail: T;
    target: HTMLIonToastElement;
}
export interface IonToggleCustomEvent<T> extends CustomEvent<T> {
    detail: T;
    target: HTMLIonToggleElement;
}
declare global {
    interface HTMLIonAccordionElement extends Components.IonAccordion, HTMLStencilElement {
    }
    var HTMLIonAccordionElement: {
        prototype: HTMLIonAccordionElement;
        new (): HTMLIonAccordionElement;
    };
    interface HTMLIonAccordionGroupElementEventMap {
        "ionChange": AccordionGroupChangeEventDetail;
        "ionValueChange": AccordionGroupChangeEventDetail;
    }
    interface HTMLIonAccordionGroupElement extends Components.IonAccordionGroup, HTMLStencilElement {
        addEventListener<K extends keyof HTMLIonAccordionGroupElementEventMap>(type: K, listener: (this: HTMLIonAccordionGroupElement, ev: IonAccordionGroupCustomEvent<HTMLIonAccordionGroupElementEventMap[K]>) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener<K extends keyof DocumentEventMap>(type: K, listener: (this: Document, ev: DocumentEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener<K extends keyof HTMLElementEventMap>(type: K, listener: (this: HTMLElement, ev: HTMLElementEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | AddEventListenerOptions): void;
        removeEventListener<K extends keyof HTMLIonAccordionGroupElementEventMap>(type: K, listener: (this: HTMLIonAccordionGroupElement, ev: IonAccordionGroupCustomEvent<HTMLIonAccordionGroupElementEventMap[K]>) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener<K extends keyof DocumentEventMap>(type: K, listener: (this: Document, ev: DocumentEventMap[K]) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener<K extends keyof HTMLElementEventMap>(type: K, listener: (this: HTMLElement, ev: HTMLElementEventMap[K]) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | EventListenerOptions): void;
    }
    var HTMLIonAccordionGroupElement: {
        prototype: HTMLIonAccordionGroupElement;
        new (): HTMLIonAccordionGroupElement;
    };
    interface HTMLIonActionSheetElementEventMap {
        "ionActionSheetDidPresent": void;
        "ionActionSheetWillPresent": void;
        "ionActionSheetWillDismiss": OverlayEventDetail;
        "ionActionSheetDidDismiss": OverlayEventDetail;
        "didPresent": void;
        "willPresent": void;
        "willDismiss": OverlayEventDetail;
        "didDismiss": OverlayEventDetail;
    }
    interface HTMLIonActionSheetElement extends Components.IonActionSheet, HTMLStencilElement {
        addEventListener<K extends keyof HTMLIonActionSheetElementEventMap>(type: K, listener: (this: HTMLIonActionSheetElement, ev: IonActionSheetCustomEvent<HTMLIonActionSheetElementEventMap[K]>) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener<K extends keyof DocumentEventMap>(type: K, listener: (this: Document, ev: DocumentEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener<K extends keyof HTMLElementEventMap>(type: K, listener: (this: HTMLElement, ev: HTMLElementEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | AddEventListenerOptions): void;
        removeEventListener<K extends keyof HTMLIonActionSheetElementEventMap>(type: K, listener: (this: HTMLIonActionSheetElement, ev: IonActionSheetCustomEvent<HTMLIonActionSheetElementEventMap[K]>) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener<K extends keyof DocumentEventMap>(type: K, listener: (this: Document, ev: DocumentEventMap[K]) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener<K extends keyof HTMLElementEventMap>(type: K, listener: (this: HTMLElement, ev: HTMLElementEventMap[K]) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | EventListenerOptions): void;
    }
    var HTMLIonActionSheetElement: {
        prototype: HTMLIonActionSheetElement;
        new (): HTMLIonActionSheetElement;
    };
    interface HTMLIonAlertElementEventMap {
        "ionAlertDidPresent": void;
        "ionAlertWillPresent": void;
        "ionAlertWillDismiss": OverlayEventDetail;
        "ionAlertDidDismiss": OverlayEventDetail;
        "didPresent": void;
        "willPresent": void;
        "willDismiss": OverlayEventDetail;
        "didDismiss": OverlayEventDetail;
    }
    interface HTMLIonAlertElement extends Components.IonAlert, HTMLStencilElement {
        addEventListener<K extends keyof HTMLIonAlertElementEventMap>(type: K, listener: (this: HTMLIonAlertElement, ev: IonAlertCustomEvent<HTMLIonAlertElementEventMap[K]>) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener<K extends keyof DocumentEventMap>(type: K, listener: (this: Document, ev: DocumentEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener<K extends keyof HTMLElementEventMap>(type: K, listener: (this: HTMLElement, ev: HTMLElementEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | AddEventListenerOptions): void;
        removeEventListener<K extends keyof HTMLIonAlertElementEventMap>(type: K, listener: (this: HTMLIonAlertElement, ev: IonAlertCustomEvent<HTMLIonAlertElementEventMap[K]>) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener<K extends keyof DocumentEventMap>(type: K, listener: (this: Document, ev: DocumentEventMap[K]) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener<K extends keyof HTMLElementEventMap>(type: K, listener: (this: HTMLElement, ev: HTMLElementEventMap[K]) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | EventListenerOptions): void;
    }
    var HTMLIonAlertElement: {
        prototype: HTMLIonAlertElement;
        new (): HTMLIonAlertElement;
    };
    interface HTMLIonAppElement extends Components.IonApp, HTMLStencilElement {
    }
    var HTMLIonAppElement: {
        prototype: HTMLIonAppElement;
        new (): HTMLIonAppElement;
    };
    interface HTMLIonAvatarElement extends Components.IonAvatar, HTMLStencilElement {
    }
    var HTMLIonAvatarElement: {
        prototype: HTMLIonAvatarElement;
        new (): HTMLIonAvatarElement;
    };
    interface HTMLIonBackButtonElement extends Components.IonBackButton, HTMLStencilElement {
    }
    var HTMLIonBackButtonElement: {
        prototype: HTMLIonBackButtonElement;
        new (): HTMLIonBackButtonElement;
    };
    interface HTMLIonBackdropElementEventMap {
        "ionBackdropTap": void;
    }
    interface HTMLIonBackdropElement extends Components.IonBackdrop, HTMLStencilElement {
        addEventListener<K extends keyof HTMLIonBackdropElementEventMap>(type: K, listener: (this: HTMLIonBackdropElement, ev: IonBackdropCustomEvent<HTMLIonBackdropElementEventMap[K]>) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener<K extends keyof DocumentEventMap>(type: K, listener: (this: Document, ev: DocumentEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener<K extends keyof HTMLElementEventMap>(type: K, listener: (this: HTMLElement, ev: HTMLElementEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | AddEventListenerOptions): void;
        removeEventListener<K extends keyof HTMLIonBackdropElementEventMap>(type: K, listener: (this: HTMLIonBackdropElement, ev: IonBackdropCustomEvent<HTMLIonBackdropElementEventMap[K]>) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener<K extends keyof DocumentEventMap>(type: K, listener: (this: Document, ev: DocumentEventMap[K]) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener<K extends keyof HTMLElementEventMap>(type: K, listener: (this: HTMLElement, ev: HTMLElementEventMap[K]) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | EventListenerOptions): void;
    }
    var HTMLIonBackdropElement: {
        prototype: HTMLIonBackdropElement;
        new (): HTMLIonBackdropElement;
    };
    interface HTMLIonBadgeElement extends Components.IonBadge, HTMLStencilElement {
    }
    var HTMLIonBadgeElement: {
        prototype: HTMLIonBadgeElement;
        new (): HTMLIonBadgeElement;
    };
    interface HTMLIonBreadcrumbElementEventMap {
        "ionFocus": void;
        "ionBlur": void;
        "collapsedClick": BreadcrumbCollapsedClickEventDetail;
    }
    interface HTMLIonBreadcrumbElement extends Components.IonBreadcrumb, HTMLStencilElement {
        addEventListener<K extends keyof HTMLIonBreadcrumbElementEventMap>(type: K, listener: (this: HTMLIonBreadcrumbElement, ev: IonBreadcrumbCustomEvent<HTMLIonBreadcrumbElementEventMap[K]>) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener<K extends keyof DocumentEventMap>(type: K, listener: (this: Document, ev: DocumentEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener<K extends keyof HTMLElementEventMap>(type: K, listener: (this: HTMLElement, ev: HTMLElementEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | AddEventListenerOptions): void;
        removeEventListener<K extends keyof HTMLIonBreadcrumbElementEventMap>(type: K, listener: (this: HTMLIonBreadcrumbElement, ev: IonBreadcrumbCustomEvent<HTMLIonBreadcrumbElementEventMap[K]>) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener<K extends keyof DocumentEventMap>(type: K, listener: (this: Document, ev: DocumentEventMap[K]) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener<K extends keyof HTMLElementEventMap>(type: K, listener: (this: HTMLElement, ev: HTMLElementEventMap[K]) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | EventListenerOptions): void;
    }
    var HTMLIonBreadcrumbElement: {
        prototype: HTMLIonBreadcrumbElement;
        new (): HTMLIonBreadcrumbElement;
    };
    interface HTMLIonBreadcrumbsElementEventMap {
        "ionCollapsedClick": BreadcrumbCollapsedClickEventDetail;
    }
    interface HTMLIonBreadcrumbsElement extends Components.IonBreadcrumbs, HTMLStencilElement {
        addEventListener<K extends keyof HTMLIonBreadcrumbsElementEventMap>(type: K, listener: (this: HTMLIonBreadcrumbsElement, ev: IonBreadcrumbsCustomEvent<HTMLIonBreadcrumbsElementEventMap[K]>) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener<K extends keyof DocumentEventMap>(type: K, listener: (this: Document, ev: DocumentEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener<K extends keyof HTMLElementEventMap>(type: K, listener: (this: HTMLElement, ev: HTMLElementEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | AddEventListenerOptions): void;
        removeEventListener<K extends keyof HTMLIonBreadcrumbsElementEventMap>(type: K, listener: (this: HTMLIonBreadcrumbsElement, ev: IonBreadcrumbsCustomEvent<HTMLIonBreadcrumbsElementEventMap[K]>) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener<K extends keyof DocumentEventMap>(type: K, listener: (this: Document, ev: DocumentEventMap[K]) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener<K extends keyof HTMLElementEventMap>(type: K, listener: (this: HTMLElement, ev: HTMLElementEventMap[K]) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | EventListenerOptions): void;
    }
    var HTMLIonBreadcrumbsElement: {
        prototype: HTMLIonBreadcrumbsElement;
        new (): HTMLIonBreadcrumbsElement;
    };
    interface HTMLIonButtonElementEventMap {
        "ionFocus": void;
        "ionBlur": void;
    }
    interface HTMLIonButtonElement extends Components.IonButton, HTMLStencilElement {
        addEventListener<K extends keyof HTMLIonButtonElementEventMap>(type: K, listener: (this: HTMLIonButtonElement, ev: IonButtonCustomEvent<HTMLIonButtonElementEventMap[K]>) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener<K extends keyof DocumentEventMap>(type: K, listener: (this: Document, ev: DocumentEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener<K extends keyof HTMLElementEventMap>(type: K, listener: (this: HTMLElement, ev: HTMLElementEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | AddEventListenerOptions): void;
        removeEventListener<K extends keyof HTMLIonButtonElementEventMap>(type: K, listener: (this: HTMLIonButtonElement, ev: IonButtonCustomEvent<HTMLIonButtonElementEventMap[K]>) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener<K extends keyof DocumentEventMap>(type: K, listener: (this: Document, ev: DocumentEventMap[K]) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener<K extends keyof HTMLElementEventMap>(type: K, listener: (this: HTMLElement, ev: HTMLElementEventMap[K]) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | EventListenerOptions): void;
    }
    var HTMLIonButtonElement: {
        prototype: HTMLIonButtonElement;
        new (): HTMLIonButtonElement;
    };
    interface HTMLIonButtonsElement extends Components.IonButtons, HTMLStencilElement {
    }
    var HTMLIonButtonsElement: {
        prototype: HTMLIonButtonsElement;
        new (): HTMLIonButtonsElement;
    };
    interface HTMLIonCardElement extends Components.IonCard, HTMLStencilElement {
    }
    var HTMLIonCardElement: {
        prototype: HTMLIonCardElement;
        new (): HTMLIonCardElement;
    };
    interface HTMLIonCardContentElement extends Components.IonCardContent, HTMLStencilElement {
    }
    var HTMLIonCardContentElement: {
        prototype: HTMLIonCardContentElement;
        new (): HTMLIonCardContentElement;
    };
    interface HTMLIonCardHeaderElement extends Components.IonCardHeader, HTMLStencilElement {
    }
    var HTMLIonCardHeaderElement: {
        prototype: HTMLIonCardHeaderElement;
        new (): HTMLIonCardHeaderElement;
    };
    interface HTMLIonCardSubtitleElement extends Components.IonCardSubtitle, HTMLStencilElement {
    }
    var HTMLIonCardSubtitleElement: {
        prototype: HTMLIonCardSubtitleElement;
        new (): HTMLIonCardSubtitleElement;
    };
    interface HTMLIonCardTitleElement extends Components.IonCardTitle, HTMLStencilElement {
    }
    var HTMLIonCardTitleElement: {
        prototype: HTMLIonCardTitleElement;
        new (): HTMLIonCardTitleElement;
    };
    interface HTMLIonCheckboxElementEventMap {
        "ionChange": CheckboxChangeEventDetail;
        "ionFocus": void;
        "ionBlur": void;
        "ionStyle": StyleEventDetail;
    }
    interface HTMLIonCheckboxElement extends Components.IonCheckbox, HTMLStencilElement {
        addEventListener<K extends keyof HTMLIonCheckboxElementEventMap>(type: K, listener: (this: HTMLIonCheckboxElement, ev: IonCheckboxCustomEvent<HTMLIonCheckboxElementEventMap[K]>) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener<K extends keyof DocumentEventMap>(type: K, listener: (this: Document, ev: DocumentEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener<K extends keyof HTMLElementEventMap>(type: K, listener: (this: HTMLElement, ev: HTMLElementEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | AddEventListenerOptions): void;
        removeEventListener<K extends keyof HTMLIonCheckboxElementEventMap>(type: K, listener: (this: HTMLIonCheckboxElement, ev: IonCheckboxCustomEvent<HTMLIonCheckboxElementEventMap[K]>) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener<K extends keyof DocumentEventMap>(type: K, listener: (this: Document, ev: DocumentEventMap[K]) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener<K extends keyof HTMLElementEventMap>(type: K, listener: (this: HTMLElement, ev: HTMLElementEventMap[K]) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | EventListenerOptions): void;
    }
    var HTMLIonCheckboxElement: {
        prototype: HTMLIonCheckboxElement;
        new (): HTMLIonCheckboxElement;
    };
    interface HTMLIonChipElement extends Components.IonChip, HTMLStencilElement {
    }
    var HTMLIonChipElement: {
        prototype: HTMLIonChipElement;
        new (): HTMLIonChipElement;
    };
    interface HTMLIonColElement extends Components.IonCol, HTMLStencilElement {
    }
    var HTMLIonColElement: {
        prototype: HTMLIonColElement;
        new (): HTMLIonColElement;
    };
    interface HTMLIonContentElementEventMap {
        "ionScrollStart": ScrollBaseDetail;
        "ionScroll": ScrollDetail;
        "ionScrollEnd": ScrollBaseDetail;
    }
    interface HTMLIonContentElement extends Components.IonContent, HTMLStencilElement {
        addEventListener<K extends keyof HTMLIonContentElementEventMap>(type: K, listener: (this: HTMLIonContentElement, ev: IonContentCustomEvent<HTMLIonContentElementEventMap[K]>) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener<K extends keyof DocumentEventMap>(type: K, listener: (this: Document, ev: DocumentEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener<K extends keyof HTMLElementEventMap>(type: K, listener: (this: HTMLElement, ev: HTMLElementEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | AddEventListenerOptions): void;
        removeEventListener<K extends keyof HTMLIonContentElementEventMap>(type: K, listener: (this: HTMLIonContentElement, ev: IonContentCustomEvent<HTMLIonContentElementEventMap[K]>) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener<K extends keyof DocumentEventMap>(type: K, listener: (this: Document, ev: DocumentEventMap[K]) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener<K extends keyof HTMLElementEventMap>(type: K, listener: (this: HTMLElement, ev: HTMLElementEventMap[K]) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | EventListenerOptions): void;
    }
    var HTMLIonContentElement: {
        prototype: HTMLIonContentElement;
        new (): HTMLIonContentElement;
    };
    interface HTMLIonDatetimeElementEventMap {
        "ionCancel": void;
        "ionChange": DatetimeChangeEventDetail;
        "ionValueChange": DatetimeChangeEventDetail;
        "ionFocus": void;
        "ionBlur": void;
        "ionStyle": StyleEventDetail;
        "ionRender": void;
    }
    interface HTMLIonDatetimeElement extends Components.IonDatetime, HTMLStencilElement {
        addEventListener<K extends keyof HTMLIonDatetimeElementEventMap>(type: K, listener: (this: HTMLIonDatetimeElement, ev: IonDatetimeCustomEvent<HTMLIonDatetimeElementEventMap[K]>) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener<K extends keyof DocumentEventMap>(type: K, listener: (this: Document, ev: DocumentEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener<K extends keyof HTMLElementEventMap>(type: K, listener: (this: HTMLElement, ev: HTMLElementEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | AddEventListenerOptions): void;
        removeEventListener<K extends keyof HTMLIonDatetimeElementEventMap>(type: K, listener: (this: HTMLIonDatetimeElement, ev: IonDatetimeCustomEvent<HTMLIonDatetimeElementEventMap[K]>) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener<K extends keyof DocumentEventMap>(type: K, listener: (this: Document, ev: DocumentEventMap[K]) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener<K extends keyof HTMLElementEventMap>(type: K, listener: (this: HTMLElement, ev: HTMLElementEventMap[K]) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | EventListenerOptions): void;
    }
    var HTMLIonDatetimeElement: {
        prototype: HTMLIonDatetimeElement;
        new (): HTMLIonDatetimeElement;
    };
    interface HTMLIonDatetimeButtonElement extends Components.IonDatetimeButton, HTMLStencilElement {
    }
    var HTMLIonDatetimeButtonElement: {
        prototype: HTMLIonDatetimeButtonElement;
        new (): HTMLIonDatetimeButtonElement;
    };
    interface HTMLIonFabElement extends Components.IonFab, HTMLStencilElement {
    }
    var HTMLIonFabElement: {
        prototype: HTMLIonFabElement;
        new (): HTMLIonFabElement;
    };
    interface HTMLIonFabButtonElementEventMap {
        "ionFocus": void;
        "ionBlur": void;
    }
    interface HTMLIonFabButtonElement extends Components.IonFabButton, HTMLStencilElement {
        addEventListener<K extends keyof HTMLIonFabButtonElementEventMap>(type: K, listener: (this: HTMLIonFabButtonElement, ev: IonFabButtonCustomEvent<HTMLIonFabButtonElementEventMap[K]>) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener<K extends keyof DocumentEventMap>(type: K, listener: (this: Document, ev: DocumentEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener<K extends keyof HTMLElementEventMap>(type: K, listener: (this: HTMLElement, ev: HTMLElementEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | AddEventListenerOptions): void;
        removeEventListener<K extends keyof HTMLIonFabButtonElementEventMap>(type: K, listener: (this: HTMLIonFabButtonElement, ev: IonFabButtonCustomEvent<HTMLIonFabButtonElementEventMap[K]>) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener<K extends keyof DocumentEventMap>(type: K, listener: (this: Document, ev: DocumentEventMap[K]) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener<K extends keyof HTMLElementEventMap>(type: K, listener: (this: HTMLElement, ev: HTMLElementEventMap[K]) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | EventListenerOptions): void;
    }
    var HTMLIonFabButtonElement: {
        prototype: HTMLIonFabButtonElement;
        new (): HTMLIonFabButtonElement;
    };
    interface HTMLIonFabListElement extends Components.IonFabList, HTMLStencilElement {
    }
    var HTMLIonFabListElement: {
        prototype: HTMLIonFabListElement;
        new (): HTMLIonFabListElement;
    };
    interface HTMLIonFooterElement extends Components.IonFooter, HTMLStencilElement {
    }
    var HTMLIonFooterElement: {
        prototype: HTMLIonFooterElement;
        new (): HTMLIonFooterElement;
    };
    interface HTMLIonGridElement extends Components.IonGrid, HTMLStencilElement {
    }
    var HTMLIonGridElement: {
        prototype: HTMLIonGridElement;
        new (): HTMLIonGridElement;
    };
    interface HTMLIonHeaderElement extends Components.IonHeader, HTMLStencilElement {
    }
    var HTMLIonHeaderElement: {
        prototype: HTMLIonHeaderElement;
        new (): HTMLIonHeaderElement;
    };
    interface HTMLIonImgElementEventMap {
        "ionImgWillLoad": void;
        "ionImgDidLoad": void;
        "ionError": void;
    }
    interface HTMLIonImgElement extends Components.IonImg, HTMLStencilElement {
        addEventListener<K extends keyof HTMLIonImgElementEventMap>(type: K, listener: (this: HTMLIonImgElement, ev: IonImgCustomEvent<HTMLIonImgElementEventMap[K]>) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener<K extends keyof DocumentEventMap>(type: K, listener: (this: Document, ev: DocumentEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener<K extends keyof HTMLElementEventMap>(type: K, listener: (this: HTMLElement, ev: HTMLElementEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | AddEventListenerOptions): void;
        removeEventListener<K extends keyof HTMLIonImgElementEventMap>(type: K, listener: (this: HTMLIonImgElement, ev: IonImgCustomEvent<HTMLIonImgElementEventMap[K]>) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener<K extends keyof DocumentEventMap>(type: K, listener: (this: Document, ev: DocumentEventMap[K]) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener<K extends keyof HTMLElementEventMap>(type: K, listener: (this: HTMLElement, ev: HTMLElementEventMap[K]) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | EventListenerOptions): void;
    }
    var HTMLIonImgElement: {
        prototype: HTMLIonImgElement;
        new (): HTMLIonImgElement;
    };
    interface HTMLIonInfiniteScrollElementEventMap {
        "ionInfinite": void;
    }
    interface HTMLIonInfiniteScrollElement extends Components.IonInfiniteScroll, HTMLStencilElement {
        addEventListener<K extends keyof HTMLIonInfiniteScrollElementEventMap>(type: K, listener: (this: HTMLIonInfiniteScrollElement, ev: IonInfiniteScrollCustomEvent<HTMLIonInfiniteScrollElementEventMap[K]>) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener<K extends keyof DocumentEventMap>(type: K, listener: (this: Document, ev: DocumentEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener<K extends keyof HTMLElementEventMap>(type: K, listener: (this: HTMLElement, ev: HTMLElementEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | AddEventListenerOptions): void;
        removeEventListener<K extends keyof HTMLIonInfiniteScrollElementEventMap>(type: K, listener: (this: HTMLIonInfiniteScrollElement, ev: IonInfiniteScrollCustomEvent<HTMLIonInfiniteScrollElementEventMap[K]>) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener<K extends keyof DocumentEventMap>(type: K, listener: (this: Document, ev: DocumentEventMap[K]) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener<K extends keyof HTMLElementEventMap>(type: K, listener: (this: HTMLElement, ev: HTMLElementEventMap[K]) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | EventListenerOptions): void;
    }
    var HTMLIonInfiniteScrollElement: {
        prototype: HTMLIonInfiniteScrollElement;
        new (): HTMLIonInfiniteScrollElement;
    };
    interface HTMLIonInfiniteScrollContentElement extends Components.IonInfiniteScrollContent, HTMLStencilElement {
    }
    var HTMLIonInfiniteScrollContentElement: {
        prototype: HTMLIonInfiniteScrollContentElement;
        new (): HTMLIonInfiniteScrollContentElement;
    };
    interface HTMLIonInputElementEventMap {
        "ionInput": InputInputEventDetail;
        "ionChange": InputChangeEventDetail;
        "ionBlur": FocusEvent;
        "ionFocus": FocusEvent;
        "ionStyle": StyleEventDetail;
    }
    interface HTMLIonInputElement extends Components.IonInput, HTMLStencilElement {
        addEventListener<K extends keyof HTMLIonInputElementEventMap>(type: K, listener: (this: HTMLIonInputElement, ev: IonInputCustomEvent<HTMLIonInputElementEventMap[K]>) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener<K extends keyof DocumentEventMap>(type: K, listener: (this: Document, ev: DocumentEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener<K extends keyof HTMLElementEventMap>(type: K, listener: (this: HTMLElement, ev: HTMLElementEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | AddEventListenerOptions): void;
        removeEventListener<K extends keyof HTMLIonInputElementEventMap>(type: K, listener: (this: HTMLIonInputElement, ev: IonInputCustomEvent<HTMLIonInputElementEventMap[K]>) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener<K extends keyof DocumentEventMap>(type: K, listener: (this: Document, ev: DocumentEventMap[K]) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener<K extends keyof HTMLElementEventMap>(type: K, listener: (this: HTMLElement, ev: HTMLElementEventMap[K]) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | EventListenerOptions): void;
    }
    var HTMLIonInputElement: {
        prototype: HTMLIonInputElement;
        new (): HTMLIonInputElement;
    };
    interface HTMLIonItemElement extends Components.IonItem, HTMLStencilElement {
    }
    var HTMLIonItemElement: {
        prototype: HTMLIonItemElement;
        new (): HTMLIonItemElement;
    };
    interface HTMLIonItemDividerElement extends Components.IonItemDivider, HTMLStencilElement {
    }
    var HTMLIonItemDividerElement: {
        prototype: HTMLIonItemDividerElement;
        new (): HTMLIonItemDividerElement;
    };
    interface HTMLIonItemGroupElement extends Components.IonItemGroup, HTMLStencilElement {
    }
    var HTMLIonItemGroupElement: {
        prototype: HTMLIonItemGroupElement;
        new (): HTMLIonItemGroupElement;
    };
    interface HTMLIonItemOptionElement extends Components.IonItemOption, HTMLStencilElement {
    }
    var HTMLIonItemOptionElement: {
        prototype: HTMLIonItemOptionElement;
        new (): HTMLIonItemOptionElement;
    };
    interface HTMLIonItemOptionsElementEventMap {
        "ionSwipe": any;
    }
    interface HTMLIonItemOptionsElement extends Components.IonItemOptions, HTMLStencilElement {
        addEventListener<K extends keyof HTMLIonItemOptionsElementEventMap>(type: K, listener: (this: HTMLIonItemOptionsElement, ev: IonItemOptionsCustomEvent<HTMLIonItemOptionsElementEventMap[K]>) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener<K extends keyof DocumentEventMap>(type: K, listener: (this: Document, ev: DocumentEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener<K extends keyof HTMLElementEventMap>(type: K, listener: (this: HTMLElement, ev: HTMLElementEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | AddEventListenerOptions): void;
        removeEventListener<K extends keyof HTMLIonItemOptionsElementEventMap>(type: K, listener: (this: HTMLIonItemOptionsElement, ev: IonItemOptionsCustomEvent<HTMLIonItemOptionsElementEventMap[K]>) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener<K extends keyof DocumentEventMap>(type: K, listener: (this: Document, ev: DocumentEventMap[K]) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener<K extends keyof HTMLElementEventMap>(type: K, listener: (this: HTMLElement, ev: HTMLElementEventMap[K]) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | EventListenerOptions): void;
    }
    var HTMLIonItemOptionsElement: {
        prototype: HTMLIonItemOptionsElement;
        new (): HTMLIonItemOptionsElement;
    };
    interface HTMLIonItemSlidingElementEventMap {
        "ionDrag": any;
    }
    interface HTMLIonItemSlidingElement extends Components.IonItemSliding, HTMLStencilElement {
        addEventListener<K extends keyof HTMLIonItemSlidingElementEventMap>(type: K, listener: (this: HTMLIonItemSlidingElement, ev: IonItemSlidingCustomEvent<HTMLIonItemSlidingElementEventMap[K]>) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener<K extends keyof DocumentEventMap>(type: K, listener: (this: Document, ev: DocumentEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener<K extends keyof HTMLElementEventMap>(type: K, listener: (this: HTMLElement, ev: HTMLElementEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | AddEventListenerOptions): void;
        removeEventListener<K extends keyof HTMLIonItemSlidingElementEventMap>(type: K, listener: (this: HTMLIonItemSlidingElement, ev: IonItemSlidingCustomEvent<HTMLIonItemSlidingElementEventMap[K]>) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener<K extends keyof DocumentEventMap>(type: K, listener: (this: Document, ev: DocumentEventMap[K]) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener<K extends keyof HTMLElementEventMap>(type: K, listener: (this: HTMLElement, ev: HTMLElementEventMap[K]) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | EventListenerOptions): void;
    }
    var HTMLIonItemSlidingElement: {
        prototype: HTMLIonItemSlidingElement;
        new (): HTMLIonItemSlidingElement;
    };
    interface HTMLIonLabelElementEventMap {
        "ionColor": StyleEventDetail;
        "ionStyle": StyleEventDetail;
    }
    interface HTMLIonLabelElement extends Components.IonLabel, HTMLStencilElement {
        addEventListener<K extends keyof HTMLIonLabelElementEventMap>(type: K, listener: (this: HTMLIonLabelElement, ev: IonLabelCustomEvent<HTMLIonLabelElementEventMap[K]>) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener<K extends keyof DocumentEventMap>(type: K, listener: (this: Document, ev: DocumentEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener<K extends keyof HTMLElementEventMap>(type: K, listener: (this: HTMLElement, ev: HTMLElementEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | AddEventListenerOptions): void;
        removeEventListener<K extends keyof HTMLIonLabelElementEventMap>(type: K, listener: (this: HTMLIonLabelElement, ev: IonLabelCustomEvent<HTMLIonLabelElementEventMap[K]>) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener<K extends keyof DocumentEventMap>(type: K, listener: (this: Document, ev: DocumentEventMap[K]) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener<K extends keyof HTMLElementEventMap>(type: K, listener: (this: HTMLElement, ev: HTMLElementEventMap[K]) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | EventListenerOptions): void;
    }
    var HTMLIonLabelElement: {
        prototype: HTMLIonLabelElement;
        new (): HTMLIonLabelElement;
    };
    interface HTMLIonListElement extends Components.IonList, HTMLStencilElement {
    }
    var HTMLIonListElement: {
        prototype: HTMLIonListElement;
        new (): HTMLIonListElement;
    };
    interface HTMLIonListHeaderElement extends Components.IonListHeader, HTMLStencilElement {
    }
    var HTMLIonListHeaderElement: {
        prototype: HTMLIonListHeaderElement;
        new (): HTMLIonListHeaderElement;
    };
    interface HTMLIonLoadingElementEventMap {
        "ionLoadingDidPresent": void;
        "ionLoadingWillPresent": void;
        "ionLoadingWillDismiss": OverlayEventDetail;
        "ionLoadingDidDismiss": OverlayEventDetail;
        "didPresent": void;
        "willPresent": void;
        "willDismiss": OverlayEventDetail;
        "didDismiss": OverlayEventDetail;
    }
    interface HTMLIonLoadingElement extends Components.IonLoading, HTMLStencilElement {
        addEventListener<K extends keyof HTMLIonLoadingElementEventMap>(type: K, listener: (this: HTMLIonLoadingElement, ev: IonLoadingCustomEvent<HTMLIonLoadingElementEventMap[K]>) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener<K extends keyof DocumentEventMap>(type: K, listener: (this: Document, ev: DocumentEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener<K extends keyof HTMLElementEventMap>(type: K, listener: (this: HTMLElement, ev: HTMLElementEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | AddEventListenerOptions): void;
        removeEventListener<K extends keyof HTMLIonLoadingElementEventMap>(type: K, listener: (this: HTMLIonLoadingElement, ev: IonLoadingCustomEvent<HTMLIonLoadingElementEventMap[K]>) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener<K extends keyof DocumentEventMap>(type: K, listener: (this: Document, ev: DocumentEventMap[K]) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener<K extends keyof HTMLElementEventMap>(type: K, listener: (this: HTMLElement, ev: HTMLElementEventMap[K]) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | EventListenerOptions): void;
    }
    var HTMLIonLoadingElement: {
        prototype: HTMLIonLoadingElement;
        new (): HTMLIonLoadingElement;
    };
    interface HTMLIonMenuElementEventMap {
        "ionWillOpen": void;
        "ionWillClose": void;
        "ionDidOpen": void;
        "ionDidClose": void;
        "ionMenuChange": MenuChangeEventDetail;
    }
    interface HTMLIonMenuElement extends Components.IonMenu, HTMLStencilElement {
        addEventListener<K extends keyof HTMLIonMenuElementEventMap>(type: K, listener: (this: HTMLIonMenuElement, ev: IonMenuCustomEvent<HTMLIonMenuElementEventMap[K]>) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener<K extends keyof DocumentEventMap>(type: K, listener: (this: Document, ev: DocumentEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener<K extends keyof HTMLElementEventMap>(type: K, listener: (this: HTMLElement, ev: HTMLElementEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | AddEventListenerOptions): void;
        removeEventListener<K extends keyof HTMLIonMenuElementEventMap>(type: K, listener: (this: HTMLIonMenuElement, ev: IonMenuCustomEvent<HTMLIonMenuElementEventMap[K]>) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener<K extends keyof DocumentEventMap>(type: K, listener: (this: Document, ev: DocumentEventMap[K]) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener<K extends keyof HTMLElementEventMap>(type: K, listener: (this: HTMLElement, ev: HTMLElementEventMap[K]) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | EventListenerOptions): void;
    }
    var HTMLIonMenuElement: {
        prototype: HTMLIonMenuElement;
        new (): HTMLIonMenuElement;
    };
    interface HTMLIonMenuButtonElement extends Components.IonMenuButton, HTMLStencilElement {
    }
    var HTMLIonMenuButtonElement: {
        prototype: HTMLIonMenuButtonElement;
        new (): HTMLIonMenuButtonElement;
    };
    interface HTMLIonMenuToggleElement extends Components.IonMenuToggle, HTMLStencilElement {
    }
    var HTMLIonMenuToggleElement: {
        prototype: HTMLIonMenuToggleElement;
        new (): HTMLIonMenuToggleElement;
    };
    interface HTMLIonModalElementEventMap {
        "ionModalDidPresent": void;
        "ionModalWillPresent": void;
        "ionModalWillDismiss": OverlayEventDetail;
        "ionModalDidDismiss": OverlayEventDetail;
        "ionBreakpointDidChange": ModalBreakpointChangeEventDetail;
        "didPresent": void;
        "willPresent": void;
        "willDismiss": OverlayEventDetail;
        "didDismiss": OverlayEventDetail;
        "ionMount": void;
    }
    interface HTMLIonModalElement extends Components.IonModal, HTMLStencilElement {
        addEventListener<K extends keyof HTMLIonModalElementEventMap>(type: K, listener: (this: HTMLIonModalElement, ev: IonModalCustomEvent<HTMLIonModalElementEventMap[K]>) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener<K extends keyof DocumentEventMap>(type: K, listener: (this: Document, ev: DocumentEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener<K extends keyof HTMLElementEventMap>(type: K, listener: (this: HTMLElement, ev: HTMLElementEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | AddEventListenerOptions): void;
        removeEventListener<K extends keyof HTMLIonModalElementEventMap>(type: K, listener: (this: HTMLIonModalElement, ev: IonModalCustomEvent<HTMLIonModalElementEventMap[K]>) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener<K extends keyof DocumentEventMap>(type: K, listener: (this: Document, ev: DocumentEventMap[K]) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener<K extends keyof HTMLElementEventMap>(type: K, listener: (this: HTMLElement, ev: HTMLElementEventMap[K]) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | EventListenerOptions): void;
    }
    var HTMLIonModalElement: {
        prototype: HTMLIonModalElement;
        new (): HTMLIonModalElement;
    };
    interface HTMLIonNavElementEventMap {
        "ionNavWillLoad": void;
        "ionNavWillChange": void;
        "ionNavDidChange": void;
    }
    interface HTMLIonNavElement extends Components.IonNav, HTMLStencilElement {
        addEventListener<K extends keyof HTMLIonNavElementEventMap>(type: K, listener: (this: HTMLIonNavElement, ev: IonNavCustomEvent<HTMLIonNavElementEventMap[K]>) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener<K extends keyof DocumentEventMap>(type: K, listener: (this: Document, ev: DocumentEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener<K extends keyof HTMLElementEventMap>(type: K, listener: (this: HTMLElement, ev: HTMLElementEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | AddEventListenerOptions): void;
        removeEventListener<K extends keyof HTMLIonNavElementEventMap>(type: K, listener: (this: HTMLIonNavElement, ev: IonNavCustomEvent<HTMLIonNavElementEventMap[K]>) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener<K extends keyof DocumentEventMap>(type: K, listener: (this: Document, ev: DocumentEventMap[K]) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener<K extends keyof HTMLElementEventMap>(type: K, listener: (this: HTMLElement, ev: HTMLElementEventMap[K]) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | EventListenerOptions): void;
    }
    var HTMLIonNavElement: {
        prototype: HTMLIonNavElement;
        new (): HTMLIonNavElement;
    };
    interface HTMLIonNavLinkElement extends Components.IonNavLink, HTMLStencilElement {
    }
    var HTMLIonNavLinkElement: {
        prototype: HTMLIonNavLinkElement;
        new (): HTMLIonNavLinkElement;
    };
    interface HTMLIonNoteElement extends Components.IonNote, HTMLStencilElement {
    }
    var HTMLIonNoteElement: {
        prototype: HTMLIonNoteElement;
        new (): HTMLIonNoteElement;
    };
    interface HTMLIonPickerElementEventMap {
        "ionPickerDidPresent": void;
        "ionPickerWillPresent": void;
        "ionPickerWillDismiss": OverlayEventDetail;
        "ionPickerDidDismiss": OverlayEventDetail;
        "didPresent": void;
        "willPresent": void;
        "willDismiss": OverlayEventDetail;
        "didDismiss": OverlayEventDetail;
    }
    interface HTMLIonPickerElement extends Components.IonPicker, HTMLStencilElement {
        addEventListener<K extends keyof HTMLIonPickerElementEventMap>(type: K, listener: (this: HTMLIonPickerElement, ev: IonPickerCustomEvent<HTMLIonPickerElementEventMap[K]>) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener<K extends keyof DocumentEventMap>(type: K, listener: (this: Document, ev: DocumentEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener<K extends keyof HTMLElementEventMap>(type: K, listener: (this: HTMLElement, ev: HTMLElementEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | AddEventListenerOptions): void;
        removeEventListener<K extends keyof HTMLIonPickerElementEventMap>(type: K, listener: (this: HTMLIonPickerElement, ev: IonPickerCustomEvent<HTMLIonPickerElementEventMap[K]>) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener<K extends keyof DocumentEventMap>(type: K, listener: (this: Document, ev: DocumentEventMap[K]) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener<K extends keyof HTMLElementEventMap>(type: K, listener: (this: HTMLElement, ev: HTMLElementEventMap[K]) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | EventListenerOptions): void;
    }
    var HTMLIonPickerElement: {
        prototype: HTMLIonPickerElement;
        new (): HTMLIonPickerElement;
    };
    interface HTMLIonPickerColumnElementEventMap {
        "ionPickerColChange": PickerColumn;
    }
    interface HTMLIonPickerColumnElement extends Components.IonPickerColumn, HTMLStencilElement {
        addEventListener<K extends keyof HTMLIonPickerColumnElementEventMap>(type: K, listener: (this: HTMLIonPickerColumnElement, ev: IonPickerColumnCustomEvent<HTMLIonPickerColumnElementEventMap[K]>) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener<K extends keyof DocumentEventMap>(type: K, listener: (this: Document, ev: DocumentEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener<K extends keyof HTMLElementEventMap>(type: K, listener: (this: HTMLElement, ev: HTMLElementEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | AddEventListenerOptions): void;
        removeEventListener<K extends keyof HTMLIonPickerColumnElementEventMap>(type: K, listener: (this: HTMLIonPickerColumnElement, ev: IonPickerColumnCustomEvent<HTMLIonPickerColumnElementEventMap[K]>) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener<K extends keyof DocumentEventMap>(type: K, listener: (this: Document, ev: DocumentEventMap[K]) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener<K extends keyof HTMLElementEventMap>(type: K, listener: (this: HTMLElement, ev: HTMLElementEventMap[K]) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | EventListenerOptions): void;
    }
    var HTMLIonPickerColumnElement: {
        prototype: HTMLIonPickerColumnElement;
        new (): HTMLIonPickerColumnElement;
    };
    interface HTMLIonPickerColumnInternalElementEventMap {
        "ionChange": PickerColumnItem;
    }
    interface HTMLIonPickerColumnInternalElement extends Components.IonPickerColumnInternal, HTMLStencilElement {
        addEventListener<K extends keyof HTMLIonPickerColumnInternalElementEventMap>(type: K, listener: (this: HTMLIonPickerColumnInternalElement, ev: IonPickerColumnInternalCustomEvent<HTMLIonPickerColumnInternalElementEventMap[K]>) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener<K extends keyof DocumentEventMap>(type: K, listener: (this: Document, ev: DocumentEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener<K extends keyof HTMLElementEventMap>(type: K, listener: (this: HTMLElement, ev: HTMLElementEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | AddEventListenerOptions): void;
        removeEventListener<K extends keyof HTMLIonPickerColumnInternalElementEventMap>(type: K, listener: (this: HTMLIonPickerColumnInternalElement, ev: IonPickerColumnInternalCustomEvent<HTMLIonPickerColumnInternalElementEventMap[K]>) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener<K extends keyof DocumentEventMap>(type: K, listener: (this: Document, ev: DocumentEventMap[K]) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener<K extends keyof HTMLElementEventMap>(type: K, listener: (this: HTMLElement, ev: HTMLElementEventMap[K]) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | EventListenerOptions): void;
    }
    var HTMLIonPickerColumnInternalElement: {
        prototype: HTMLIonPickerColumnInternalElement;
        new (): HTMLIonPickerColumnInternalElement;
    };
    interface HTMLIonPickerInternalElementEventMap {
        "ionInputModeChange": PickerInternalChangeEventDetail;
    }
    interface HTMLIonPickerInternalElement extends Components.IonPickerInternal, HTMLStencilElement {
        addEventListener<K extends keyof HTMLIonPickerInternalElementEventMap>(type: K, listener: (this: HTMLIonPickerInternalElement, ev: IonPickerInternalCustomEvent<HTMLIonPickerInternalElementEventMap[K]>) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener<K extends keyof DocumentEventMap>(type: K, listener: (this: Document, ev: DocumentEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener<K extends keyof HTMLElementEventMap>(type: K, listener: (this: HTMLElement, ev: HTMLElementEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | AddEventListenerOptions): void;
        removeEventListener<K extends keyof HTMLIonPickerInternalElementEventMap>(type: K, listener: (this: HTMLIonPickerInternalElement, ev: IonPickerInternalCustomEvent<HTMLIonPickerInternalElementEventMap[K]>) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener<K extends keyof DocumentEventMap>(type: K, listener: (this: Document, ev: DocumentEventMap[K]) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener<K extends keyof HTMLElementEventMap>(type: K, listener: (this: HTMLElement, ev: HTMLElementEventMap[K]) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | EventListenerOptions): void;
    }
    var HTMLIonPickerInternalElement: {
        prototype: HTMLIonPickerInternalElement;
        new (): HTMLIonPickerInternalElement;
    };
    interface HTMLIonPopoverElementEventMap {
        "ionPopoverDidPresent": void;
        "ionPopoverWillPresent": void;
        "ionPopoverWillDismiss": OverlayEventDetail;
        "ionPopoverDidDismiss": OverlayEventDetail;
        "didPresent": void;
        "willPresent": void;
        "willDismiss": OverlayEventDetail;
        "didDismiss": OverlayEventDetail;
        "ionMount": void;
    }
    interface HTMLIonPopoverElement extends Components.IonPopover, HTMLStencilElement {
        addEventListener<K extends keyof HTMLIonPopoverElementEventMap>(type: K, listener: (this: HTMLIonPopoverElement, ev: IonPopoverCustomEvent<HTMLIonPopoverElementEventMap[K]>) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener<K extends keyof DocumentEventMap>(type: K, listener: (this: Document, ev: DocumentEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener<K extends keyof HTMLElementEventMap>(type: K, listener: (this: HTMLElement, ev: HTMLElementEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | AddEventListenerOptions): void;
        removeEventListener<K extends keyof HTMLIonPopoverElementEventMap>(type: K, listener: (this: HTMLIonPopoverElement, ev: IonPopoverCustomEvent<HTMLIonPopoverElementEventMap[K]>) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener<K extends keyof DocumentEventMap>(type: K, listener: (this: Document, ev: DocumentEventMap[K]) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener<K extends keyof HTMLElementEventMap>(type: K, listener: (this: HTMLElement, ev: HTMLElementEventMap[K]) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | EventListenerOptions): void;
    }
    var HTMLIonPopoverElement: {
        prototype: HTMLIonPopoverElement;
        new (): HTMLIonPopoverElement;
    };
    interface HTMLIonProgressBarElement extends Components.IonProgressBar, HTMLStencilElement {
    }
    var HTMLIonProgressBarElement: {
        prototype: HTMLIonProgressBarElement;
        new (): HTMLIonProgressBarElement;
    };
    interface HTMLIonRadioElementEventMap {
        "ionStyle": StyleEventDetail;
        "ionFocus": void;
        "ionBlur": void;
    }
    interface HTMLIonRadioElement extends Components.IonRadio, HTMLStencilElement {
        addEventListener<K extends keyof HTMLIonRadioElementEventMap>(type: K, listener: (this: HTMLIonRadioElement, ev: IonRadioCustomEvent<HTMLIonRadioElementEventMap[K]>) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener<K extends keyof DocumentEventMap>(type: K, listener: (this: Document, ev: DocumentEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener<K extends keyof HTMLElementEventMap>(type: K, listener: (this: HTMLElement, ev: HTMLElementEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | AddEventListenerOptions): void;
        removeEventListener<K extends keyof HTMLIonRadioElementEventMap>(type: K, listener: (this: HTMLIonRadioElement, ev: IonRadioCustomEvent<HTMLIonRadioElementEventMap[K]>) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener<K extends keyof DocumentEventMap>(type: K, listener: (this: Document, ev: DocumentEventMap[K]) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener<K extends keyof HTMLElementEventMap>(type: K, listener: (this: HTMLElement, ev: HTMLElementEventMap[K]) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | EventListenerOptions): void;
    }
    var HTMLIonRadioElement: {
        prototype: HTMLIonRadioElement;
        new (): HTMLIonRadioElement;
    };
    interface HTMLIonRadioGroupElementEventMap {
        "ionChange": RadioGroupChangeEventDetail;
        "ionValueChange": RadioGroupChangeEventDetail;
    }
    interface HTMLIonRadioGroupElement extends Components.IonRadioGroup, HTMLStencilElement {
        addEventListener<K extends keyof HTMLIonRadioGroupElementEventMap>(type: K, listener: (this: HTMLIonRadioGroupElement, ev: IonRadioGroupCustomEvent<HTMLIonRadioGroupElementEventMap[K]>) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener<K extends keyof DocumentEventMap>(type: K, listener: (this: Document, ev: DocumentEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener<K extends keyof HTMLElementEventMap>(type: K, listener: (this: HTMLElement, ev: HTMLElementEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | AddEventListenerOptions): void;
        removeEventListener<K extends keyof HTMLIonRadioGroupElementEventMap>(type: K, listener: (this: HTMLIonRadioGroupElement, ev: IonRadioGroupCustomEvent<HTMLIonRadioGroupElementEventMap[K]>) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener<K extends keyof DocumentEventMap>(type: K, listener: (this: Document, ev: DocumentEventMap[K]) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener<K extends keyof HTMLElementEventMap>(type: K, listener: (this: HTMLElement, ev: HTMLElementEventMap[K]) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | EventListenerOptions): void;
    }
    var HTMLIonRadioGroupElement: {
        prototype: HTMLIonRadioGroupElement;
        new (): HTMLIonRadioGroupElement;
    };
    interface HTMLIonRangeElementEventMap {
        "ionChange": RangeChangeEventDetail;
        "ionInput": RangeChangeEventDetail;
        "ionStyle": StyleEventDetail;
        "ionFocus": void;
        "ionBlur": void;
        "ionKnobMoveStart": RangeKnobMoveStartEventDetail;
        "ionKnobMoveEnd": RangeKnobMoveEndEventDetail;
    }
    interface HTMLIonRangeElement extends Components.IonRange, HTMLStencilElement {
        addEventListener<K extends keyof HTMLIonRangeElementEventMap>(type: K, listener: (this: HTMLIonRangeElement, ev: IonRangeCustomEvent<HTMLIonRangeElementEventMap[K]>) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener<K extends keyof DocumentEventMap>(type: K, listener: (this: Document, ev: DocumentEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener<K extends keyof HTMLElementEventMap>(type: K, listener: (this: HTMLElement, ev: HTMLElementEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | AddEventListenerOptions): void;
        removeEventListener<K extends keyof HTMLIonRangeElementEventMap>(type: K, listener: (this: HTMLIonRangeElement, ev: IonRangeCustomEvent<HTMLIonRangeElementEventMap[K]>) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener<K extends keyof DocumentEventMap>(type: K, listener: (this: Document, ev: DocumentEventMap[K]) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener<K extends keyof HTMLElementEventMap>(type: K, listener: (this: HTMLElement, ev: HTMLElementEventMap[K]) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | EventListenerOptions): void;
    }
    var HTMLIonRangeElement: {
        prototype: HTMLIonRangeElement;
        new (): HTMLIonRangeElement;
    };
    interface HTMLIonRefresherElementEventMap {
        "ionRefresh": RefresherEventDetail;
        "ionPull": void;
        "ionStart": void;
    }
    interface HTMLIonRefresherElement extends Components.IonRefresher, HTMLStencilElement {
        addEventListener<K extends keyof HTMLIonRefresherElementEventMap>(type: K, listener: (this: HTMLIonRefresherElement, ev: IonRefresherCustomEvent<HTMLIonRefresherElementEventMap[K]>) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener<K extends keyof DocumentEventMap>(type: K, listener: (this: Document, ev: DocumentEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener<K extends keyof HTMLElementEventMap>(type: K, listener: (this: HTMLElement, ev: HTMLElementEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | AddEventListenerOptions): void;
        removeEventListener<K extends keyof HTMLIonRefresherElementEventMap>(type: K, listener: (this: HTMLIonRefresherElement, ev: IonRefresherCustomEvent<HTMLIonRefresherElementEventMap[K]>) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener<K extends keyof DocumentEventMap>(type: K, listener: (this: Document, ev: DocumentEventMap[K]) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener<K extends keyof HTMLElementEventMap>(type: K, listener: (this: HTMLElement, ev: HTMLElementEventMap[K]) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | EventListenerOptions): void;
    }
    var HTMLIonRefresherElement: {
        prototype: HTMLIonRefresherElement;
        new (): HTMLIonRefresherElement;
    };
    interface HTMLIonRefresherContentElement extends Components.IonRefresherContent, HTMLStencilElement {
    }
    var HTMLIonRefresherContentElement: {
        prototype: HTMLIonRefresherContentElement;
        new (): HTMLIonRefresherContentElement;
    };
    interface HTMLIonReorderElement extends Components.IonReorder, HTMLStencilElement {
    }
    var HTMLIonReorderElement: {
        prototype: HTMLIonReorderElement;
        new (): HTMLIonReorderElement;
    };
    interface HTMLIonReorderGroupElementEventMap {
        "ionItemReorder": ItemReorderEventDetail;
    }
    interface HTMLIonReorderGroupElement extends Components.IonReorderGroup, HTMLStencilElement {
        addEventListener<K extends keyof HTMLIonReorderGroupElementEventMap>(type: K, listener: (this: HTMLIonReorderGroupElement, ev: IonReorderGroupCustomEvent<HTMLIonReorderGroupElementEventMap[K]>) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener<K extends keyof DocumentEventMap>(type: K, listener: (this: Document, ev: DocumentEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener<K extends keyof HTMLElementEventMap>(type: K, listener: (this: HTMLElement, ev: HTMLElementEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | AddEventListenerOptions): void;
        removeEventListener<K extends keyof HTMLIonReorderGroupElementEventMap>(type: K, listener: (this: HTMLIonReorderGroupElement, ev: IonReorderGroupCustomEvent<HTMLIonReorderGroupElementEventMap[K]>) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener<K extends keyof DocumentEventMap>(type: K, listener: (this: Document, ev: DocumentEventMap[K]) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener<K extends keyof HTMLElementEventMap>(type: K, listener: (this: HTMLElement, ev: HTMLElementEventMap[K]) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | EventListenerOptions): void;
    }
    var HTMLIonReorderGroupElement: {
        prototype: HTMLIonReorderGroupElement;
        new (): HTMLIonReorderGroupElement;
    };
    interface HTMLIonRippleEffectElement extends Components.IonRippleEffect, HTMLStencilElement {
    }
    var HTMLIonRippleEffectElement: {
        prototype: HTMLIonRippleEffectElement;
        new (): HTMLIonRippleEffectElement;
    };
    interface HTMLIonRouteElementEventMap {
        "ionRouteDataChanged": any;
    }
    interface HTMLIonRouteElement extends Components.IonRoute, HTMLStencilElement {
        addEventListener<K extends keyof HTMLIonRouteElementEventMap>(type: K, listener: (this: HTMLIonRouteElement, ev: IonRouteCustomEvent<HTMLIonRouteElementEventMap[K]>) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener<K extends keyof DocumentEventMap>(type: K, listener: (this: Document, ev: DocumentEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener<K extends keyof HTMLElementEventMap>(type: K, listener: (this: HTMLElement, ev: HTMLElementEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | AddEventListenerOptions): void;
        removeEventListener<K extends keyof HTMLIonRouteElementEventMap>(type: K, listener: (this: HTMLIonRouteElement, ev: IonRouteCustomEvent<HTMLIonRouteElementEventMap[K]>) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener<K extends keyof DocumentEventMap>(type: K, listener: (this: Document, ev: DocumentEventMap[K]) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener<K extends keyof HTMLElementEventMap>(type: K, listener: (this: HTMLElement, ev: HTMLElementEventMap[K]) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | EventListenerOptions): void;
    }
    var HTMLIonRouteElement: {
        prototype: HTMLIonRouteElement;
        new (): HTMLIonRouteElement;
    };
    interface HTMLIonRouteRedirectElementEventMap {
        "ionRouteRedirectChanged": any;
    }
    interface HTMLIonRouteRedirectElement extends Components.IonRouteRedirect, HTMLStencilElement {
        addEventListener<K extends keyof HTMLIonRouteRedirectElementEventMap>(type: K, listener: (this: HTMLIonRouteRedirectElement, ev: IonRouteRedirectCustomEvent<HTMLIonRouteRedirectElementEventMap[K]>) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener<K extends keyof DocumentEventMap>(type: K, listener: (this: Document, ev: DocumentEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener<K extends keyof HTMLElementEventMap>(type: K, listener: (this: HTMLElement, ev: HTMLElementEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | AddEventListenerOptions): void;
        removeEventListener<K extends keyof HTMLIonRouteRedirectElementEventMap>(type: K, listener: (this: HTMLIonRouteRedirectElement, ev: IonRouteRedirectCustomEvent<HTMLIonRouteRedirectElementEventMap[K]>) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener<K extends keyof DocumentEventMap>(type: K, listener: (this: Document, ev: DocumentEventMap[K]) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener<K extends keyof HTMLElementEventMap>(type: K, listener: (this: HTMLElement, ev: HTMLElementEventMap[K]) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | EventListenerOptions): void;
    }
    var HTMLIonRouteRedirectElement: {
        prototype: HTMLIonRouteRedirectElement;
        new (): HTMLIonRouteRedirectElement;
    };
    interface HTMLIonRouterElementEventMap {
        "ionRouteWillChange": RouterEventDetail;
        "ionRouteDidChange": RouterEventDetail;
    }
    interface HTMLIonRouterElement extends Components.IonRouter, HTMLStencilElement {
        addEventListener<K extends keyof HTMLIonRouterElementEventMap>(type: K, listener: (this: HTMLIonRouterElement, ev: IonRouterCustomEvent<HTMLIonRouterElementEventMap[K]>) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener<K extends keyof DocumentEventMap>(type: K, listener: (this: Document, ev: DocumentEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener<K extends keyof HTMLElementEventMap>(type: K, listener: (this: HTMLElement, ev: HTMLElementEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | AddEventListenerOptions): void;
        removeEventListener<K extends keyof HTMLIonRouterElementEventMap>(type: K, listener: (this: HTMLIonRouterElement, ev: IonRouterCustomEvent<HTMLIonRouterElementEventMap[K]>) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener<K extends keyof DocumentEventMap>(type: K, listener: (this: Document, ev: DocumentEventMap[K]) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener<K extends keyof HTMLElementEventMap>(type: K, listener: (this: HTMLElement, ev: HTMLElementEventMap[K]) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | EventListenerOptions): void;
    }
    var HTMLIonRouterElement: {
        prototype: HTMLIonRouterElement;
        new (): HTMLIonRouterElement;
    };
    interface HTMLIonRouterLinkElement extends Components.IonRouterLink, HTMLStencilElement {
    }
    var HTMLIonRouterLinkElement: {
        prototype: HTMLIonRouterLinkElement;
        new (): HTMLIonRouterLinkElement;
    };
    interface HTMLIonRouterOutletElementEventMap {
        "ionNavWillLoad": void;
        "ionNavWillChange": void;
        "ionNavDidChange": void;
    }
    interface HTMLIonRouterOutletElement extends Components.IonRouterOutlet, HTMLStencilElement {
        addEventListener<K extends keyof HTMLIonRouterOutletElementEventMap>(type: K, listener: (this: HTMLIonRouterOutletElement, ev: IonRouterOutletCustomEvent<HTMLIonRouterOutletElementEventMap[K]>) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener<K extends keyof DocumentEventMap>(type: K, listener: (this: Document, ev: DocumentEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener<K extends keyof HTMLElementEventMap>(type: K, listener: (this: HTMLElement, ev: HTMLElementEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | AddEventListenerOptions): void;
        removeEventListener<K extends keyof HTMLIonRouterOutletElementEventMap>(type: K, listener: (this: HTMLIonRouterOutletElement, ev: IonRouterOutletCustomEvent<HTMLIonRouterOutletElementEventMap[K]>) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener<K extends keyof DocumentEventMap>(type: K, listener: (this: Document, ev: DocumentEventMap[K]) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener<K extends keyof HTMLElementEventMap>(type: K, listener: (this: HTMLElement, ev: HTMLElementEventMap[K]) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | EventListenerOptions): void;
    }
    var HTMLIonRouterOutletElement: {
        prototype: HTMLIonRouterOutletElement;
        new (): HTMLIonRouterOutletElement;
    };
    interface HTMLIonRowElement extends Components.IonRow, HTMLStencilElement {
    }
    var HTMLIonRowElement: {
        prototype: HTMLIonRowElement;
        new (): HTMLIonRowElement;
    };
    interface HTMLIonSearchbarElementEventMap {
        "ionInput": SearchbarInputEventDetail;
        "ionChange": SearchbarChangeEventDetail;
        "ionCancel": void;
        "ionClear": void;
        "ionBlur": void;
        "ionFocus": void;
        "ionStyle": StyleEventDetail;
    }
    interface HTMLIonSearchbarElement extends Components.IonSearchbar, HTMLStencilElement {
        addEventListener<K extends keyof HTMLIonSearchbarElementEventMap>(type: K, listener: (this: HTMLIonSearchbarElement, ev: IonSearchbarCustomEvent<HTMLIonSearchbarElementEventMap[K]>) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener<K extends keyof DocumentEventMap>(type: K, listener: (this: Document, ev: DocumentEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener<K extends keyof HTMLElementEventMap>(type: K, listener: (this: HTMLElement, ev: HTMLElementEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | AddEventListenerOptions): void;
        removeEventListener<K extends keyof HTMLIonSearchbarElementEventMap>(type: K, listener: (this: HTMLIonSearchbarElement, ev: IonSearchbarCustomEvent<HTMLIonSearchbarElementEventMap[K]>) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener<K extends keyof DocumentEventMap>(type: K, listener: (this: Document, ev: DocumentEventMap[K]) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener<K extends keyof HTMLElementEventMap>(type: K, listener: (this: HTMLElement, ev: HTMLElementEventMap[K]) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | EventListenerOptions): void;
    }
    var HTMLIonSearchbarElement: {
        prototype: HTMLIonSearchbarElement;
        new (): HTMLIonSearchbarElement;
    };
    interface HTMLIonSegmentElementEventMap {
        "ionChange": SegmentChangeEventDetail;
        "ionSelect": SegmentChangeEventDetail;
        "ionStyle": StyleEventDetail;
    }
    interface HTMLIonSegmentElement extends Components.IonSegment, HTMLStencilElement {
        addEventListener<K extends keyof HTMLIonSegmentElementEventMap>(type: K, listener: (this: HTMLIonSegmentElement, ev: IonSegmentCustomEvent<HTMLIonSegmentElementEventMap[K]>) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener<K extends keyof DocumentEventMap>(type: K, listener: (this: Document, ev: DocumentEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener<K extends keyof HTMLElementEventMap>(type: K, listener: (this: HTMLElement, ev: HTMLElementEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | AddEventListenerOptions): void;
        removeEventListener<K extends keyof HTMLIonSegmentElementEventMap>(type: K, listener: (this: HTMLIonSegmentElement, ev: IonSegmentCustomEvent<HTMLIonSegmentElementEventMap[K]>) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener<K extends keyof DocumentEventMap>(type: K, listener: (this: Document, ev: DocumentEventMap[K]) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener<K extends keyof HTMLElementEventMap>(type: K, listener: (this: HTMLElement, ev: HTMLElementEventMap[K]) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | EventListenerOptions): void;
    }
    var HTMLIonSegmentElement: {
        prototype: HTMLIonSegmentElement;
        new (): HTMLIonSegmentElement;
    };
    interface HTMLIonSegmentButtonElement extends Components.IonSegmentButton, HTMLStencilElement {
    }
    var HTMLIonSegmentButtonElement: {
        prototype: HTMLIonSegmentButtonElement;
        new (): HTMLIonSegmentButtonElement;
    };
    interface HTMLIonSelectElementEventMap {
        "ionChange": SelectChangeEventDetail;
        "ionCancel": void;
        "ionDismiss": void;
        "ionFocus": void;
        "ionBlur": void;
        "ionStyle": StyleEventDetail;
    }
    interface HTMLIonSelectElement extends Components.IonSelect, HTMLStencilElement {
        addEventListener<K extends keyof HTMLIonSelectElementEventMap>(type: K, listener: (this: HTMLIonSelectElement, ev: IonSelectCustomEvent<HTMLIonSelectElementEventMap[K]>) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener<K extends keyof DocumentEventMap>(type: K, listener: (this: Document, ev: DocumentEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener<K extends keyof HTMLElementEventMap>(type: K, listener: (this: HTMLElement, ev: HTMLElementEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | AddEventListenerOptions): void;
        removeEventListener<K extends keyof HTMLIonSelectElementEventMap>(type: K, listener: (this: HTMLIonSelectElement, ev: IonSelectCustomEvent<HTMLIonSelectElementEventMap[K]>) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener<K extends keyof DocumentEventMap>(type: K, listener: (this: Document, ev: DocumentEventMap[K]) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener<K extends keyof HTMLElementEventMap>(type: K, listener: (this: HTMLElement, ev: HTMLElementEventMap[K]) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | EventListenerOptions): void;
    }
    var HTMLIonSelectElement: {
        prototype: HTMLIonSelectElement;
        new (): HTMLIonSelectElement;
    };
    interface HTMLIonSelectOptionElement extends Components.IonSelectOption, HTMLStencilElement {
    }
    var HTMLIonSelectOptionElement: {
        prototype: HTMLIonSelectOptionElement;
        new (): HTMLIonSelectOptionElement;
    };
    interface HTMLIonSelectPopoverElement extends Components.IonSelectPopover, HTMLStencilElement {
    }
    var HTMLIonSelectPopoverElement: {
        prototype: HTMLIonSelectPopoverElement;
        new (): HTMLIonSelectPopoverElement;
    };
    interface HTMLIonSkeletonTextElementEventMap {
        "ionStyle": StyleEventDetail;
    }
    interface HTMLIonSkeletonTextElement extends Components.IonSkeletonText, HTMLStencilElement {
        addEventListener<K extends keyof HTMLIonSkeletonTextElementEventMap>(type: K, listener: (this: HTMLIonSkeletonTextElement, ev: IonSkeletonTextCustomEvent<HTMLIonSkeletonTextElementEventMap[K]>) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener<K extends keyof DocumentEventMap>(type: K, listener: (this: Document, ev: DocumentEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener<K extends keyof HTMLElementEventMap>(type: K, listener: (this: HTMLElement, ev: HTMLElementEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | AddEventListenerOptions): void;
        removeEventListener<K extends keyof HTMLIonSkeletonTextElementEventMap>(type: K, listener: (this: HTMLIonSkeletonTextElement, ev: IonSkeletonTextCustomEvent<HTMLIonSkeletonTextElementEventMap[K]>) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener<K extends keyof DocumentEventMap>(type: K, listener: (this: Document, ev: DocumentEventMap[K]) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener<K extends keyof HTMLElementEventMap>(type: K, listener: (this: HTMLElement, ev: HTMLElementEventMap[K]) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | EventListenerOptions): void;
    }
    var HTMLIonSkeletonTextElement: {
        prototype: HTMLIonSkeletonTextElement;
        new (): HTMLIonSkeletonTextElement;
    };
    interface HTMLIonSpinnerElement extends Components.IonSpinner, HTMLStencilElement {
    }
    var HTMLIonSpinnerElement: {
        prototype: HTMLIonSpinnerElement;
        new (): HTMLIonSpinnerElement;
    };
    interface HTMLIonSplitPaneElementEventMap {
        "ionSplitPaneVisible": { visible: boolean };
    }
    interface HTMLIonSplitPaneElement extends Components.IonSplitPane, HTMLStencilElement {
        addEventListener<K extends keyof HTMLIonSplitPaneElementEventMap>(type: K, listener: (this: HTMLIonSplitPaneElement, ev: IonSplitPaneCustomEvent<HTMLIonSplitPaneElementEventMap[K]>) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener<K extends keyof DocumentEventMap>(type: K, listener: (this: Document, ev: DocumentEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener<K extends keyof HTMLElementEventMap>(type: K, listener: (this: HTMLElement, ev: HTMLElementEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | AddEventListenerOptions): void;
        removeEventListener<K extends keyof HTMLIonSplitPaneElementEventMap>(type: K, listener: (this: HTMLIonSplitPaneElement, ev: IonSplitPaneCustomEvent<HTMLIonSplitPaneElementEventMap[K]>) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener<K extends keyof DocumentEventMap>(type: K, listener: (this: Document, ev: DocumentEventMap[K]) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener<K extends keyof HTMLElementEventMap>(type: K, listener: (this: HTMLElement, ev: HTMLElementEventMap[K]) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | EventListenerOptions): void;
    }
    var HTMLIonSplitPaneElement: {
        prototype: HTMLIonSplitPaneElement;
        new (): HTMLIonSplitPaneElement;
    };
    interface HTMLIonTabElement extends Components.IonTab, HTMLStencilElement {
    }
    var HTMLIonTabElement: {
        prototype: HTMLIonTabElement;
        new (): HTMLIonTabElement;
    };
    interface HTMLIonTabBarElementEventMap {
        "ionTabBarChanged": TabBarChangedEventDetail;
        "ionTabBarLoaded": void;
    }
    interface HTMLIonTabBarElement extends Components.IonTabBar, HTMLStencilElement {
        addEventListener<K extends keyof HTMLIonTabBarElementEventMap>(type: K, listener: (this: HTMLIonTabBarElement, ev: IonTabBarCustomEvent<HTMLIonTabBarElementEventMap[K]>) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener<K extends keyof DocumentEventMap>(type: K, listener: (this: Document, ev: DocumentEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener<K extends keyof HTMLElementEventMap>(type: K, listener: (this: HTMLElement, ev: HTMLElementEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | AddEventListenerOptions): void;
        removeEventListener<K extends keyof HTMLIonTabBarElementEventMap>(type: K, listener: (this: HTMLIonTabBarElement, ev: IonTabBarCustomEvent<HTMLIonTabBarElementEventMap[K]>) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener<K extends keyof DocumentEventMap>(type: K, listener: (this: Document, ev: DocumentEventMap[K]) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener<K extends keyof HTMLElementEventMap>(type: K, listener: (this: HTMLElement, ev: HTMLElementEventMap[K]) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | EventListenerOptions): void;
    }
    var HTMLIonTabBarElement: {
        prototype: HTMLIonTabBarElement;
        new (): HTMLIonTabBarElement;
    };
    interface HTMLIonTabButtonElementEventMap {
        "ionTabButtonClick": TabButtonClickEventDetail;
    }
    interface HTMLIonTabButtonElement extends Components.IonTabButton, HTMLStencilElement {
        addEventListener<K extends keyof HTMLIonTabButtonElementEventMap>(type: K, listener: (this: HTMLIonTabButtonElement, ev: IonTabButtonCustomEvent<HTMLIonTabButtonElementEventMap[K]>) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener<K extends keyof DocumentEventMap>(type: K, listener: (this: Document, ev: DocumentEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener<K extends keyof HTMLElementEventMap>(type: K, listener: (this: HTMLElement, ev: HTMLElementEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | AddEventListenerOptions): void;
        removeEventListener<K extends keyof HTMLIonTabButtonElementEventMap>(type: K, listener: (this: HTMLIonTabButtonElement, ev: IonTabButtonCustomEvent<HTMLIonTabButtonElementEventMap[K]>) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener<K extends keyof DocumentEventMap>(type: K, listener: (this: Document, ev: DocumentEventMap[K]) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener<K extends keyof HTMLElementEventMap>(type: K, listener: (this: HTMLElement, ev: HTMLElementEventMap[K]) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | EventListenerOptions): void;
    }
    var HTMLIonTabButtonElement: {
        prototype: HTMLIonTabButtonElement;
        new (): HTMLIonTabButtonElement;
    };
    interface HTMLIonTabsElementEventMap {
        "ionNavWillLoad": void;
        "ionTabsWillChange": { tab: string };
        "ionTabsDidChange": { tab: string };
    }
    interface HTMLIonTabsElement extends Components.IonTabs, HTMLStencilElement {
        addEventListener<K extends keyof HTMLIonTabsElementEventMap>(type: K, listener: (this: HTMLIonTabsElement, ev: IonTabsCustomEvent<HTMLIonTabsElementEventMap[K]>) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener<K extends keyof DocumentEventMap>(type: K, listener: (this: Document, ev: DocumentEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener<K extends keyof HTMLElementEventMap>(type: K, listener: (this: HTMLElement, ev: HTMLElementEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | AddEventListenerOptions): void;
        removeEventListener<K extends keyof HTMLIonTabsElementEventMap>(type: K, listener: (this: HTMLIonTabsElement, ev: IonTabsCustomEvent<HTMLIonTabsElementEventMap[K]>) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener<K extends keyof DocumentEventMap>(type: K, listener: (this: Document, ev: DocumentEventMap[K]) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener<K extends keyof HTMLElementEventMap>(type: K, listener: (this: HTMLElement, ev: HTMLElementEventMap[K]) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | EventListenerOptions): void;
    }
    var HTMLIonTabsElement: {
        prototype: HTMLIonTabsElement;
        new (): HTMLIonTabsElement;
    };
    interface HTMLIonTextElement extends Components.IonText, HTMLStencilElement {
    }
    var HTMLIonTextElement: {
        prototype: HTMLIonTextElement;
        new (): HTMLIonTextElement;
    };
    interface HTMLIonTextareaElementEventMap {
        "ionChange": TextareaChangeEventDetail;
        "ionInput": TextareaInputEventDetail;
        "ionStyle": StyleEventDetail;
        "ionBlur": FocusEvent;
        "ionFocus": FocusEvent;
    }
    interface HTMLIonTextareaElement extends Components.IonTextarea, HTMLStencilElement {
        addEventListener<K extends keyof HTMLIonTextareaElementEventMap>(type: K, listener: (this: HTMLIonTextareaElement, ev: IonTextareaCustomEvent<HTMLIonTextareaElementEventMap[K]>) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener<K extends keyof DocumentEventMap>(type: K, listener: (this: Document, ev: DocumentEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener<K extends keyof HTMLElementEventMap>(type: K, listener: (this: HTMLElement, ev: HTMLElementEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | AddEventListenerOptions): void;
        removeEventListener<K extends keyof HTMLIonTextareaElementEventMap>(type: K, listener: (this: HTMLIonTextareaElement, ev: IonTextareaCustomEvent<HTMLIonTextareaElementEventMap[K]>) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener<K extends keyof DocumentEventMap>(type: K, listener: (this: Document, ev: DocumentEventMap[K]) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener<K extends keyof HTMLElementEventMap>(type: K, listener: (this: HTMLElement, ev: HTMLElementEventMap[K]) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | EventListenerOptions): void;
    }
    var HTMLIonTextareaElement: {
        prototype: HTMLIonTextareaElement;
        new (): HTMLIonTextareaElement;
    };
    interface HTMLIonThumbnailElement extends Components.IonThumbnail, HTMLStencilElement {
    }
    var HTMLIonThumbnailElement: {
        prototype: HTMLIonThumbnailElement;
        new (): HTMLIonThumbnailElement;
    };
    interface HTMLIonTitleElementEventMap {
        "ionStyle": StyleEventDetail;
    }
    interface HTMLIonTitleElement extends Components.IonTitle, HTMLStencilElement {
        addEventListener<K extends keyof HTMLIonTitleElementEventMap>(type: K, listener: (this: HTMLIonTitleElement, ev: IonTitleCustomEvent<HTMLIonTitleElementEventMap[K]>) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener<K extends keyof DocumentEventMap>(type: K, listener: (this: Document, ev: DocumentEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener<K extends keyof HTMLElementEventMap>(type: K, listener: (this: HTMLElement, ev: HTMLElementEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | AddEventListenerOptions): void;
        removeEventListener<K extends keyof HTMLIonTitleElementEventMap>(type: K, listener: (this: HTMLIonTitleElement, ev: IonTitleCustomEvent<HTMLIonTitleElementEventMap[K]>) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener<K extends keyof DocumentEventMap>(type: K, listener: (this: Document, ev: DocumentEventMap[K]) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener<K extends keyof HTMLElementEventMap>(type: K, listener: (this: HTMLElement, ev: HTMLElementEventMap[K]) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | EventListenerOptions): void;
    }
    var HTMLIonTitleElement: {
        prototype: HTMLIonTitleElement;
        new (): HTMLIonTitleElement;
    };
    interface HTMLIonToastElementEventMap {
        "ionToastDidPresent": void;
        "ionToastWillPresent": void;
        "ionToastWillDismiss": OverlayEventDetail;
        "ionToastDidDismiss": OverlayEventDetail;
        "didPresent": void;
        "willPresent": void;
        "willDismiss": OverlayEventDetail;
        "didDismiss": OverlayEventDetail;
    }
    interface HTMLIonToastElement extends Components.IonToast, HTMLStencilElement {
        addEventListener<K extends keyof HTMLIonToastElementEventMap>(type: K, listener: (this: HTMLIonToastElement, ev: IonToastCustomEvent<HTMLIonToastElementEventMap[K]>) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener<K extends keyof DocumentEventMap>(type: K, listener: (this: Document, ev: DocumentEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener<K extends keyof HTMLElementEventMap>(type: K, listener: (this: HTMLElement, ev: HTMLElementEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | AddEventListenerOptions): void;
        removeEventListener<K extends keyof HTMLIonToastElementEventMap>(type: K, listener: (this: HTMLIonToastElement, ev: IonToastCustomEvent<HTMLIonToastElementEventMap[K]>) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener<K extends keyof DocumentEventMap>(type: K, listener: (this: Document, ev: DocumentEventMap[K]) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener<K extends keyof HTMLElementEventMap>(type: K, listener: (this: HTMLElement, ev: HTMLElementEventMap[K]) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | EventListenerOptions): void;
    }
    var HTMLIonToastElement: {
        prototype: HTMLIonToastElement;
        new (): HTMLIonToastElement;
    };
    interface HTMLIonToggleElementEventMap {
        "ionChange": ToggleChangeEventDetail;
        "ionFocus": void;
        "ionBlur": void;
        "ionStyle": StyleEventDetail;
    }
    interface HTMLIonToggleElement extends Components.IonToggle, HTMLStencilElement {
        addEventListener<K extends keyof HTMLIonToggleElementEventMap>(type: K, listener: (this: HTMLIonToggleElement, ev: IonToggleCustomEvent<HTMLIonToggleElementEventMap[K]>) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener<K extends keyof DocumentEventMap>(type: K, listener: (this: Document, ev: DocumentEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener<K extends keyof HTMLElementEventMap>(type: K, listener: (this: HTMLElement, ev: HTMLElementEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | AddEventListenerOptions): void;
        removeEventListener<K extends keyof HTMLIonToggleElementEventMap>(type: K, listener: (this: HTMLIonToggleElement, ev: IonToggleCustomEvent<HTMLIonToggleElementEventMap[K]>) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener<K extends keyof DocumentEventMap>(type: K, listener: (this: Document, ev: DocumentEventMap[K]) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener<K extends keyof HTMLElementEventMap>(type: K, listener: (this: HTMLElement, ev: HTMLElementEventMap[K]) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | EventListenerOptions): void;
    }
    var HTMLIonToggleElement: {
        prototype: HTMLIonToggleElement;
        new (): HTMLIonToggleElement;
    };
    interface HTMLIonToolbarElement extends Components.IonToolbar, HTMLStencilElement {
    }
    var HTMLIonToolbarElement: {
        prototype: HTMLIonToolbarElement;
        new (): HTMLIonToolbarElement;
    };
    interface HTMLElementTagNameMap {
        "ion-accordion": HTMLIonAccordionElement;
        "ion-accordion-group": HTMLIonAccordionGroupElement;
        "ion-action-sheet": HTMLIonActionSheetElement;
        "ion-alert": HTMLIonAlertElement;
        "ion-app": HTMLIonAppElement;
        "ion-avatar": HTMLIonAvatarElement;
        "ion-back-button": HTMLIonBackButtonElement;
        "ion-backdrop": HTMLIonBackdropElement;
        "ion-badge": HTMLIonBadgeElement;
        "ion-breadcrumb": HTMLIonBreadcrumbElement;
        "ion-breadcrumbs": HTMLIonBreadcrumbsElement;
        "ion-button": HTMLIonButtonElement;
        "ion-buttons": HTMLIonButtonsElement;
        "ion-card": HTMLIonCardElement;
        "ion-card-content": HTMLIonCardContentElement;
        "ion-card-header": HTMLIonCardHeaderElement;
        "ion-card-subtitle": HTMLIonCardSubtitleElement;
        "ion-card-title": HTMLIonCardTitleElement;
        "ion-checkbox": HTMLIonCheckboxElement;
        "ion-chip": HTMLIonChipElement;
        "ion-col": HTMLIonColElement;
        "ion-content": HTMLIonContentElement;
        "ion-datetime": HTMLIonDatetimeElement;
        "ion-datetime-button": HTMLIonDatetimeButtonElement;
        "ion-fab": HTMLIonFabElement;
        "ion-fab-button": HTMLIonFabButtonElement;
        "ion-fab-list": HTMLIonFabListElement;
        "ion-footer": HTMLIonFooterElement;
        "ion-grid": HTMLIonGridElement;
        "ion-header": HTMLIonHeaderElement;
        "ion-img": HTMLIonImgElement;
        "ion-infinite-scroll": HTMLIonInfiniteScrollElement;
        "ion-infinite-scroll-content": HTMLIonInfiniteScrollContentElement;
        "ion-input": HTMLIonInputElement;
        "ion-item": HTMLIonItemElement;
        "ion-item-divider": HTMLIonItemDividerElement;
        "ion-item-group": HTMLIonItemGroupElement;
        "ion-item-option": HTMLIonItemOptionElement;
        "ion-item-options": HTMLIonItemOptionsElement;
        "ion-item-sliding": HTMLIonItemSlidingElement;
        "ion-label": HTMLIonLabelElement;
        "ion-list": HTMLIonListElement;
        "ion-list-header": HTMLIonListHeaderElement;
        "ion-loading": HTMLIonLoadingElement;
        "ion-menu": HTMLIonMenuElement;
        "ion-menu-button": HTMLIonMenuButtonElement;
        "ion-menu-toggle": HTMLIonMenuToggleElement;
        "ion-modal": HTMLIonModalElement;
        "ion-nav": HTMLIonNavElement;
        "ion-nav-link": HTMLIonNavLinkElement;
        "ion-note": HTMLIonNoteElement;
        "ion-picker": HTMLIonPickerElement;
        "ion-picker-column": HTMLIonPickerColumnElement;
        "ion-picker-column-internal": HTMLIonPickerColumnInternalElement;
        "ion-picker-internal": HTMLIonPickerInternalElement;
        "ion-popover": HTMLIonPopoverElement;
        "ion-progress-bar": HTMLIonProgressBarElement;
        "ion-radio": HTMLIonRadioElement;
        "ion-radio-group": HTMLIonRadioGroupElement;
        "ion-range": HTMLIonRangeElement;
        "ion-refresher": HTMLIonRefresherElement;
        "ion-refresher-content": HTMLIonRefresherContentElement;
        "ion-reorder": HTMLIonReorderElement;
        "ion-reorder-group": HTMLIonReorderGroupElement;
        "ion-ripple-effect": HTMLIonRippleEffectElement;
        "ion-route": HTMLIonRouteElement;
        "ion-route-redirect": HTMLIonRouteRedirectElement;
        "ion-router": HTMLIonRouterElement;
        "ion-router-link": HTMLIonRouterLinkElement;
        "ion-router-outlet": HTMLIonRouterOutletElement;
        "ion-row": HTMLIonRowElement;
        "ion-searchbar": HTMLIonSearchbarElement;
        "ion-segment": HTMLIonSegmentElement;
        "ion-segment-button": HTMLIonSegmentButtonElement;
        "ion-select": HTMLIonSelectElement;
        "ion-select-option": HTMLIonSelectOptionElement;
        "ion-select-popover": HTMLIonSelectPopoverElement;
        "ion-skeleton-text": HTMLIonSkeletonTextElement;
        "ion-spinner": HTMLIonSpinnerElement;
        "ion-split-pane": HTMLIonSplitPaneElement;
        "ion-tab": HTMLIonTabElement;
        "ion-tab-bar": HTMLIonTabBarElement;
        "ion-tab-button": HTMLIonTabButtonElement;
        "ion-tabs": HTMLIonTabsElement;
        "ion-text": HTMLIonTextElement;
        "ion-textarea": HTMLIonTextareaElement;
        "ion-thumbnail": HTMLIonThumbnailElement;
        "ion-title": HTMLIonTitleElement;
        "ion-toast": HTMLIonToastElement;
        "ion-toggle": HTMLIonToggleElement;
        "ion-toolbar": HTMLIonToolbarElement;
    }
}
declare namespace LocalJSX {
    interface IonAccordion {
        /**
          * If `true`, the accordion cannot be interacted with.
         */
        "disabled"?: boolean;
        /**
          * The mode determines which platform styles to use.
         */
        "mode"?: "ios" | "md";
        /**
          * If `true`, the accordion cannot be interacted with, but does not alter the opacity.
         */
        "readonly"?: boolean;
        /**
          * The toggle icon to use. This icon will be rotated when the accordion is expanded or collapsed.
         */
        "toggleIcon"?: string;
        /**
          * The slot inside of `ion-item` to place the toggle icon. Defaults to `"end"`.
         */
        "toggleIconSlot"?: 'start' | 'end';
        /**
          * The value of the accordion. Defaults to an autogenerated value.
         */
        "value"?: string;
    }
    interface IonAccordionGroup {
        /**
          * If `true`, all accordions inside of the accordion group will animate when expanding or collapsing.
         */
        "animated"?: boolean;
        /**
          * If `true`, the accordion group cannot be interacted with.
         */
        "disabled"?: boolean;
        /**
          * Describes the expansion behavior for each accordion. Possible values are `"compact"` and `"inset"`. Defaults to `"compact"`.
         */
        "expand"?: 'compact' | 'inset';
        /**
          * The mode determines which platform styles to use.
         */
        "mode"?: "ios" | "md";
        /**
          * If `true`, the accordion group can have multiple accordion components expanded at the same time.
         */
        "multiple"?: boolean;
        /**
          * Emitted when the value property has changed as a result of a user action such as a click. This event will not emit when programmatically setting the value property.
         */
        "onIonChange"?: (event: IonAccordionGroupCustomEvent<AccordionGroupChangeEventDetail>) => void;
        /**
          * If `true`, the accordion group cannot be interacted with, but does not alter the opacity.
         */
        "readonly"?: boolean;
        /**
          * The value of the accordion group. This controls which accordions are expanded. This should be an array of strings only when `multiple="true"`
         */
        "value"?: string | string[] | null;
    }
    interface IonActionSheet {
        /**
          * If `true`, the action sheet will animate.
         */
        "animated"?: boolean;
        /**
          * If `true`, the action sheet will be dismissed when the backdrop is clicked.
         */
        "backdropDismiss"?: boolean;
        /**
          * An array of buttons for the action sheet.
         */
        "buttons"?: (ActionSheetButton | string)[];
        /**
          * Additional classes to apply for custom CSS. If multiple classes are provided they should be separated by spaces.
         */
        "cssClass"?: string | string[];
        /**
          * Animation to use when the action sheet is presented.
         */
        "enterAnimation"?: AnimationBuilder;
        /**
          * Title for the action sheet.
         */
        "header"?: string;
        /**
          * Additional attributes to pass to the action sheet.
         */
        "htmlAttributes"?: { [key: string]: any };
        /**
          * If `true`, the action sheet will open. If `false`, the action sheet will close. Use this if you need finer grained control over presentation, otherwise just use the actionSheetController or the `trigger` property. Note: `isOpen` will not automatically be set back to `false` when the action sheet dismisses. You will need to do that in your code.
         */
        "isOpen"?: boolean;
        /**
          * If `true`, the keyboard will be automatically dismissed when the overlay is presented.
         */
        "keyboardClose"?: boolean;
        /**
          * Animation to use when the action sheet is dismissed.
         */
        "leaveAnimation"?: AnimationBuilder;
        /**
          * The mode determines which platform styles to use.
         */
        "mode"?: "ios" | "md";
        /**
          * Emitted after the action sheet has dismissed. Shorthand for ionActionSheetDidDismiss.
         */
        "onDidDismiss"?: (event: IonActionSheetCustomEvent<OverlayEventDetail>) => void;
        /**
          * Emitted after the action sheet has presented. Shorthand for ionActionSheetWillDismiss.
         */
        "onDidPresent"?: (event: IonActionSheetCustomEvent<void>) => void;
        /**
          * Emitted after the action sheet has dismissed.
         */
        "onIonActionSheetDidDismiss"?: (event: IonActionSheetCustomEvent<OverlayEventDetail>) => void;
        /**
          * Emitted after the action sheet has presented.
         */
        "onIonActionSheetDidPresent"?: (event: IonActionSheetCustomEvent<void>) => void;
        /**
          * Emitted before the action sheet has dismissed.
         */
        "onIonActionSheetWillDismiss"?: (event: IonActionSheetCustomEvent<OverlayEventDetail>) => void;
        /**
          * Emitted before the action sheet has presented.
         */
        "onIonActionSheetWillPresent"?: (event: IonActionSheetCustomEvent<void>) => void;
        /**
          * Emitted before the action sheet has dismissed. Shorthand for ionActionSheetWillDismiss.
         */
        "onWillDismiss"?: (event: IonActionSheetCustomEvent<OverlayEventDetail>) => void;
        /**
          * Emitted before the action sheet has presented. Shorthand for ionActionSheetWillPresent.
         */
        "onWillPresent"?: (event: IonActionSheetCustomEvent<void>) => void;
        /**
          * Subtitle for the action sheet.
         */
        "subHeader"?: string;
        /**
          * If `true`, the action sheet will be translucent. Only applies when the mode is `"ios"` and the device supports [`backdrop-filter`](https://developer.mozilla.org/en-US/docs/Web/CSS/backdrop-filter#Browser_compatibility).
         */
        "translucent"?: boolean;
        /**
          * An ID corresponding to the trigger element that causes the action sheet to open when clicked.
         */
        "trigger"?: string | undefined;
    }
    interface IonAlert {
        /**
          * If `true`, the alert will animate.
         */
        "animated"?: boolean;
        /**
          * If `true`, the alert will be dismissed when the backdrop is clicked.
         */
        "backdropDismiss"?: boolean;
        /**
          * Array of buttons to be added to the alert.
         */
        "buttons"?: (AlertButton | string)[];
        /**
          * Additional classes to apply for custom CSS. If multiple classes are provided they should be separated by spaces.
         */
        "cssClass"?: string | string[];
        /**
          * Animation to use when the alert is presented.
         */
        "enterAnimation"?: AnimationBuilder;
        /**
          * The main title in the heading of the alert.
         */
        "header"?: string;
        /**
          * Additional attributes to pass to the alert.
         */
        "htmlAttributes"?: { [key: string]: any };
        /**
          * Array of input to show in the alert.
         */
        "inputs"?: AlertInput[];
        /**
          * If `true`, the alert will open. If `false`, the alert will close. Use this if you need finer grained control over presentation, otherwise just use the alertController or the `trigger` property. Note: `isOpen` will not automatically be set back to `false` when the alert dismisses. You will need to do that in your code.
         */
        "isOpen"?: boolean;
        /**
          * If `true`, the keyboard will be automatically dismissed when the overlay is presented.
         */
        "keyboardClose"?: boolean;
        /**
          * Animation to use when the alert is dismissed.
         */
        "leaveAnimation"?: AnimationBuilder;
        /**
          * The main message to be displayed in the alert. `message` can accept either plaintext or HTML as a string. To display characters normally reserved for HTML, they must be escaped. For example `<Ionic>` would become `&lt;Ionic&gt;`  For more information: [Security Documentation](https://ionicframework.com/docs/faq/security)  This property accepts custom HTML as a string. Content is parsed as plaintext by default. `innerHTMLTemplatesEnabled` must be set to `true` in the Ionic config before custom HTML can be used.
         */
        "message"?: string | IonicSafeString;
        /**
          * The mode determines which platform styles to use.
         */
        "mode"?: "ios" | "md";
        /**
          * Emitted after the alert has dismissed. Shorthand for ionAlertDidDismiss.
         */
        "onDidDismiss"?: (event: IonAlertCustomEvent<OverlayEventDetail>) => void;
        /**
          * Emitted after the alert has presented. Shorthand for ionAlertWillDismiss.
         */
        "onDidPresent"?: (event: IonAlertCustomEvent<void>) => void;
        /**
          * Emitted after the alert has dismissed.
         */
        "onIonAlertDidDismiss"?: (event: IonAlertCustomEvent<OverlayEventDetail>) => void;
        /**
          * Emitted after the alert has presented.
         */
        "onIonAlertDidPresent"?: (event: IonAlertCustomEvent<void>) => void;
        /**
          * Emitted before the alert has dismissed.
         */
        "onIonAlertWillDismiss"?: (event: IonAlertCustomEvent<OverlayEventDetail>) => void;
        /**
          * Emitted before the alert has presented.
         */
        "onIonAlertWillPresent"?: (event: IonAlertCustomEvent<void>) => void;
        /**
          * Emitted before the alert has dismissed. Shorthand for ionAlertWillDismiss.
         */
        "onWillDismiss"?: (event: IonAlertCustomEvent<OverlayEventDetail>) => void;
        /**
          * Emitted before the alert has presented. Shorthand for ionAlertWillPresent.
         */
        "onWillPresent"?: (event: IonAlertCustomEvent<void>) => void;
        /**
          * The subtitle in the heading of the alert. Displayed under the title.
         */
        "subHeader"?: string;
        /**
          * If `true`, the alert will be translucent. Only applies when the mode is `"ios"` and the device supports [`backdrop-filter`](https://developer.mozilla.org/en-US/docs/Web/CSS/backdrop-filter#Browser_compatibility).
         */
        "translucent"?: boolean;
        /**
          * An ID corresponding to the trigger element that causes the alert to open when clicked.
         */
        "trigger"?: string | undefined;
    }
    interface IonApp {
    }
    interface IonAvatar {
    }
    interface IonBackButton {
        /**
          * The color to use from your application's color palette. Default options are: `"primary"`, `"secondary"`, `"tertiary"`, `"success"`, `"warning"`, `"danger"`, `"light"`, `"medium"`, and `"dark"`. For more information on colors, see [theming](/docs/theming/basics).
         */
        "color"?: Color;
        /**
          * The url to navigate back to by default when there is no history.
         */
        "defaultHref"?: string;
        /**
          * If `true`, the user cannot interact with the button.
         */
        "disabled"?: boolean;
        /**
          * The built-in named SVG icon name or the exact `src` of an SVG file to use for the back button.
         */
        "icon"?: string | null;
        /**
          * The mode determines which platform styles to use.
         */
        "mode"?: "ios" | "md";
        /**
          * When using a router, it specifies the transition animation when navigating to another page.
         */
        "routerAnimation"?: AnimationBuilder | undefined;
        /**
          * The text to display in the back button.
         */
        "text"?: string | null;
        /**
          * The type of the button.
         */
        "type"?: 'submit' | 'reset' | 'button';
    }
    interface IonBackdrop {
        /**
          * Emitted when the backdrop is tapped.
         */
        "onIonBackdropTap"?: (event: IonBackdropCustomEvent<void>) => void;
        /**
          * If `true`, the backdrop will stop propagation on tap.
         */
        "stopPropagation"?: boolean;
        /**
          * If `true`, the backdrop will can be clicked and will emit the `ionBackdropTap` event.
         */
        "tappable"?: boolean;
        /**
          * If `true`, the backdrop will be visible.
         */
        "visible"?: boolean;
    }
    interface IonBadge {
        /**
          * The color to use from your application's color palette. Default options are: `"primary"`, `"secondary"`, `"tertiary"`, `"success"`, `"warning"`, `"danger"`, `"light"`, `"medium"`, and `"dark"`. For more information on colors, see [theming](/docs/theming/basics).
         */
        "color"?: Color;
        /**
          * The mode determines which platform styles to use.
         */
        "mode"?: "ios" | "md";
    }
    interface IonBreadcrumb {
        /**
          * If `true`, the breadcrumb will take on a different look to show that it is the currently active breadcrumb. Defaults to `true` for the last breadcrumb if it is not set on any.
         */
        "active"?: boolean;
        /**
          * The color to use from your application's color palette. Default options are: `"primary"`, `"secondary"`, `"tertiary"`, `"success"`, `"warning"`, `"danger"`, `"light"`, `"medium"`, and `"dark"`. For more information on colors, see [theming](/docs/theming/basics).
         */
        "color"?: Color;
        /**
          * If `true`, the user cannot interact with the breadcrumb.
         */
        "disabled"?: boolean;
        /**
          * This attribute instructs browsers to download a URL instead of navigating to it, so the user will be prompted to save it as a local file. If the attribute has a value, it is used as the pre-filled file name in the Save prompt (the user can still change the file name if they want).
         */
        "download"?: string | undefined;
        /**
          * Contains a URL or a URL fragment that the hyperlink points to. If this property is set, an anchor tag will be rendered.
         */
        "href"?: string | undefined;
        /**
          * The mode determines which platform styles to use.
         */
        "mode"?: "ios" | "md";
        /**
          * Emitted when the breadcrumb loses focus.
         */
        "onIonBlur"?: (event: IonBreadcrumbCustomEvent<void>) => void;
        /**
          * Emitted when the breadcrumb has focus.
         */
        "onIonFocus"?: (event: IonBreadcrumbCustomEvent<void>) => void;
        /**
          * Specifies the relationship of the target object to the link object. The value is a space-separated list of [link types](https://developer.mozilla.org/en-US/docs/Web/HTML/Link_types).
         */
        "rel"?: string | undefined;
        /**
          * When using a router, it specifies the transition animation when navigating to another page using `href`.
         */
        "routerAnimation"?: AnimationBuilder | undefined;
        /**
          * When using a router, it specifies the transition direction when navigating to another page using `href`.
         */
        "routerDirection"?: RouterDirection;
        /**
          * If true, show a separator between this breadcrumb and the next. Defaults to `true` for all breadcrumbs except the last.
         */
        "separator"?: boolean | undefined;
        /**
          * Specifies where to display the linked URL. Only applies when an `href` is provided. Special keywords: `"_blank"`, `"_self"`, `"_parent"`, `"_top"`.
         */
        "target"?: string | undefined;
    }
    interface IonBreadcrumbs {
        /**
          * The color to use from your application's color palette. Default options are: `"primary"`, `"secondary"`, `"tertiary"`, `"success"`, `"warning"`, `"danger"`, `"light"`, `"medium"`, and `"dark"`. For more information on colors, see [theming](/docs/theming/basics).
         */
        "color"?: Color;
        /**
          * The number of breadcrumbs to show after the collapsed indicator. If `itemsBeforeCollapse` + `itemsAfterCollapse` is greater than `maxItems`, the breadcrumbs will not be collapsed.
         */
        "itemsAfterCollapse"?: number;
        /**
          * The number of breadcrumbs to show before the collapsed indicator. If `itemsBeforeCollapse` + `itemsAfterCollapse` is greater than `maxItems`, the breadcrumbs will not be collapsed.
         */
        "itemsBeforeCollapse"?: number;
        /**
          * The maximum number of breadcrumbs to show before collapsing.
         */
        "maxItems"?: number;
        /**
          * The mode determines which platform styles to use.
         */
        "mode"?: "ios" | "md";
        /**
          * Emitted when the collapsed indicator is clicked on.
         */
        "onIonCollapsedClick"?: (event: IonBreadcrumbsCustomEvent<BreadcrumbCollapsedClickEventDetail>) => void;
    }
    interface IonButton {
        /**
          * The type of button.
         */
        "buttonType"?: string;
        /**
          * The color to use from your application's color palette. Default options are: `"primary"`, `"secondary"`, `"tertiary"`, `"success"`, `"warning"`, `"danger"`, `"light"`, `"medium"`, and `"dark"`. For more information on colors, see [theming](/docs/theming/basics).
         */
        "color"?: Color;
        /**
          * If `true`, the user cannot interact with the button.
         */
        "disabled"?: boolean;
        /**
          * This attribute instructs browsers to download a URL instead of navigating to it, so the user will be prompted to save it as a local file. If the attribute has a value, it is used as the pre-filled file name in the Save prompt (the user can still change the file name if they want).
         */
        "download"?: string | undefined;
        /**
          * Set to `"block"` for a full-width button or to `"full"` for a full-width button with square corners and no left or right borders.
         */
        "expand"?: 'full' | 'block';
        /**
          * Set to `"clear"` for a transparent button that resembles a flat button, to `"outline"` for a transparent button with a border, or to `"solid"` for a button with a filled background. The default fill is `"solid"` except inside of a toolbar, where the default is `"clear"`.
         */
        "fill"?: 'clear' | 'outline' | 'solid' | 'default';
        /**
          * The HTML form element or form element id. Used to submit a form when the button is not a child of the form.
         */
        "form"?: string | HTMLFormElement;
        /**
          * Contains a URL or a URL fragment that the hyperlink points to. If this property is set, an anchor tag will be rendered.
         */
        "href"?: string | undefined;
        /**
          * The mode determines which platform styles to use.
         */
        "mode"?: "ios" | "md";
        /**
          * Emitted when the button loses focus.
         */
        "onIonBlur"?: (event: IonButtonCustomEvent<void>) => void;
        /**
          * Emitted when the button has focus.
         */
        "onIonFocus"?: (event: IonButtonCustomEvent<void>) => void;
        /**
          * Specifies the relationship of the target object to the link object. The value is a space-separated list of [link types](https://developer.mozilla.org/en-US/docs/Web/HTML/Link_types).
         */
        "rel"?: string | undefined;
        /**
          * When using a router, it specifies the transition animation when navigating to another page using `href`.
         */
        "routerAnimation"?: AnimationBuilder | undefined;
        /**
          * When using a router, it specifies the transition direction when navigating to another page using `href`.
         */
        "routerDirection"?: RouterDirection;
        /**
          * Set to `"round"` for a button with more rounded corners.
         */
        "shape"?: 'round';
        /**
          * Set to `"small"` for a button with less height and padding, to `"default"` for a button with the default height and padding, or to `"large"` for a button with more height and padding. By default the size is unset, unless the button is inside of an item, where the size is `"small"` by default. Set the size to `"default"` inside of an item to make it a standard size button.
         */
        "size"?: 'small' | 'default' | 'large';
        /**
          * If `true`, activates a button with a heavier font weight.
         */
        "strong"?: boolean;
        /**
          * Specifies where to display the linked URL. Only applies when an `href` is provided. Special keywords: `"_blank"`, `"_self"`, `"_parent"`, `"_top"`.
         */
        "target"?: string | undefined;
        /**
          * The type of the button.
         */
        "type"?: 'submit' | 'reset' | 'button';
    }
    interface IonButtons {
        /**
          * If true, buttons will disappear when its parent toolbar has fully collapsed if the toolbar is not the first toolbar. If the toolbar is the first toolbar, the buttons will be hidden and will only be shown once all toolbars have fully collapsed.  Only applies in `ios` mode with `collapse` set to `true` on `ion-header`.  Typically used for [Collapsible Large Titles](https://ionicframework.com/docs/api/title#collapsible-large-titles)
         */
        "collapse"?: boolean;
    }
    interface IonCard {
        /**
          * If `true`, a button tag will be rendered and the card will be tappable.
         */
        "button"?: boolean;
        /**
          * The color to use from your application's color palette. Default options are: `"primary"`, `"secondary"`, `"tertiary"`, `"success"`, `"warning"`, `"danger"`, `"light"`, `"medium"`, and `"dark"`. For more information on colors, see [theming](/docs/theming/basics).
         */
        "color"?: Color;
        /**
          * If `true`, the user cannot interact with the card.
         */
        "disabled"?: boolean;
        /**
          * This attribute instructs browsers to download a URL instead of navigating to it, so the user will be prompted to save it as a local file. If the attribute has a value, it is used as the pre-filled file name in the Save prompt (the user can still change the file name if they want).
         */
        "download"?: string | undefined;
        /**
          * Contains a URL or a URL fragment that the hyperlink points to. If this property is set, an anchor tag will be rendered.
         */
        "href"?: string | undefined;
        /**
          * The mode determines which platform styles to use.
         */
        "mode"?: "ios" | "md";
        /**
          * Specifies the relationship of the target object to the link object. The value is a space-separated list of [link types](https://developer.mozilla.org/en-US/docs/Web/HTML/Link_types).
         */
        "rel"?: string | undefined;
        /**
          * When using a router, it specifies the transition animation when navigating to another page using `href`.
         */
        "routerAnimation"?: AnimationBuilder | undefined;
        /**
          * When using a router, it specifies the transition direction when navigating to another page using `href`.
         */
        "routerDirection"?: RouterDirection;
        /**
          * Specifies where to display the linked URL. Only applies when an `href` is provided. Special keywords: `"_blank"`, `"_self"`, `"_parent"`, `"_top"`.
         */
        "target"?: string | undefined;
        /**
          * The type of the button. Only used when an `onclick` or `button` property is present.
         */
        "type"?: 'submit' | 'reset' | 'button';
    }
    interface IonCardContent {
        /**
          * The mode determines which platform styles to use.
         */
        "mode"?: "ios" | "md";
    }
    interface IonCardHeader {
        /**
          * The color to use from your application's color palette. Default options are: `"primary"`, `"secondary"`, `"tertiary"`, `"success"`, `"warning"`, `"danger"`, `"light"`, `"medium"`, and `"dark"`. For more information on colors, see [theming](/docs/theming/basics).
         */
        "color"?: Color;
        /**
          * The mode determines which platform styles to use.
         */
        "mode"?: "ios" | "md";
        /**
          * If `true`, the card header will be translucent. Only applies when the mode is `"ios"` and the device supports [`backdrop-filter`](https://developer.mozilla.org/en-US/docs/Web/CSS/backdrop-filter#Browser_compatibility).
         */
        "translucent"?: boolean;
    }
    interface IonCardSubtitle {
        /**
          * The color to use from your application's color palette. Default options are: `"primary"`, `"secondary"`, `"tertiary"`, `"success"`, `"warning"`, `"danger"`, `"light"`, `"medium"`, and `"dark"`. For more information on colors, see [theming](/docs/theming/basics).
         */
        "color"?: Color;
        /**
          * The mode determines which platform styles to use.
         */
        "mode"?: "ios" | "md";
    }
    interface IonCardTitle {
        /**
          * The color to use from your application's color palette. Default options are: `"primary"`, `"secondary"`, `"tertiary"`, `"success"`, `"warning"`, `"danger"`, `"light"`, `"medium"`, and `"dark"`. For more information on colors, see [theming](/docs/theming/basics).
         */
        "color"?: Color;
        /**
          * The mode determines which platform styles to use.
         */
        "mode"?: "ios" | "md";
    }
    interface IonCheckbox {
        /**
          * How to control the alignment of the checkbox and label on the cross axis. `"start"`: The label and control will appear on the left of the cross axis in LTR, and on the right side in RTL. `"center"`: The label and control will appear at the center of the cross axis in both LTR and RTL.
         */
        "alignment"?: 'start' | 'center';
        /**
          * If `true`, the checkbox is selected.
         */
        "checked"?: boolean;
        /**
          * The color to use from your application's color palette. Default options are: `"primary"`, `"secondary"`, `"tertiary"`, `"success"`, `"warning"`, `"danger"`, `"light"`, `"medium"`, and `"dark"`. For more information on colors, see [theming](/docs/theming/basics).
         */
        "color"?: Color;
        /**
          * If `true`, the user cannot interact with the checkbox.
         */
        "disabled"?: boolean;
        /**
          * If `true`, the checkbox will visually appear as indeterminate.
         */
        "indeterminate"?: boolean;
        /**
          * How to pack the label and checkbox within a line. `"start"`: The label and checkbox will appear on the left in LTR and on the right in RTL. `"end"`: The label and checkbox will appear on the right in LTR and on the left in RTL. `"space-between"`: The label and checkbox will appear on opposite ends of the line with space between the two elements.
         */
        "justify"?: 'start' | 'end' | 'space-between';
        /**
          * Where to place the label relative to the checkbox. `"start"`: The label will appear to the left of the checkbox in LTR and to the right in RTL. `"end"`: The label will appear to the right of the checkbox in LTR and to the left in RTL. `"fixed"`: The label has the same behavior as `"start"` except it also has a fixed width. Long text will be truncated with ellipses ("..."). `"stacked"`: The label will appear above the checkbox regardless of the direction. The alignment of the label can be controlled with the `alignment` property.
         */
        "labelPlacement"?: 'start' | 'end' | 'fixed' | 'stacked';
        /**
          * Set the `legacy` property to `true` to forcibly use the legacy form control markup. Ionic will only opt checkboxes in to the modern form markup when they are using either the `aria-label` attribute or have text in the default slot. As a result, the `legacy` property should only be used as an escape hatch when you want to avoid this automatic opt-in behavior.  Note that this property will be removed in an upcoming major release of Ionic, and all form components will be opted-in to using the modern form markup.
         */
        "legacy"?: boolean;
        /**
          * The mode determines which platform styles to use.
         */
        "mode"?: "ios" | "md";
        /**
          * The name of the control, which is submitted with the form data.
         */
        "name"?: string;
        /**
          * Emitted when the checkbox loses focus.
         */
        "onIonBlur"?: (event: IonCheckboxCustomEvent<void>) => void;
        /**
          * Emitted when the checked property has changed as a result of a user action such as a click. This event will not emit when programmatically setting the checked property.
         */
        "onIonChange"?: (event: IonCheckboxCustomEvent<CheckboxChangeEventDetail>) => void;
        /**
          * Emitted when the checkbox has focus.
         */
        "onIonFocus"?: (event: IonCheckboxCustomEvent<void>) => void;
        /**
          * The value of the checkbox does not mean if it's checked or not, use the `checked` property for that.  The value of a checkbox is analogous to the value of an `<input type="checkbox">`, it's only used when the checkbox participates in a native `<form>`.
         */
        "value"?: any | null;
    }
    interface IonChip {
        /**
          * The color to use from your application's color palette. Default options are: `"primary"`, `"secondary"`, `"tertiary"`, `"success"`, `"warning"`, `"danger"`, `"light"`, `"medium"`, and `"dark"`. For more information on colors, see [theming](/docs/theming/basics).
         */
        "color"?: Color;
        /**
          * If `true`, the user cannot interact with the chip.
         */
        "disabled"?: boolean;
        /**
          * The mode determines which platform styles to use.
         */
        "mode"?: "ios" | "md";
        /**
          * Display an outline style button.
         */
        "outline"?: boolean;
    }
    interface IonCol {
        /**
          * The amount to offset the column, in terms of how many columns it should shift to the end of the total available.
         */
        "offset"?: string;
        /**
          * The amount to offset the column for lg screens, in terms of how many columns it should shift to the end of the total available.
         */
        "offsetLg"?: string;
        /**
          * The amount to offset the column for md screens, in terms of how many columns it should shift to the end of the total available.
         */
        "offsetMd"?: string;
        /**
          * The amount to offset the column for sm screens, in terms of how many columns it should shift to the end of the total available.
         */
        "offsetSm"?: string;
        /**
          * The amount to offset the column for xl screens, in terms of how many columns it should shift to the end of the total available.
         */
        "offsetXl"?: string;
        /**
          * The amount to offset the column for xs screens, in terms of how many columns it should shift to the end of the total available.
         */
        "offsetXs"?: string;
        /**
          * The amount to pull the column, in terms of how many columns it should shift to the start of the total available.
         */
        "pull"?: string;
        /**
          * The amount to pull the column for lg screens, in terms of how many columns it should shift to the start of the total available.
         */
        "pullLg"?: string;
        /**
          * The amount to pull the column for md screens, in terms of how many columns it should shift to the start of the total available.
         */
        "pullMd"?: string;
        /**
          * The amount to pull the column for sm screens, in terms of how many columns it should shift to the start of the total available.
         */
        "pullSm"?: string;
        /**
          * The amount to pull the column for xl screens, in terms of how many columns it should shift to the start of the total available.
         */
        "pullXl"?: string;
        /**
          * The amount to pull the column for xs screens, in terms of how many columns it should shift to the start of the total available.
         */
        "pullXs"?: string;
        /**
          * The amount to push the column, in terms of how many columns it should shift to the end of the total available.
         */
        "push"?: string;
        /**
          * The amount to push the column for lg screens, in terms of how many columns it should shift to the end of the total available.
         */
        "pushLg"?: string;
        /**
          * The amount to push the column for md screens, in terms of how many columns it should shift to the end of the total available.
         */
        "pushMd"?: string;
        /**
          * The amount to push the column for sm screens, in terms of how many columns it should shift to the end of the total available.
         */
        "pushSm"?: string;
        /**
          * The amount to push the column for xl screens, in terms of how many columns it should shift to the end of the total available.
         */
        "pushXl"?: string;
        /**
          * The amount to push the column for xs screens, in terms of how many columns it should shift to the end of the total available.
         */
        "pushXs"?: string;
        /**
          * The size of the column, in terms of how many columns it should take up out of the total available. If `"auto"` is passed, the column will be the size of its content.
         */
        "size"?: string;
        /**
          * The size of the column for lg screens, in terms of how many columns it should take up out of the total available. If `"auto"` is passed, the column will be the size of its content.
         */
        "sizeLg"?: string;
        /**
          * The size of the column for md screens, in terms of how many columns it should take up out of the total available. If `"auto"` is passed, the column will be the size of its content.
         */
        "sizeMd"?: string;
        /**
          * The size of the column for sm screens, in terms of how many columns it should take up out of the total available. If `"auto"` is passed, the column will be the size of its content.
         */
        "sizeSm"?: string;
        /**
          * The size of the column for xl screens, in terms of how many columns it should take up out of the total available. If `"auto"` is passed, the column will be the size of its content.
         */
        "sizeXl"?: string;
        /**
          * The size of the column for xs screens, in terms of how many columns it should take up out of the total available. If `"auto"` is passed, the column will be the size of its content.
         */
        "sizeXs"?: string;
    }
    interface IonContent {
        /**
          * The color to use from your application's color palette. Default options are: `"primary"`, `"secondary"`, `"tertiary"`, `"success"`, `"warning"`, `"danger"`, `"light"`, `"medium"`, and `"dark"`. For more information on colors, see [theming](/docs/theming/basics).
         */
        "color"?: Color;
        /**
          * If `true` and the content does not cause an overflow scroll, the scroll interaction will cause a bounce. If the content exceeds the bounds of ionContent, nothing will change. Note, this does not disable the system bounce on iOS. That is an OS level setting.
         */
        "forceOverscroll"?: boolean;
        /**
          * If `true`, the content will scroll behind the headers and footers. This effect can easily be seen by setting the toolbar to transparent.
         */
        "fullscreen"?: boolean;
        /**
          * Emitted while scrolling. This event is disabled by default. Set `scrollEvents` to `true` to enable.
         */
        "onIonScroll"?: (event: IonContentCustomEvent<ScrollDetail>) => void;
        /**
          * Emitted when the scroll has ended. This event is disabled by default. Set `scrollEvents` to `true` to enable.
         */
        "onIonScrollEnd"?: (event: IonContentCustomEvent<ScrollBaseDetail>) => void;
        /**
          * Emitted when the scroll has started. This event is disabled by default. Set `scrollEvents` to `true` to enable.
         */
        "onIonScrollStart"?: (event: IonContentCustomEvent<ScrollBaseDetail>) => void;
        /**
          * Because of performance reasons, ionScroll events are disabled by default, in order to enable them and start listening from (ionScroll), set this property to `true`.
         */
        "scrollEvents"?: boolean;
        /**
          * If you want to enable the content scrolling in the X axis, set this property to `true`.
         */
        "scrollX"?: boolean;
        /**
          * If you want to disable the content scrolling in the Y axis, set this property to `false`.
         */
        "scrollY"?: boolean;
    }
    interface IonDatetime {
        /**
          * The text to display on the picker's cancel button.
         */
        "cancelText"?: string;
        /**
          * The text to display on the picker's "Clear" button.
         */
        "clearText"?: string;
        /**
          * The color to use from your application's color palette. Default options are: `"primary"`, `"secondary"`, `"tertiary"`, `"success"`, `"warning"`, `"danger"`, `"light"`, `"medium"`, and `"dark"`. For more information on colors, see [theming](/docs/theming/basics).
         */
        "color"?: Color;
        /**
          * Values used to create the list of selectable days. By default every day is shown for the given month. However, to control exactly which days of the month to display, the `dayValues` input can take a number, an array of numbers, or a string of comma separated numbers. Note that even if the array days have an invalid number for the selected month, like `31` in February, it will correctly not show days which are not valid for the selected month.
         */
        "dayValues"?: number[] | number | string;
        /**
          * If `true`, the user cannot interact with the datetime.
         */
        "disabled"?: boolean;
        /**
          * The text to display on the picker's "Done" button.
         */
        "doneText"?: string;
        /**
          * The first day of the week to use for `ion-datetime`. The default value is `0` and represents Sunday.
         */
        "firstDayOfWeek"?: number;
        /**
          * Formatting options for dates and times. Should include a 'date' and/or 'time' object, each of which is of type [Intl.DateTimeFormatOptions](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Intl/DateTimeFormat/DateTimeFormat#options).
         */
        "formatOptions"?: FormatOptions;
        /**
          * Used to apply custom text and background colors to specific dates.  Can be either an array of objects containing ISO strings and colors, or a callback that receives an ISO string and returns the colors.  Only applies to the `date`, `date-time`, and `time-date` presentations, with `preferWheel="false"`.
         */
        "highlightedDates"?: DatetimeHighlight[] | DatetimeHighlightCallback;
        /**
          * The hour cycle of the `ion-datetime`. If no value is set, this is specified by the current locale.
         */
        "hourCycle"?: DatetimeHourCycle;
        /**
          * Values used to create the list of selectable hours. By default the hour values range from `0` to `23` for 24-hour, or `1` to `12` for 12-hour. However, to control exactly which hours to display, the `hourValues` input can take a number, an array of numbers, or a string of comma separated numbers.
         */
        "hourValues"?: number[] | number | string;
        /**
          * Returns if an individual date (calendar day) is enabled or disabled.  If `true`, the day will be enabled/interactive. If `false`, the day will be disabled/non-interactive.  The function accepts an ISO 8601 date string of a given day. By default, all days are enabled. Developers can use this function to write custom logic to disable certain days.  The function is called for each rendered calendar day, for the previous, current and next month. Custom implementations should be optimized for performance to avoid jank.
         */
        "isDateEnabled"?: (dateIsoString: string) => boolean;
        /**
          * The locale to use for `ion-datetime`. This impacts month and day name formatting. The `"default"` value refers to the default locale set by your device.
         */
        "locale"?: string;
        /**
          * The maximum datetime allowed. Value must be a date string following the [ISO 8601 datetime format standard](https://www.w3.org/TR/NOTE-datetime), `1996-12-19`. The format does not have to be specific to an exact datetime. For example, the maximum could just be the year, such as `1994`. Defaults to the end of this year.
         */
        "max"?: string;
        /**
          * The minimum datetime allowed. Value must be a date string following the [ISO 8601 datetime format standard](https://www.w3.org/TR/NOTE-datetime), such as `1996-12-19`. The format does not have to be specific to an exact datetime. For example, the minimum could just be the year, such as `1994`. Defaults to the beginning of the year, 100 years ago from today.
         */
        "min"?: string;
        /**
          * Values used to create the list of selectable minutes. By default the minutes range from `0` to `59`. However, to control exactly which minutes to display, the `minuteValues` input can take a number, an array of numbers, or a string of comma separated numbers. For example, if the minute selections should only be every 15 minutes, then this input value would be `minuteValues="0,15,30,45"`.
         */
        "minuteValues"?: number[] | number | string;
        /**
          * The mode determines which platform styles to use.
         */
        "mode"?: "ios" | "md";
        /**
          * Values used to create the list of selectable months. By default the month values range from `1` to `12`. However, to control exactly which months to display, the `monthValues` input can take a number, an array of numbers, or a string of comma separated numbers. For example, if only summer months should be shown, then this input value would be `monthValues="6,7,8"`. Note that month numbers do *not* have a zero-based index, meaning January's value is `1`, and December's is `12`.
         */
        "monthValues"?: number[] | number | string;
        /**
          * If `true`, multiple dates can be selected at once. Only applies to `presentation="date"` and `preferWheel="false"`.
         */
        "multiple"?: boolean;
        /**
          * The name of the control, which is submitted with the form data.
         */
        "name"?: string;
        /**
          * Emitted when the datetime loses focus.
         */
        "onIonBlur"?: (event: IonDatetimeCustomEvent<void>) => void;
        /**
          * Emitted when the datetime selection was cancelled.
         */
        "onIonCancel"?: (event: IonDatetimeCustomEvent<void>) => void;
        /**
          * Emitted when the value (selected date) has changed.
         */
        "onIonChange"?: (event: IonDatetimeCustomEvent<DatetimeChangeEventDetail>) => void;
        /**
          * Emitted when the datetime has focus.
         */
        "onIonFocus"?: (event: IonDatetimeCustomEvent<void>) => void;
        /**
          * If `true`, a wheel picker will be rendered instead of a calendar grid where possible. If `false`, a calendar grid will be rendered instead of a wheel picker where possible.  A wheel picker can be rendered instead of a grid when `presentation` is one of the following values: `"date"`, `"date-time"`, or `"time-date"`.  A wheel picker will always be rendered regardless of the `preferWheel` value when `presentation` is one of the following values: `"time"`, `"month"`, `"month-year"`, or `"year"`.
         */
        "preferWheel"?: boolean;
        /**
          * Which values you want to select. `"date"` will show a calendar picker to select the month, day, and year. `"time"` will show a time picker to select the hour, minute, and (optionally) AM/PM. `"date-time"` will show the date picker first and time picker second. `"time-date"` will show the time picker first and date picker second.
         */
        "presentation"?: DatetimePresentation;
        /**
          * If `true`, the datetime appears normal but the selected date cannot be changed.
         */
        "readonly"?: boolean;
        /**
          * If `true`, a "Clear" button will be rendered alongside the default "Cancel" and "OK" buttons at the bottom of the `ion-datetime` component. Developers can also use the `button` slot if they want to customize these buttons. If custom buttons are set in the `button` slot then the default buttons will not be rendered.
         */
        "showClearButton"?: boolean;
        /**
          * If `true`, the default "Cancel" and "OK" buttons will be rendered at the bottom of the `ion-datetime` component. Developers can also use the `button` slot if they want to customize these buttons. If custom buttons are set in the `button` slot then the default buttons will not be rendered.
         */
        "showDefaultButtons"?: boolean;
        /**
          * If `true`, the default "Time" label will be rendered for the time selector of the `ion-datetime` component. Developers can also use the `time-label` slot if they want to customize this label. If a custom label is set in the `time-label` slot then the default label will not be rendered.
         */
        "showDefaultTimeLabel"?: boolean;
        /**
          * If `true`, a header will be shown above the calendar picker. This will include both the slotted title, and the selected date.
         */
        "showDefaultTitle"?: boolean;
        /**
          * If `cover`, the `ion-datetime` will expand to cover the full width of its container. If `fixed`, the `ion-datetime` will have a fixed width.
         */
        "size"?: 'cover' | 'fixed';
        /**
          * A callback used to format the header text that shows how many dates are selected. Only used if there are 0 or more than 1 selected (i.e. unused for exactly 1). By default, the header text is set to "numberOfDates days".  See https://ionicframework.com/docs/troubleshooting/runtime#accessing-this if you need to access `this` from within the callback.
         */
        "titleSelectedDatesFormatter"?: TitleSelectedDatesFormatter;
        /**
          * The value of the datetime as a valid ISO 8601 datetime string. This should be an array of strings only when `multiple="true"`.
         */
        "value"?: string | string[] | null;
        /**
          * Values used to create the list of selectable years. By default the year values range between the `min` and `max` datetime inputs. However, to control exactly which years to display, the `yearValues` input can take a number, an array of numbers, or string of comma separated numbers. For example, to show upcoming and recent leap years, then this input's value would be `yearValues="2008,2012,2016,2020,2024"`.
         */
        "yearValues"?: number[] | number | string;
    }
    interface IonDatetimeButton {
        /**
          * The color to use from your application's color palette. Default options are: `"primary"`, `"secondary"`, `"tertiary"`, `"success"`, `"warning"`, `"danger"`, `"light"`, `"medium"`, and `"dark"`. For more information on colors, see [theming](/docs/theming/basics).
         */
        "color"?: Color;
        /**
          * The ID of the `ion-datetime` instance associated with the datetime button.
         */
        "datetime"?: string;
        /**
          * If `true`, the user cannot interact with the button.
         */
        "disabled"?: boolean;
        /**
          * The mode determines which platform styles to use.
         */
        "mode"?: "ios" | "md";
    }
    interface IonFab {
        /**
          * If `true`, both the `ion-fab-button` and all `ion-fab-list` inside `ion-fab` will become active. That means `ion-fab-button` will become a `close` icon and `ion-fab-list` will become visible.
         */
        "activated"?: boolean;
        /**
          * If `true`, the fab will display on the edge of the header if `vertical` is `"top"`, and on the edge of the footer if it is `"bottom"`. Should be used with a `fixed` slot.
         */
        "edge"?: boolean;
        /**
          * Where to align the fab horizontally in the viewport.
         */
        "horizontal"?: 'start' | 'end' | 'center';
        /**
          * Where to align the fab vertically in the viewport.
         */
        "vertical"?: 'top' | 'bottom' | 'center';
    }
    interface IonFabButton {
        /**
          * If `true`, the fab button will be show a close icon.
         */
        "activated"?: boolean;
        /**
          * The icon name to use for the close icon. This will appear when the fab button is pressed. Only applies if it is the main button inside of a fab containing a fab list.
         */
        "closeIcon"?: string;
        /**
          * The color to use from your application's color palette. Default options are: `"primary"`, `"secondary"`, `"tertiary"`, `"success"`, `"warning"`, `"danger"`, `"light"`, `"medium"`, and `"dark"`. For more information on colors, see [theming](/docs/theming/basics).
         */
        "color"?: Color;
        /**
          * If `true`, the user cannot interact with the fab button.
         */
        "disabled"?: boolean;
        /**
          * This attribute instructs browsers to download a URL instead of navigating to it, so the user will be prompted to save it as a local file. If the attribute has a value, it is used as the pre-filled file name in the Save prompt (the user can still change the file name if they want).
         */
        "download"?: string | undefined;
        /**
          * Contains a URL or a URL fragment that the hyperlink points to. If this property is set, an anchor tag will be rendered.
         */
        "href"?: string | undefined;
        /**
          * The mode determines which platform styles to use.
         */
        "mode"?: "ios" | "md";
        /**
          * Emitted when the button loses focus.
         */
        "onIonBlur"?: (event: IonFabButtonCustomEvent<void>) => void;
        /**
          * Emitted when the button has focus.
         */
        "onIonFocus"?: (event: IonFabButtonCustomEvent<void>) => void;
        /**
          * Specifies the relationship of the target object to the link object. The value is a space-separated list of [link types](https://developer.mozilla.org/en-US/docs/Web/HTML/Link_types).
         */
        "rel"?: string | undefined;
        /**
          * When using a router, it specifies the transition animation when navigating to another page using `href`.
         */
        "routerAnimation"?: AnimationBuilder | undefined;
        /**
          * When using a router, it specifies the transition direction when navigating to another page using `href`.
         */
        "routerDirection"?: RouterDirection;
        /**
          * If `true`, the fab button will show when in a fab-list.
         */
        "show"?: boolean;
        /**
          * The size of the button. Set this to `small` in order to have a mini fab button.
         */
        "size"?: 'small';
        /**
          * Specifies where to display the linked URL. Only applies when an `href` is provided. Special keywords: `"_blank"`, `"_self"`, `"_parent"`, `"_top"`.
         */
        "target"?: string | undefined;
        /**
          * If `true`, the fab button will be translucent. Only applies when the mode is `"ios"` and the device supports [`backdrop-filter`](https://developer.mozilla.org/en-US/docs/Web/CSS/backdrop-filter#Browser_compatibility).
         */
        "translucent"?: boolean;
        /**
          * The type of the button.
         */
        "type"?: 'submit' | 'reset' | 'button';
    }
    interface IonFabList {
        /**
          * If `true`, the fab list will show all fab buttons in the list.
         */
        "activated"?: boolean;
        /**
          * The side the fab list will show on relative to the main fab button.
         */
        "side"?: 'start' | 'end' | 'top' | 'bottom';
    }
    interface IonFooter {
        /**
          * Describes the scroll effect that will be applied to the footer. Only applies in iOS mode.
         */
        "collapse"?: 'fade';
        /**
          * The mode determines which platform styles to use.
         */
        "mode"?: "ios" | "md";
        /**
          * If `true`, the footer will be translucent. Only applies when the mode is `"ios"` and the device supports [`backdrop-filter`](https://developer.mozilla.org/en-US/docs/Web/CSS/backdrop-filter#Browser_compatibility).  Note: In order to scroll content behind the footer, the `fullscreen` attribute needs to be set on the content.
         */
        "translucent"?: boolean;
    }
    interface IonGrid {
        /**
          * If `true`, the grid will have a fixed width based on the screen size.
         */
        "fixed"?: boolean;
    }
    interface IonHeader {
        /**
          * Describes the scroll effect that will be applied to the header. Only applies in iOS mode.  Typically used for [Collapsible Large Titles](https://ionicframework.com/docs/api/title#collapsible-large-titles)
         */
        "collapse"?: 'condense' | 'fade';
        /**
          * The mode determines which platform styles to use.
         */
        "mode"?: "ios" | "md";
        /**
          * If `true`, the header will be translucent. Only applies when the mode is `"ios"` and the device supports [`backdrop-filter`](https://developer.mozilla.org/en-US/docs/Web/CSS/backdrop-filter#Browser_compatibility).  Note: In order to scroll content behind the header, the `fullscreen` attribute needs to be set on the content.
         */
        "translucent"?: boolean;
    }
    interface IonImg {
        /**
          * This attribute defines the alternative text describing the image. Users will see this text displayed if the image URL is wrong, the image is not in one of the supported formats, or if the image is not yet downloaded.
         */
        "alt"?: string;
        /**
          * Emitted when the img fails to load
         */
        "onIonError"?: (event: IonImgCustomEvent<void>) => void;
        /**
          * Emitted when the image has finished loading
         */
        "onIonImgDidLoad"?: (event: IonImgCustomEvent<void>) => void;
        /**
          * Emitted when the img src has been set
         */
        "onIonImgWillLoad"?: (event: IonImgCustomEvent<void>) => void;
        /**
          * The image URL. This attribute is mandatory for the `<img>` element.
         */
        "src"?: string;
    }
    interface IonInfiniteScroll {
        /**
          * If `true`, the infinite scroll will be hidden and scroll event listeners will be removed.  Set this to true to disable the infinite scroll from actively trying to receive new data while scrolling. This is useful when it is known that there is no more data that can be added, and the infinite scroll is no longer needed.
         */
        "disabled"?: boolean;
        /**
          * Emitted when the scroll reaches the threshold distance. From within your infinite handler, you must call the infinite scroll's `complete()` method when your async operation has completed.
         */
        "onIonInfinite"?: (event: IonInfiniteScrollCustomEvent<void>) => void;
        /**
          * The position of the infinite scroll element. The value can be either `top` or `bottom`.
         */
        "position"?: 'top' | 'bottom';
        /**
          * The threshold distance from the bottom of the content to call the `infinite` output event when scrolled. The threshold value can be either a percent, or in pixels. For example, use the value of `10%` for the `infinite` output event to get called when the user has scrolled 10% from the bottom of the page. Use the value `100px` when the scroll is within 100 pixels from the bottom of the page.
         */
        "threshold"?: string;
    }
    interface IonInfiniteScrollContent {
        /**
          * An animated SVG spinner that shows while loading.
         */
        "loadingSpinner"?: SpinnerTypes | null;
        /**
          * Optional text to display while loading. `loadingText` can accept either plaintext or HTML as a string. To display characters normally reserved for HTML, they must be escaped. For example `<Ionic>` would become `&lt;Ionic&gt;`  For more information: [Security Documentation](https://ionicframework.com/docs/faq/security)  This property accepts custom HTML as a string. Content is parsed as plaintext by default. `innerHTMLTemplatesEnabled` must be set to `true` in the Ionic config before custom HTML can be used.
         */
        "loadingText"?: string | IonicSafeString;
    }
    interface IonInput {
        /**
          * This attribute is ignored.
          * @deprecated
         */
        "accept"?: string;
        /**
          * Indicates whether and how the text value should be automatically capitalized as it is entered/edited by the user. Available options: `"off"`, `"none"`, `"on"`, `"sentences"`, `"words"`, `"characters"`.
         */
        "autocapitalize"?: string;
        /**
          * Indicates whether the value of the control can be automatically completed by the browser.
         */
        "autocomplete"?: AutocompleteTypes;
        /**
          * Whether auto correction should be enabled when the user is entering/editing the text value.
         */
        "autocorrect"?: 'on' | 'off';
        /**
          * Sets the [`autofocus` attribute](https://developer.mozilla.org/en-US/docs/Web/HTML/Global_attributes/autofocus) on the native input element.  This may not be sufficient for the element to be focused on page load. See [managing focus](/docs/developing/managing-focus) for more information.
         */
        "autofocus"?: boolean;
        /**
          * If `true`, a clear icon will appear in the input when there is a value. Clicking it clears the input.
         */
        "clearInput"?: boolean;
        /**
          * If `true`, the value will be cleared after focus upon edit. Defaults to `true` when `type` is `"password"`, `false` for all other types.
         */
        "clearOnEdit"?: boolean;
        /**
          * The color to use from your application's color palette. Default options are: `"primary"`, `"secondary"`, `"tertiary"`, `"success"`, `"warning"`, `"danger"`, `"light"`, `"medium"`, and `"dark"`. For more information on colors, see [theming](/docs/theming/basics).
         */
        "color"?: Color;
        /**
          * If `true`, a character counter will display the ratio of characters used and the total character limit. Developers must also set the `maxlength` property for the counter to be calculated correctly.
         */
        "counter"?: boolean;
        /**
          * A callback used to format the counter text. By default the counter text is set to "itemLength / maxLength".  See https://ionicframework.com/docs/troubleshooting/runtime#accessing-this if you need to access `this` from within the callback.
         */
        "counterFormatter"?: (inputLength: number, maxLength: number) => string;
        /**
          * Set the amount of time, in milliseconds, to wait to trigger the `ionInput` event after each keystroke.
         */
        "debounce"?: number;
        /**
          * If `true`, the user cannot interact with the input.
         */
        "disabled"?: boolean;
        /**
          * A hint to the browser for which enter key to display. Possible values: `"enter"`, `"done"`, `"go"`, `"next"`, `"previous"`, `"search"`, and `"send"`.
         */
        "enterkeyhint"?: 'enter' | 'done' | 'go' | 'next' | 'previous' | 'search' | 'send';
        /**
          * Text that is placed under the input and displayed when an error is detected.
         */
        "errorText"?: string;
        /**
          * The fill for the item. If `"solid"` the item will have a background. If `"outline"` the item will be transparent with a border. Only available in `md` mode.
         */
        "fill"?: 'outline' | 'solid';
        /**
          * Text that is placed under the input and displayed when no error is detected.
         */
        "helperText"?: string;
        /**
          * A hint to the browser for which keyboard to display. Possible values: `"none"`, `"text"`, `"tel"`, `"url"`, `"email"`, `"numeric"`, `"decimal"`, and `"search"`.
         */
        "inputmode"?: 'none' | 'text' | 'tel' | 'url' | 'email' | 'numeric' | 'decimal' | 'search';
        /**
          * The visible label associated with the input.  Use this if you need to render a plaintext label.  The `label` property will take priority over the `label` slot if both are used.
         */
        "label"?: string;
        /**
          * Where to place the label relative to the input. `"start"`: The label will appear to the left of the input in LTR and to the right in RTL. `"end"`: The label will appear to the right of the input in LTR and to the left in RTL. `"floating"`: The label will appear smaller and above the input when the input is focused or it has a value. Otherwise it will appear on top of the input. `"stacked"`: The label will appear smaller and above the input regardless even when the input is blurred or has no value. `"fixed"`: The label has the same behavior as `"start"` except it also has a fixed width. Long text will be truncated with ellipses ("...").
         */
        "labelPlacement"?: 'start' | 'end' | 'floating' | 'stacked' | 'fixed';
        /**
          * Set the `legacy` property to `true` to forcibly use the legacy form control markup. Ionic will only opt components in to the modern form markup when they are using either the `aria-label` attribute or the `label` property. As a result, the `legacy` property should only be used as an escape hatch when you want to avoid this automatic opt-in behavior. Note that this property will be removed in an upcoming major release of Ionic, and all form components will be opted-in to using the modern form markup.
         */
        "legacy"?: boolean;
        /**
          * The maximum value, which must not be less than its minimum (min attribute) value.
         */
        "max"?: string | number;
        /**
          * If the value of the type attribute is `text`, `email`, `search`, `password`, `tel`, or `url`, this attribute specifies the maximum number of characters that the user can enter.
         */
        "maxlength"?: number;
        /**
          * The minimum value, which must not be greater than its maximum (max attribute) value.
         */
        "min"?: string | number;
        /**
          * If the value of the type attribute is `text`, `email`, `search`, `password`, `tel`, or `url`, this attribute specifies the minimum number of characters that the user can enter.
         */
        "minlength"?: number;
        /**
          * The mode determines which platform styles to use.
         */
        "mode"?: "ios" | "md";
        /**
          * If `true`, the user can enter more than one value. This attribute applies when the type attribute is set to `"email"`, otherwise it is ignored.
         */
        "multiple"?: boolean;
        /**
          * The name of the control, which is submitted with the form data.
         */
        "name"?: string;
        /**
          * Emitted when the input loses focus.
         */
        "onIonBlur"?: (event: IonInputCustomEvent<FocusEvent>) => void;
        /**
          * The `ionChange` event is fired when the user modifies the input's value. Unlike the `ionInput` event, the `ionChange` event is only fired when changes are committed, not as the user types.  Depending on the way the users interacts with the element, the `ionChange` event fires at a different moment: - When the user commits the change explicitly (e.g. by selecting a date from a date picker for `<ion-input type="date">`, pressing the "Enter" key, etc.). - When the element loses focus after its value has changed: for elements where the user's interaction is typing.
         */
        "onIonChange"?: (event: IonInputCustomEvent<InputChangeEventDetail>) => void;
        /**
          * Emitted when the input has focus.
         */
        "onIonFocus"?: (event: IonInputCustomEvent<FocusEvent>) => void;
        /**
          * The `ionInput` event is fired each time the user modifies the input's value. Unlike the `ionChange` event, the `ionInput` event is fired for each alteration to the input's value. This typically happens for each keystroke as the user types.  For elements that accept text input (`type=text`, `type=tel`, etc.), the interface is [`InputEvent`](https://developer.mozilla.org/en-US/docs/Web/API/InputEvent); for others, the interface is [`Event`](https://developer.mozilla.org/en-US/docs/Web/API/Event). If the input is cleared on edit, the type is `null`.
         */
        "onIonInput"?: (event: IonInputCustomEvent<InputInputEventDetail>) => void;
        /**
          * A regular expression that the value is checked against. The pattern must match the entire value, not just some subset. Use the title attribute to describe the pattern to help the user. This attribute applies when the value of the type attribute is `"text"`, `"search"`, `"tel"`, `"url"`, `"email"`, `"date"`, or `"password"`, otherwise it is ignored. When the type attribute is `"date"`, `pattern` will only be used in browsers that do not support the `"date"` input type natively. See https://developer.mozilla.org/en-US/docs/Web/HTML/Element/input/date for more information.
         */
        "pattern"?: string;
        /**
          * Instructional text that shows before the input has a value. This property applies only when the `type` property is set to `"email"`, `"number"`, `"password"`, `"search"`, `"tel"`, `"text"`, or `"url"`, otherwise it is ignored.
         */
        "placeholder"?: string;
        /**
          * If `true`, the user cannot modify the value.
         */
        "readonly"?: boolean;
        /**
          * If `true`, the user must fill in a value before submitting a form.
         */
        "required"?: boolean;
        /**
          * The shape of the input. If "round" it will have an increased border radius.
         */
        "shape"?: 'round';
        "size"?: number;
        /**
          * If `true`, the element will have its spelling and grammar checked.
         */
        "spellcheck"?: boolean;
        /**
          * Works with the min and max attributes to limit the increments at which a value can be set. Possible values are: `"any"` or a positive floating point number.
         */
        "step"?: string;
        /**
          * The type of control to display. The default type is text.
         */
        "type"?: TextFieldTypes;
        /**
          * The value of the input.
         */
        "value"?: string | number | null;
    }
    interface IonItem {
        /**
          * If `true`, a button tag will be rendered and the item will be tappable.
         */
        "button"?: boolean;
        /**
          * The color to use from your application's color palette. Default options are: `"primary"`, `"secondary"`, `"tertiary"`, `"success"`, `"warning"`, `"danger"`, `"light"`, `"medium"`, and `"dark"`. For more information on colors, see [theming](/docs/theming/basics).
         */
        "color"?: Color;
        /**
          * If `true`, a character counter will display the ratio of characters used and the total character limit. Only applies when the `maxlength` property is set on the inner `ion-input` or `ion-textarea`.
          * @deprecated Use the `counter` property on `ion-input` or `ion-textarea` instead.
         */
        "counter"?: boolean;
        /**
          * A callback used to format the counter text. By default the counter text is set to "itemLength / maxLength".
          * @deprecated Use the `counterFormatter` property on `ion-input` or `ion-textarea` instead.
         */
        "counterFormatter"?: CounterFormatter;
        /**
          * If `true`, a detail arrow will appear on the item. Defaults to `false` unless the `mode` is `ios` and an `href` or `button` property is present.
         */
        "detail"?: boolean;
        /**
          * The icon to use when `detail` is set to `true`.
         */
        "detailIcon"?: string;
        /**
          * If `true`, the user cannot interact with the item.
         */
        "disabled"?: boolean;
        /**
          * This attribute instructs browsers to download a URL instead of navigating to it, so the user will be prompted to save it as a local file. If the attribute has a value, it is used as the pre-filled file name in the Save prompt (the user can still change the file name if they want).
         */
        "download"?: string | undefined;
        /**
          * The fill for the item. If `"solid"` the item will have a background. If `"outline"` the item will be transparent with a border. Only available in `md` mode.
          * @deprecated Use the `fill` property on `ion-input` or `ion-textarea` instead.
         */
        "fill"?: 'outline' | 'solid';
        /**
          * Contains a URL or a URL fragment that the hyperlink points to. If this property is set, an anchor tag will be rendered.
         */
        "href"?: string | undefined;
        /**
          * How the bottom border should be displayed on the item.
         */
        "lines"?: 'full' | 'inset' | 'none';
        /**
          * The mode determines which platform styles to use.
         */
        "mode"?: "ios" | "md";
        /**
          * Specifies the relationship of the target object to the link object. The value is a space-separated list of [link types](https://developer.mozilla.org/en-US/docs/Web/HTML/Link_types).
         */
        "rel"?: string | undefined;
        /**
          * When using a router, it specifies the transition animation when navigating to another page using `href`.
         */
        "routerAnimation"?: AnimationBuilder | undefined;
        /**
          * When using a router, it specifies the transition direction when navigating to another page using `href`.
         */
        "routerDirection"?: RouterDirection;
        /**
          * The shape of the item. If "round" it will have increased border radius.
         */
        "shape"?: 'round';
        /**
          * Specifies where to display the linked URL. Only applies when an `href` is provided. Special keywords: `"_blank"`, `"_self"`, `"_parent"`, `"_top"`.
         */
        "target"?: string | undefined;
        /**
          * The type of the button. Only used when an `onclick` or `button` property is present.
         */
        "type"?: 'submit' | 'reset' | 'button';
    }
    interface IonItemDivider {
        /**
          * The color to use from your application's color palette. Default options are: `"primary"`, `"secondary"`, `"tertiary"`, `"success"`, `"warning"`, `"danger"`, `"light"`, `"medium"`, and `"dark"`. For more information on colors, see [theming](/docs/theming/basics).
         */
        "color"?: Color;
        /**
          * The mode determines which platform styles to use.
         */
        "mode"?: "ios" | "md";
        /**
          * When it's set to `true`, the item-divider will stay visible when it reaches the top of the viewport until the next `ion-item-divider` replaces it.  This feature relies in `position:sticky`: https://caniuse.com/#feat=css-sticky
         */
        "sticky"?: boolean;
    }
    interface IonItemGroup {
    }
    interface IonItemOption {
        /**
          * The color to use from your application's color palette. Default options are: `"primary"`, `"secondary"`, `"tertiary"`, `"success"`, `"warning"`, `"danger"`, `"light"`, `"medium"`, and `"dark"`. For more information on colors, see [theming](/docs/theming/basics).
         */
        "color"?: Color;
        /**
          * If `true`, the user cannot interact with the item option.
         */
        "disabled"?: boolean;
        /**
          * This attribute instructs browsers to download a URL instead of navigating to it, so the user will be prompted to save it as a local file. If the attribute has a value, it is used as the pre-filled file name in the Save prompt (the user can still change the file name if they want).
         */
        "download"?: string | undefined;
        /**
          * If `true`, the option will expand to take up the available width and cover any other options.
         */
        "expandable"?: boolean;
        /**
          * Contains a URL or a URL fragment that the hyperlink points to. If this property is set, an anchor tag will be rendered.
         */
        "href"?: string | undefined;
        /**
          * The mode determines which platform styles to use.
         */
        "mode"?: "ios" | "md";
        /**
          * Specifies the relationship of the target object to the link object. The value is a space-separated list of [link types](https://developer.mozilla.org/en-US/docs/Web/HTML/Link_types).
         */
        "rel"?: string | undefined;
        /**
          * Specifies where to display the linked URL. Only applies when an `href` is provided. Special keywords: `"_blank"`, `"_self"`, `"_parent"`, `"_top"`.
         */
        "target"?: string | undefined;
        /**
          * The type of the button.
         */
        "type"?: 'submit' | 'reset' | 'button';
    }
    interface IonItemOptions {
        /**
          * Emitted when the item has been fully swiped.
         */
        "onIonSwipe"?: (event: IonItemOptionsCustomEvent<any>) => void;
        /**
          * The side the option button should be on. Possible values: `"start"` and `"end"`. If you have multiple `ion-item-options`, a side must be provided for each.
         */
        "side"?: Side;
    }
    interface IonItemSliding {
        /**
          * If `true`, the user cannot interact with the sliding item.
         */
        "disabled"?: boolean;
        /**
          * Emitted when the sliding position changes.
         */
        "onIonDrag"?: (event: IonItemSlidingCustomEvent<any>) => void;
    }
    interface IonLabel {
        /**
          * The color to use from your application's color palette. Default options are: `"primary"`, `"secondary"`, `"tertiary"`, `"success"`, `"warning"`, `"danger"`, `"light"`, `"medium"`, and `"dark"`. For more information on colors, see [theming](/docs/theming/basics).
         */
        "color"?: Color;
        /**
          * The mode determines which platform styles to use.
         */
        "mode"?: "ios" | "md";
        /**
          * The position determines where and how the label behaves inside an item.
         */
        "position"?: 'fixed' | 'stacked' | 'floating';
    }
    interface IonList {
        /**
          * If `true`, the list will have margin around it and rounded corners.
         */
        "inset"?: boolean;
        /**
          * How the bottom border should be displayed on all items.
         */
        "lines"?: 'full' | 'inset' | 'none';
        /**
          * The mode determines which platform styles to use.
         */
        "mode"?: "ios" | "md";
    }
    interface IonListHeader {
        /**
          * The color to use from your application's color palette. Default options are: `"primary"`, `"secondary"`, `"tertiary"`, `"success"`, `"warning"`, `"danger"`, `"light"`, `"medium"`, and `"dark"`. For more information on colors, see [theming](/docs/theming/basics).
         */
        "color"?: Color;
        /**
          * How the bottom border should be displayed on the list header.
         */
        "lines"?: 'full' | 'inset' | 'none';
        /**
          * The mode determines which platform styles to use.
         */
        "mode"?: "ios" | "md";
    }
    interface IonLoading {
        /**
          * If `true`, the loading indicator will animate.
         */
        "animated"?: boolean;
        /**
          * If `true`, the loading indicator will be dismissed when the backdrop is clicked.
         */
        "backdropDismiss"?: boolean;
        /**
          * Additional classes to apply for custom CSS. If multiple classes are provided they should be separated by spaces.
         */
        "cssClass"?: string | string[];
        /**
          * Number of milliseconds to wait before dismissing the loading indicator.
         */
        "duration"?: number;
        /**
          * Animation to use when the loading indicator is presented.
         */
        "enterAnimation"?: AnimationBuilder;
        /**
          * Additional attributes to pass to the loader.
         */
        "htmlAttributes"?: { [key: string]: any };
        /**
          * If `true`, the loading indicator will open. If `false`, the loading indicator will close. Use this if you need finer grained control over presentation, otherwise just use the loadingController or the `trigger` property. Note: `isOpen` will not automatically be set back to `false` when the loading indicator dismisses. You will need to do that in your code.
         */
        "isOpen"?: boolean;
        /**
          * If `true`, the keyboard will be automatically dismissed when the overlay is presented.
         */
        "keyboardClose"?: boolean;
        /**
          * Animation to use when the loading indicator is dismissed.
         */
        "leaveAnimation"?: AnimationBuilder;
        /**
          * Optional text content to display in the loading indicator.  This property accepts custom HTML as a string. Content is parsed as plaintext by default. `innerHTMLTemplatesEnabled` must be set to `true` in the Ionic config before custom HTML can be used.
         */
        "message"?: string | IonicSafeString;
        /**
          * The mode determines which platform styles to use.
         */
        "mode"?: "ios" | "md";
        /**
          * Emitted after the loading indicator has dismissed. Shorthand for ionLoadingDidDismiss.
         */
        "onDidDismiss"?: (event: IonLoadingCustomEvent<OverlayEventDetail>) => void;
        /**
          * Emitted after the loading indicator has presented. Shorthand for ionLoadingWillDismiss.
         */
        "onDidPresent"?: (event: IonLoadingCustomEvent<void>) => void;
        /**
          * Emitted after the loading has dismissed.
         */
        "onIonLoadingDidDismiss"?: (event: IonLoadingCustomEvent<OverlayEventDetail>) => void;
        /**
          * Emitted after the loading has presented.
         */
        "onIonLoadingDidPresent"?: (event: IonLoadingCustomEvent<void>) => void;
        /**
          * Emitted before the loading has dismissed.
         */
        "onIonLoadingWillDismiss"?: (event: IonLoadingCustomEvent<OverlayEventDetail>) => void;
        /**
          * Emitted before the loading has presented.
         */
        "onIonLoadingWillPresent"?: (event: IonLoadingCustomEvent<void>) => void;
        /**
          * Emitted before the loading indicator has dismissed. Shorthand for ionLoadingWillDismiss.
         */
        "onWillDismiss"?: (event: IonLoadingCustomEvent<OverlayEventDetail>) => void;
        /**
          * Emitted before the loading indicator has presented. Shorthand for ionLoadingWillPresent.
         */
        "onWillPresent"?: (event: IonLoadingCustomEvent<void>) => void;
        /**
          * If `true`, a backdrop will be displayed behind the loading indicator.
         */
        "showBackdrop"?: boolean;
        /**
          * The name of the spinner to display.
         */
        "spinner"?: SpinnerTypes | null;
        /**
          * If `true`, the loading indicator will be translucent. Only applies when the mode is `"ios"` and the device supports [`backdrop-filter`](https://developer.mozilla.org/en-US/docs/Web/CSS/backdrop-filter#Browser_compatibility).
         */
        "translucent"?: boolean;
        /**
          * An ID corresponding to the trigger element that causes the loading indicator to open when clicked.
         */
        "trigger"?: string | undefined;
    }
    interface IonMenu {
        /**
          * The `id` of the main content. When using a router this is typically `ion-router-outlet`. When not using a router, this is typically your main view's `ion-content`. This is not the id of the `ion-content` inside of your `ion-menu`.
         */
        "contentId"?: string;
        /**
          * If `true`, the menu is disabled.
         */
        "disabled"?: boolean;
        /**
          * The edge threshold for dragging the menu open. If a drag/swipe happens over this value, the menu is not triggered.
         */
        "maxEdgeStart"?: number;
        /**
          * An id for the menu.
         */
        "menuId"?: string;
        /**
          * Emitted when the menu is closed.
         */
        "onIonDidClose"?: (event: IonMenuCustomEvent<void>) => void;
        /**
          * Emitted when the menu is open.
         */
        "onIonDidOpen"?: (event: IonMenuCustomEvent<void>) => void;
        /**
          * Emitted when the menu is about to be closed.
         */
        "onIonWillClose"?: (event: IonMenuCustomEvent<void>) => void;
        /**
          * Emitted when the menu is about to be opened.
         */
        "onIonWillOpen"?: (event: IonMenuCustomEvent<void>) => void;
        /**
          * Which side of the view the menu should be placed.
         */
        "side"?: Side;
        /**
          * If `true`, swiping the menu is enabled.
         */
        "swipeGesture"?: boolean;
        /**
          * The display type of the menu. Available options: `"overlay"`, `"reveal"`, `"push"`.
         */
        "type"?: string;
    }
    interface IonMenuButton {
        /**
          * Automatically hides the menu button when the corresponding menu is not active
         */
        "autoHide"?: boolean;
        /**
          * The color to use from your application's color palette. Default options are: `"primary"`, `"secondary"`, `"tertiary"`, `"success"`, `"warning"`, `"danger"`, `"light"`, `"medium"`, and `"dark"`. For more information on colors, see [theming](/docs/theming/basics).
         */
        "color"?: Color;
        /**
          * If `true`, the user cannot interact with the menu button.
         */
        "disabled"?: boolean;
        /**
          * Optional property that maps to a Menu's `menuId` prop. Can also be `start` or `end` for the menu side. This is used to find the correct menu to toggle
         */
        "menu"?: string;
        /**
          * The mode determines which platform styles to use.
         */
        "mode"?: "ios" | "md";
        /**
          * The type of the button.
         */
        "type"?: 'submit' | 'reset' | 'button';
    }
    interface IonMenuToggle {
        /**
          * Automatically hides the content when the corresponding menu is not active.  By default, it's `true`. Change it to `false` in order to keep `ion-menu-toggle` always visible regardless the state of the menu.
         */
        "autoHide"?: boolean;
        /**
          * Optional property that maps to a Menu's `menuId` prop. Can also be `start` or `end` for the menu side. This is used to find the correct menu to toggle.  If this property is not used, `ion-menu-toggle` will toggle the first menu that is active.
         */
        "menu"?: string;
    }
    interface IonModal {
        /**
          * If `true`, the modal will animate.
         */
        "animated"?: boolean;
        /**
          * A decimal value between 0 and 1 that indicates the point after which the backdrop will begin to fade in when using a sheet modal. Prior to this point, the backdrop will be hidden and the content underneath the sheet can be interacted with. This value is exclusive meaning the backdrop will become active after the value specified.
         */
        "backdropBreakpoint"?: number;
        /**
          * If `true`, the modal will be dismissed when the backdrop is clicked.
         */
        "backdropDismiss"?: boolean;
        /**
          * The breakpoints to use when creating a sheet modal. Each value in the array must be a decimal between 0 and 1 where 0 indicates the modal is fully closed and 1 indicates the modal is fully open. Values are relative to the height of the modal, not the height of the screen. One of the values in this array must be the value of the `initialBreakpoint` property. For example: [0, .25, .5, 1]
         */
        "breakpoints"?: number[];
        /**
          * Determines whether or not a modal can dismiss when calling the `dismiss` method.  If the value is `true` or the value's function returns `true`, the modal will close when trying to dismiss. If the value is `false` or the value's function returns `false`, the modal will not close when trying to dismiss.  See https://ionicframework.com/docs/troubleshooting/runtime#accessing-this if you need to access `this` from within the callback.
         */
        "canDismiss"?: boolean | ((data?: any, role?: string) => Promise<boolean>);
        /**
          * Animation to use when the modal is presented.
         */
        "enterAnimation"?: AnimationBuilder;
        /**
          * The horizontal line that displays at the top of a sheet modal. It is `true` by default when setting the `breakpoints` and `initialBreakpoint` properties.
         */
        "handle"?: boolean;
        /**
          * The interaction behavior for the sheet modal when the handle is pressed.  Defaults to `"none"`, which  means the modal will not change size or position when the handle is pressed. Set to `"cycle"` to let the modal cycle between available breakpoints when pressed.  Handle behavior is unavailable when the `handle` property is set to `false` or when the `breakpoints` property is not set (using a fullscreen or card modal).
         */
        "handleBehavior"?: ModalHandleBehavior;
        /**
          * Additional attributes to pass to the modal.
         */
        "htmlAttributes"?: { [key: string]: any };
        /**
          * A decimal value between 0 and 1 that indicates the initial point the modal will open at when creating a sheet modal. This value must also be listed in the `breakpoints` array.
         */
        "initialBreakpoint"?: number;
        /**
          * If `true`, the modal will open. If `false`, the modal will close. Use this if you need finer grained control over presentation, otherwise just use the modalController or the `trigger` property. Note: `isOpen` will not automatically be set back to `false` when the modal dismisses. You will need to do that in your code.
         */
        "isOpen"?: boolean;
        /**
          * If `true`, the component passed into `ion-modal` will automatically be mounted when the modal is created. The component will remain mounted even when the modal is dismissed. However, the component will be destroyed when the modal is destroyed. This property is not reactive and should only be used when initially creating a modal.  Note: This feature only applies to inline modals in JavaScript frameworks such as Angular, React, and Vue.
         */
        "keepContentsMounted"?: boolean;
        /**
          * If `true`, the keyboard will be automatically dismissed when the overlay is presented.
         */
        "keyboardClose"?: boolean;
        /**
          * Animation to use when the modal is dismissed.
         */
        "leaveAnimation"?: AnimationBuilder;
        /**
          * The mode determines which platform styles to use.
         */
        "mode"?: "ios" | "md";
        /**
          * Emitted after the modal has dismissed. Shorthand for ionModalDidDismiss.
         */
        "onDidDismiss"?: (event: IonModalCustomEvent<OverlayEventDetail>) => void;
        /**
          * Emitted after the modal has presented. Shorthand for ionModalDidPresent.
         */
        "onDidPresent"?: (event: IonModalCustomEvent<void>) => void;
        /**
          * Emitted after the modal breakpoint has changed.
         */
        "onIonBreakpointDidChange"?: (event: IonModalCustomEvent<ModalBreakpointChangeEventDetail>) => void;
        /**
          * Emitted after the modal has dismissed.
         */
        "onIonModalDidDismiss"?: (event: IonModalCustomEvent<OverlayEventDetail>) => void;
        /**
          * Emitted after the modal has presented.
         */
        "onIonModalDidPresent"?: (event: IonModalCustomEvent<void>) => void;
        /**
          * Emitted before the modal has dismissed.
         */
        "onIonModalWillDismiss"?: (event: IonModalCustomEvent<OverlayEventDetail>) => void;
        /**
          * Emitted before the modal has presented.
         */
        "onIonModalWillPresent"?: (event: IonModalCustomEvent<void>) => void;
        /**
          * Emitted before the modal has dismissed. Shorthand for ionModalWillDismiss.
         */
        "onWillDismiss"?: (event: IonModalCustomEvent<OverlayEventDetail>) => void;
        /**
          * Emitted before the modal has presented. Shorthand for ionModalWillPresent.
         */
        "onWillPresent"?: (event: IonModalCustomEvent<void>) => void;
        /**
          * The element that presented the modal. This is used for card presentation effects and for stacking multiple modals on top of each other. Only applies in iOS mode.
         */
        "presentingElement"?: HTMLElement;
        /**
          * If `true`, a backdrop will be displayed behind the modal. This property controls whether or not the backdrop darkens the screen when the modal is presented. It does not control whether or not the backdrop is active or present in the DOM.
         */
        "showBackdrop"?: boolean;
        /**
          * An ID corresponding to the trigger element that causes the modal to open when clicked.
         */
        "trigger"?: string | undefined;
    }
    interface IonNav {
        /**
          * If `true`, the nav should animate the transition of components.
         */
        "animated"?: boolean;
        /**
          * By default `ion-nav` animates transition between pages based in the mode (ios or material design). However, this property allows to create custom transition using `AnimationBuilder` functions.
         */
        "animation"?: AnimationBuilder;
        /**
          * Event fired when the nav has changed components
         */
        "onIonNavDidChange"?: (event: IonNavCustomEvent<void>) => void;
        /**
          * Event fired when the nav will change components
         */
        "onIonNavWillChange"?: (event: IonNavCustomEvent<void>) => void;
        /**
          * Root NavComponent to load
         */
        "root"?: NavComponent;
        /**
          * Any parameters for the root component
         */
        "rootParams"?: ComponentProps;
        /**
          * If the nav component should allow for swipe-to-go-back.
         */
        "swipeGesture"?: boolean;
    }
    interface IonNavLink {
        /**
          * Component to navigate to. Only used if the `routerDirection` is `"forward"` or `"root"`.
         */
        "component"?: NavComponent;
        /**
          * Data you want to pass to the component as props. Only used if the `"routerDirection"` is `"forward"` or `"root"`.
         */
        "componentProps"?: ComponentProps;
        /**
          * The transition animation when navigating to another page.
         */
        "routerAnimation"?: AnimationBuilder;
        /**
          * The transition direction when navigating to another page.
         */
        "routerDirection"?: RouterDirection;
    }
    interface IonNote {
        /**
          * The color to use from your application's color palette. Default options are: `"primary"`, `"secondary"`, `"tertiary"`, `"success"`, `"warning"`, `"danger"`, `"light"`, `"medium"`, and `"dark"`. For more information on colors, see [theming](/docs/theming/basics).
         */
        "color"?: Color;
        /**
          * The mode determines which platform styles to use.
         */
        "mode"?: "ios" | "md";
    }
    interface IonPicker {
        /**
          * If `true`, the picker will animate.
         */
        "animated"?: boolean;
        /**
          * If `true`, the picker will be dismissed when the backdrop is clicked.
         */
        "backdropDismiss"?: boolean;
        /**
          * Array of buttons to be displayed at the top of the picker.
         */
        "buttons"?: PickerButton[];
        /**
          * Array of columns to be displayed in the picker.
         */
        "columns"?: PickerColumn[];
        /**
          * Additional classes to apply for custom CSS. If multiple classes are provided they should be separated by spaces.
         */
        "cssClass"?: string | string[];
        /**
          * Number of milliseconds to wait before dismissing the picker.
         */
        "duration"?: number;
        /**
          * Animation to use when the picker is presented.
         */
        "enterAnimation"?: AnimationBuilder;
        /**
          * Additional attributes to pass to the picker.
         */
        "htmlAttributes"?: { [key: string]: any };
        /**
          * If `true`, the picker will open. If `false`, the picker will close. Use this if you need finer grained control over presentation, otherwise just use the pickerController or the `trigger` property. Note: `isOpen` will not automatically be set back to `false` when the picker dismisses. You will need to do that in your code.
         */
        "isOpen"?: boolean;
        /**
          * If `true`, the keyboard will be automatically dismissed when the overlay is presented.
         */
        "keyboardClose"?: boolean;
        /**
          * Animation to use when the picker is dismissed.
         */
        "leaveAnimation"?: AnimationBuilder;
        /**
          * The mode determines which platform styles to use.
         */
        "mode"?: "ios" | "md";
        /**
          * Emitted after the picker has dismissed. Shorthand for ionPickerDidDismiss.
         */
        "onDidDismiss"?: (event: IonPickerCustomEvent<OverlayEventDetail>) => void;
        /**
          * Emitted after the picker has presented. Shorthand for ionPickerWillDismiss.
         */
        "onDidPresent"?: (event: IonPickerCustomEvent<void>) => void;
        /**
          * Emitted after the picker has dismissed.
         */
        "onIonPickerDidDismiss"?: (event: IonPickerCustomEvent<OverlayEventDetail>) => void;
        /**
          * Emitted after the picker has presented.
         */
        "onIonPickerDidPresent"?: (event: IonPickerCustomEvent<void>) => void;
        /**
          * Emitted before the picker has dismissed.
         */
        "onIonPickerWillDismiss"?: (event: IonPickerCustomEvent<OverlayEventDetail>) => void;
        /**
          * Emitted before the picker has presented.
         */
        "onIonPickerWillPresent"?: (event: IonPickerCustomEvent<void>) => void;
        /**
          * Emitted before the picker has dismissed. Shorthand for ionPickerWillDismiss.
         */
        "onWillDismiss"?: (event: IonPickerCustomEvent<OverlayEventDetail>) => void;
        /**
          * Emitted before the picker has presented. Shorthand for ionPickerWillPresent.
         */
        "onWillPresent"?: (event: IonPickerCustomEvent<void>) => void;
        /**
          * If `true`, a backdrop will be displayed behind the picker.
         */
        "showBackdrop"?: boolean;
        /**
          * An ID corresponding to the trigger element that causes the picker to open when clicked.
         */
        "trigger"?: string | undefined;
    }
    interface IonPickerColumn {
        /**
          * Picker column data
         */
        "col": PickerColumn;
    }
    interface IonPickerColumnInternal {
        /**
          * The color to use from your application's color palette. Default options are: `"primary"`, `"secondary"`, `"tertiary"`, `"success"`, `"warning"`, `"danger"`, `"light"`, `"medium"`, and `"dark"`. For more information on colors, see [theming](/docs/theming/basics).
         */
        "color"?: Color;
        /**
          * If `true`, the user cannot interact with the picker.
         */
        "disabled"?: boolean;
        /**
          * A list of options to be displayed in the picker
         */
        "items"?: PickerColumnItem[];
        /**
          * The mode determines which platform styles to use.
         */
        "mode"?: "ios" | "md";
        /**
          * Emitted when the value has changed.
         */
        "onIonChange"?: (event: IonPickerColumnInternalCustomEvent<PickerColumnItem>) => void;
        /**
          * The selected option in the picker.
         */
        "value"?: string | number;
    }
    interface IonPickerInternal {
        /**
          * The mode determines which platform styles to use.
         */
        "mode"?: "ios" | "md";
        "onIonInputModeChange"?: (event: IonPickerInternalCustomEvent<PickerInternalChangeEventDetail>) => void;
    }
    interface IonPopover {
        /**
          * Describes how to align the popover content with the `reference` point. Defaults to `"center"` for `ios` mode, and `"start"` for `md` mode.
         */
        "alignment"?: PositionAlign;
        /**
          * If `true`, the popover will animate.
         */
        "animated"?: boolean;
        /**
          * If `true`, the popover will display an arrow that points at the `reference` when running in `ios` mode. Does not apply in `md` mode.
         */
        "arrow"?: boolean;
        /**
          * If `true`, the popover will be dismissed when the backdrop is clicked.
         */
        "backdropDismiss"?: boolean;
        /**
          * The component to display inside of the popover. You only need to use this if you are not using a JavaScript framework. Otherwise, you can just slot your component inside of `ion-popover`.
         */
        "component"?: ComponentRef;
        /**
          * The data to pass to the popover component. You only need to use this if you are not using a JavaScript framework. Otherwise, you can just set the props directly on your component.
         */
        "componentProps"?: ComponentProps;
        /**
          * If `true`, the popover will be automatically dismissed when the content has been clicked.
         */
        "dismissOnSelect"?: boolean;
        /**
          * Animation to use when the popover is presented.
         */
        "enterAnimation"?: AnimationBuilder;
        /**
          * The event to pass to the popover animation.
         */
        "event"?: any;
        /**
          * Additional attributes to pass to the popover.
         */
        "htmlAttributes"?: { [key: string]: any };
        /**
          * If `true`, the popover will open. If `false`, the popover will close. Use this if you need finer grained control over presentation, otherwise just use the popoverController or the `trigger` property. Note: `isOpen` will not automatically be set back to `false` when the popover dismisses. You will need to do that in your code.
         */
        "isOpen"?: boolean;
        /**
          * If `true`, the component passed into `ion-popover` will automatically be mounted when the popover is created. The component will remain mounted even when the popover is dismissed. However, the component will be destroyed when the popover is destroyed. This property is not reactive and should only be used when initially creating a popover.  Note: This feature only applies to inline popovers in JavaScript frameworks such as Angular, React, and Vue.
         */
        "keepContentsMounted"?: boolean;
        /**
          * If `true`, the keyboard will be automatically dismissed when the overlay is presented.
         */
        "keyboardClose"?: boolean;
        /**
          * Animation to use when the popover is dismissed.
         */
        "leaveAnimation"?: AnimationBuilder;
        /**
          * The mode determines which platform styles to use.
         */
        "mode"?: "ios" | "md";
        /**
          * Emitted after the popover has dismissed. Shorthand for ionPopoverDidDismiss.
         */
        "onDidDismiss"?: (event: IonPopoverCustomEvent<OverlayEventDetail>) => void;
        /**
          * Emitted after the popover has presented. Shorthand for ionPopoverWillDismiss.
         */
        "onDidPresent"?: (event: IonPopoverCustomEvent<void>) => void;
        /**
          * Emitted after the popover has dismissed.
         */
        "onIonPopoverDidDismiss"?: (event: IonPopoverCustomEvent<OverlayEventDetail>) => void;
        /**
          * Emitted after the popover has presented.
         */
        "onIonPopoverDidPresent"?: (event: IonPopoverCustomEvent<void>) => void;
        /**
          * Emitted before the popover has dismissed.
         */
        "onIonPopoverWillDismiss"?: (event: IonPopoverCustomEvent<OverlayEventDetail>) => void;
        /**
          * Emitted before the popover has presented.
         */
        "onIonPopoverWillPresent"?: (event: IonPopoverCustomEvent<void>) => void;
        /**
          * Emitted before the popover has dismissed. Shorthand for ionPopoverWillDismiss.
         */
        "onWillDismiss"?: (event: IonPopoverCustomEvent<OverlayEventDetail>) => void;
        /**
          * Emitted before the popover has presented. Shorthand for ionPopoverWillPresent.
         */
        "onWillPresent"?: (event: IonPopoverCustomEvent<void>) => void;
        /**
          * Describes what to position the popover relative to. If `"trigger"`, the popover will be positioned relative to the trigger button. If passing in an event, this is determined via event.target. If `"event"`, the popover will be positioned relative to the x/y coordinates of the trigger action. If passing in an event, this is determined via event.clientX and event.clientY.
         */
        "reference"?: PositionReference;
        /**
          * If `true`, a backdrop will be displayed behind the popover. This property controls whether or not the backdrop darkens the screen when the popover is presented. It does not control whether or not the backdrop is active or present in the DOM.
         */
        "showBackdrop"?: boolean;
        /**
          * Describes which side of the `reference` point to position the popover on. The `"start"` and `"end"` values are RTL-aware, and the `"left"` and `"right"` values are not.
         */
        "side"?: PositionSide;
        /**
          * Describes how to calculate the popover width. If `"cover"`, the popover width will match the width of the trigger. If `"auto"`, the popover width will be set to a static default value.
         */
        "size"?: PopoverSize;
        /**
          * If `true`, the popover will be translucent. Only applies when the mode is `"ios"` and the device supports [`backdrop-filter`](https://developer.mozilla.org/en-US/docs/Web/CSS/backdrop-filter#Browser_compatibility).
         */
        "translucent"?: boolean;
        /**
          * An ID corresponding to the trigger element that causes the popover to open. Use the `trigger-action` property to customize the interaction that results in the popover opening.
         */
        "trigger"?: string | undefined;
        /**
          * Describes what kind of interaction with the trigger that should cause the popover to open. Does not apply when the `trigger` property is `undefined`. If `"click"`, the popover will be presented when the trigger is left clicked. If `"hover"`, the popover will be presented when a pointer hovers over the trigger. If `"context-menu"`, the popover will be presented when the trigger is right clicked on desktop and long pressed on mobile. This will also prevent your device's normal context menu from appearing.
         */
        "triggerAction"?: TriggerAction;
    }
    interface IonProgressBar {
        /**
          * If the buffer and value are smaller than 1, the buffer circles will show. The buffer should be between [0, 1].
         */
        "buffer"?: number;
        /**
          * The color to use from your application's color palette. Default options are: `"primary"`, `"secondary"`, `"tertiary"`, `"success"`, `"warning"`, `"danger"`, `"light"`, `"medium"`, and `"dark"`. For more information on colors, see [theming](/docs/theming/basics).
         */
        "color"?: Color;
        /**
          * The mode determines which platform styles to use.
         */
        "mode"?: "ios" | "md";
        /**
          * If true, reverse the progress bar direction.
         */
        "reversed"?: boolean;
        /**
          * The state of the progress bar, based on if the time the process takes is known or not. Default options are: `"determinate"` (no animation), `"indeterminate"` (animate from left to right).
         */
        "type"?: 'determinate' | 'indeterminate';
        /**
          * The value determines how much of the active bar should display when the `type` is `"determinate"`. The value should be between [0, 1].
         */
        "value"?: number;
    }
    interface IonRadio {
        /**
          * How to control the alignment of the radio and label on the cross axis. `"start"`: The label and control will appear on the left of the cross axis in LTR, and on the right side in RTL. `"center"`: The label and control will appear at the center of the cross axis in both LTR and RTL.
         */
        "alignment"?: 'start' | 'center';
        /**
          * The color to use from your application's color palette. Default options are: `"primary"`, `"secondary"`, `"tertiary"`, `"success"`, `"warning"`, `"danger"`, `"light"`, `"medium"`, and `"dark"`. For more information on colors, see [theming](/docs/theming/basics).
         */
        "color"?: Color;
        /**
          * If `true`, the user cannot interact with the radio.
         */
        "disabled"?: boolean;
        /**
          * How to pack the label and radio within a line. `"start"`: The label and radio will appear on the left in LTR and on the right in RTL. `"end"`: The label and radio will appear on the right in LTR and on the left in RTL. `"space-between"`: The label and radio will appear on opposite ends of the line with space between the two elements.
         */
        "justify"?: 'start' | 'end' | 'space-between';
        /**
          * Where to place the label relative to the radio. `"start"`: The label will appear to the left of the radio in LTR and to the right in RTL. `"end"`: The label will appear to the right of the radio in LTR and to the left in RTL. `"fixed"`: The label has the same behavior as `"start"` except it also has a fixed width. Long text will be truncated with ellipses ("..."). `"stacked"`: The label will appear above the radio regardless of the direction. The alignment of the label can be controlled with the `alignment` property.
         */
        "labelPlacement"?: 'start' | 'end' | 'fixed' | 'stacked';
        /**
          * Set the `legacy` property to `true` to forcibly use the legacy form control markup. Ionic will only opt components in to the modern form markup when they are using either the `aria-label` attribute or the default slot that contains the label text. As a result, the `legacy` property should only be used as an escape hatch when you want to avoid this automatic opt-in behavior. Note that this property will be removed in an upcoming major release of Ionic, and all form components will be opted-in to using the modern form markup.
         */
        "legacy"?: boolean;
        /**
          * The mode determines which platform styles to use.
         */
        "mode"?: "ios" | "md";
        /**
          * The name of the control, which is submitted with the form data.
         */
        "name"?: string;
        /**
          * Emitted when the radio button loses focus.
         */
        "onIonBlur"?: (event: IonRadioCustomEvent<void>) => void;
        /**
          * Emitted when the radio button has focus.
         */
        "onIonFocus"?: (event: IonRadioCustomEvent<void>) => void;
        /**
          * the value of the radio.
         */
        "value"?: any | null;
    }
    interface IonRadioGroup {
        /**
          * If `true`, the radios can be deselected.
         */
        "allowEmptySelection"?: boolean;
        /**
          * This property allows developers to specify a custom function or property name for comparing objects when determining the selected option in the ion-radio-group. When not specified, the default behavior will use strict equality (===) for comparison.
         */
        "compareWith"?: string | RadioGroupCompareFn | null;
        /**
          * The name of the control, which is submitted with the form data.
         */
        "name"?: string;
        /**
          * Emitted when the value has changed.
         */
        "onIonChange"?: (event: IonRadioGroupCustomEvent<RadioGroupChangeEventDetail>) => void;
        /**
          * the value of the radio group.
         */
        "value"?: any | null;
    }
    interface IonRange {
        /**
          * The start position of the range active bar. This feature is only available with a single knob (dualKnobs="false"). Valid values are greater than or equal to the min value and less than or equal to the max value.
         */
        "activeBarStart"?: number;
        /**
          * The color to use from your application's color palette. Default options are: `"primary"`, `"secondary"`, `"tertiary"`, `"success"`, `"warning"`, `"danger"`, `"light"`, `"medium"`, and `"dark"`. For more information on colors, see [theming](/docs/theming/basics).
         */
        "color"?: Color;
        /**
          * How long, in milliseconds, to wait to trigger the `ionInput` event after each change in the range value.
         */
        "debounce"?: number;
        /**
          * If `true`, the user cannot interact with the range.
         */
        "disabled"?: boolean;
        /**
          * Show two knobs.
         */
        "dualKnobs"?: boolean;
        /**
          * The text to display as the control's label. Use this over the `label` slot if you only need plain text. The `label` property will take priority over the `label` slot if both are used.
         */
        "label"?: string;
        /**
          * Where to place the label relative to the range. `"start"`: The label will appear to the left of the range in LTR and to the right in RTL. `"end"`: The label will appear to the right of the range in LTR and to the left in RTL. `"fixed"`: The label has the same behavior as `"start"` except it also has a fixed width. Long text will be truncated with ellipses ("..."). `"stacked"`: The label will appear above the range regardless of the direction.
         */
        "labelPlacement"?: 'start' | 'end' | 'fixed' | 'stacked';
        /**
          * Set the `legacy` property to `true` to forcibly use the legacy form control markup. Ionic will only opt components in to the modern form markup when they are using either the `aria-label` attribute or the `label` property. As a result, the `legacy` property should only be used as an escape hatch when you want to avoid this automatic opt-in behavior. Note that this property will be removed in an upcoming major release of Ionic, and all form components will be opted-in to using the modern form markup.
         */
        "legacy"?: boolean;
        /**
          * Maximum integer value of the range.
         */
        "max"?: number;
        /**
          * Minimum integer value of the range.
         */
        "min"?: number;
        /**
          * The mode determines which platform styles to use.
         */
        "mode"?: "ios" | "md";
        /**
          * The name of the control, which is submitted with the form data.
         */
        "name"?: string;
        /**
          * Emitted when the range loses focus.
         */
        "onIonBlur"?: (event: IonRangeCustomEvent<void>) => void;
        /**
          * The `ionChange` event is fired for `<ion-range>` elements when the user modifies the element's value: - When the user releases the knob after dragging; - When the user moves the knob with keyboard arrows  `ionChange` is not fired when the value is changed programmatically.
         */
        "onIonChange"?: (event: IonRangeCustomEvent<RangeChangeEventDetail>) => void;
        /**
          * Emitted when the range has focus.
         */
        "onIonFocus"?: (event: IonRangeCustomEvent<void>) => void;
        /**
          * The `ionInput` event is fired for `<ion-range>` elements when the value is modified. Unlike `ionChange`, `ionInput` is fired continuously while the user is dragging the knob.
         */
        "onIonInput"?: (event: IonRangeCustomEvent<RangeChangeEventDetail>) => void;
        /**
          * Emitted when the user finishes moving the range knob, whether through mouse drag, touch gesture, or keyboard interaction.
         */
        "onIonKnobMoveEnd"?: (event: IonRangeCustomEvent<RangeKnobMoveEndEventDetail>) => void;
        /**
          * Emitted when the user starts moving the range knob, whether through mouse drag, touch gesture, or keyboard interaction.
         */
        "onIonKnobMoveStart"?: (event: IonRangeCustomEvent<RangeKnobMoveStartEventDetail>) => void;
        /**
          * If `true`, a pin with integer value is shown when the knob is pressed.
         */
        "pin"?: boolean;
        /**
          * A callback used to format the pin text. By default the pin text is set to `Math.round(value)`.  See https://ionicframework.com/docs/troubleshooting/runtime#accessing-this if you need to access `this` from within the callback.
         */
        "pinFormatter"?: PinFormatter;
        /**
          * If `true`, the knob snaps to tick marks evenly spaced based on the step property value.
         */
        "snaps"?: boolean;
        /**
          * Specifies the value granularity.
         */
        "step"?: number;
        /**
          * If `true`, tick marks are displayed based on the step value. Only applies when `snaps` is `true`.
         */
        "ticks"?: boolean;
        /**
          * the value of the range.
         */
        "value"?: RangeValue;
    }
    interface IonRefresher {
        /**
          * Time it takes to close the refresher. Does not apply when the refresher content uses a spinner, enabling the native refresher.
         */
        "closeDuration"?: string;
        /**
          * If `true`, the refresher will be hidden.
         */
        "disabled"?: boolean;
        /**
          * The mode determines which platform styles to use.
         */
        "mode"?: "ios" | "md";
        /**
          * Emitted while the user is pulling down the content and exposing the refresher.
         */
        "onIonPull"?: (event: IonRefresherCustomEvent<void>) => void;
        /**
          * Emitted when the user lets go of the content and has pulled down further than the `pullMin` or pulls the content down and exceeds the pullMax. Updates the refresher state to `refreshing`. The `complete()` method should be called when the async operation has completed.
         */
        "onIonRefresh"?: (event: IonRefresherCustomEvent<RefresherEventDetail>) => void;
        /**
          * Emitted when the user begins to start pulling down.
         */
        "onIonStart"?: (event: IonRefresherCustomEvent<void>) => void;
        /**
          * How much to multiply the pull speed by. To slow the pull animation down, pass a number less than `1`. To speed up the pull, pass a number greater than `1`. The default value is `1` which is equal to the speed of the cursor. If a negative value is passed in, the factor will be `1` instead.  For example: If the value passed is `1.2` and the content is dragged by `10` pixels, instead of `10` pixels the content will be pulled by `12` pixels (an increase of 20 percent). If the value passed is `0.8`, the dragged amount will be `8` pixels, less than the amount the cursor has moved.  Does not apply when the refresher content uses a spinner, enabling the native refresher.
         */
        "pullFactor"?: number;
        /**
          * The maximum distance of the pull until the refresher will automatically go into the `refreshing` state. Defaults to the result of `pullMin + 60`. Does not apply when  the refresher content uses a spinner, enabling the native refresher.
         */
        "pullMax"?: number;
        /**
          * The minimum distance the user must pull down until the refresher will go into the `refreshing` state. Does not apply when the refresher content uses a spinner, enabling the native refresher.
         */
        "pullMin"?: number;
        /**
          * Time it takes the refresher to snap back to the `refreshing` state. Does not apply when the refresher content uses a spinner, enabling the native refresher.
         */
        "snapbackDuration"?: string;
    }
    interface IonRefresherContent {
        /**
          * A static icon or a spinner to display when you begin to pull down. A spinner name can be provided to gradually show tick marks when pulling down on iOS devices.
         */
        "pullingIcon"?: SpinnerTypes | string | null;
        /**
          * The text you want to display when you begin to pull down. `pullingText` can accept either plaintext or HTML as a string. To display characters normally reserved for HTML, they must be escaped. For example `<Ionic>` would become `&lt;Ionic&gt;`  For more information: [Security Documentation](https://ionicframework.com/docs/faq/security)  Content is parsed as plaintext by default. `innerHTMLTemplatesEnabled` must be set to `true` in the Ionic config before custom HTML can be used.
         */
        "pullingText"?: string | IonicSafeString;
        /**
          * An animated SVG spinner that shows when refreshing begins
         */
        "refreshingSpinner"?: SpinnerTypes | null;
        /**
          * The text you want to display when performing a refresh. `refreshingText` can accept either plaintext or HTML as a string. To display characters normally reserved for HTML, they must be escaped. For example `<Ionic>` would become `&lt;Ionic&gt;`  For more information: [Security Documentation](https://ionicframework.com/docs/faq/security)  Content is parsed as plaintext by default. `innerHTMLTemplatesEnabled` must be set to `true` in the Ionic config before custom HTML can be used.
         */
        "refreshingText"?: string | IonicSafeString;
    }
    interface IonReorder {
    }
    interface IonReorderGroup {
        /**
          * If `true`, the reorder will be hidden.
         */
        "disabled"?: boolean;
        /**
          * Event that needs to be listened to in order to complete the reorder action. Once the event has been emitted, the `complete()` method then needs to be called in order to finalize the reorder action.
         */
        "onIonItemReorder"?: (event: IonReorderGroupCustomEvent<ItemReorderEventDetail>) => void;
    }
    interface IonRippleEffect {
        /**
          * Sets the type of ripple-effect:  - `bounded`: the ripple effect expands from the user's click position - `unbounded`: the ripple effect expands from the center of the button and overflows the container.  NOTE: Surfaces for bounded ripples should have the overflow property set to hidden, while surfaces for unbounded ripples should have it set to visible.
         */
        "type"?: 'bounded' | 'unbounded';
    }
    interface IonRoute {
        /**
          * A navigation hook that is fired when the route tries to enter. Returning `true` allows the navigation to proceed, while returning `false` causes it to be cancelled. Returning a `NavigationHookOptions` object causes the router to redirect to the path specified.
         */
        "beforeEnter"?: NavigationHookCallback;
        /**
          * A navigation hook that is fired when the route tries to leave. Returning `true` allows the navigation to proceed, while returning `false` causes it to be cancelled. Returning a `NavigationHookOptions` object causes the router to redirect to the path specified.
         */
        "beforeLeave"?: NavigationHookCallback;
        /**
          * Name of the component to load/select in the navigation outlet (`ion-tabs`, `ion-nav`) when the route matches.  The value of this property is not always the tagname of the component to load, in `ion-tabs` it actually refers to the name of the `ion-tab` to select.
         */
        "component": string;
        /**
          * A key value `{ 'red': true, 'blue': 'white'}` containing props that should be passed to the defined component when rendered.
         */
        "componentProps"?: { [key: string]: any };
        /**
          * Used internally by `ion-router` to know when this route did change.
         */
        "onIonRouteDataChanged"?: (event: IonRouteCustomEvent<any>) => void;
        /**
          * Relative path that needs to match in order for this route to apply.  Accepts paths similar to expressjs so that you can define parameters in the url /foo/:bar where bar would be available in incoming props.
         */
        "url"?: string;
    }
    interface IonRouteRedirect {
        /**
          * A redirect route, redirects "from" a URL "to" another URL. This property is that "from" URL. It needs to be an exact match of the navigated URL in order to apply.  The path specified in this value is always an absolute path, even if the initial `/` slash is not specified.
         */
        "from": string;
        /**
          * Internal event that fires when any value of this rule is added/removed from the DOM, or any of his public properties changes.  `ion-router` captures this event in order to update his internal registry of router rules.
         */
        "onIonRouteRedirectChanged"?: (event: IonRouteRedirectCustomEvent<any>) => void;
        /**
          * A redirect route, redirects "from" a URL "to" another URL. This property is that "to" URL. When the defined `ion-route-redirect` rule matches, the router will redirect to the path specified in this property.  The value of this property is always an absolute path inside the scope of routes defined in `ion-router` it can't be used with another router or to perform a redirection to a different domain.  Note that this is a virtual redirect, it will not cause a real browser refresh, again, it's a redirect inside the context of ion-router.  When this property is not specified or his value is `undefined` the whole redirect route is noop, even if the "from" value matches.
         */
        "to": string | undefined | null;
    }
    interface IonRouter {
        /**
          * Emitted when the route had changed
         */
        "onIonRouteDidChange"?: (event: IonRouterCustomEvent<RouterEventDetail>) => void;
        /**
          * Event emitted when the route is about to change
         */
        "onIonRouteWillChange"?: (event: IonRouterCustomEvent<RouterEventDetail>) => void;
        /**
          * The root path to use when matching URLs. By default, this is set to "/", but you can specify an alternate prefix for all URL paths.
         */
        "root"?: string;
        /**
          * The router can work in two "modes": - With hash: `/index.html#/path/to/page` - Without hash: `/path/to/page`  Using one or another might depend in the requirements of your app and/or where it's deployed.  Usually "hash-less" navigation works better for SEO and it's more user friendly too, but it might requires additional server-side configuration in order to properly work.  On the other side hash-navigation is much easier to deploy, it even works over the file protocol.  By default, this property is `true`, change to `false` to allow hash-less URLs.
         */
        "useHash"?: boolean;
    }
    interface IonRouterLink {
        /**
          * The color to use from your application's color palette. Default options are: `"primary"`, `"secondary"`, `"tertiary"`, `"success"`, `"warning"`, `"danger"`, `"light"`, `"medium"`, and `"dark"`. For more information on colors, see [theming](/docs/theming/basics).
         */
        "color"?: Color;
        /**
          * Contains a URL or a URL fragment that the hyperlink points to. If this property is set, an anchor tag will be rendered.
         */
        "href"?: string | undefined;
        /**
          * Specifies the relationship of the target object to the link object. The value is a space-separated list of [link types](https://developer.mozilla.org/en-US/docs/Web/HTML/Link_types).
         */
        "rel"?: string | undefined;
        /**
          * When using a router, it specifies the transition animation when navigating to another page using `href`.
         */
        "routerAnimation"?: AnimationBuilder | undefined;
        /**
          * When using a router, it specifies the transition direction when navigating to another page using `href`.
         */
        "routerDirection"?: RouterDirection;
        /**
          * Specifies where to display the linked URL. Only applies when an `href` is provided. Special keywords: `"_blank"`, `"_self"`, `"_parent"`, `"_top"`.
         */
        "target"?: string | undefined;
    }
    interface IonRouterOutlet {
        /**
          * If `true`, the router-outlet should animate the transition of components.
         */
        "animated"?: boolean;
        /**
          * This property allows to create custom transition using AnimationBuilder functions.
         */
        "animation"?: AnimationBuilder;
        /**
          * The mode determines which platform styles to use.
         */
        "mode"?: "ios" | "md";
    }
    interface IonRow {
    }
    interface IonSearchbar {
        /**
          * If `true`, enable searchbar animation.
         */
        "animated"?: boolean;
        /**
          * Indicates whether and how the text value should be automatically capitalized as it is entered/edited by the user. Available options: `"off"`, `"none"`, `"on"`, `"sentences"`, `"words"`, `"characters"`.
         */
        "autocapitalize"?: string;
        /**
          * Set the input's autocomplete property.
         */
        "autocomplete"?: AutocompleteTypes;
        /**
          * Set the input's autocorrect property.
         */
        "autocorrect"?: 'on' | 'off';
        /**
          * Set the cancel button icon. Only applies to `md` mode. Defaults to `arrow-back-sharp`.
         */
        "cancelButtonIcon"?: string;
        /**
          * Set the the cancel button text. Only applies to `ios` mode.
         */
        "cancelButtonText"?: string;
        /**
          * Set the clear icon. Defaults to `close-circle` for `ios` and `close-sharp` for `md`.
         */
        "clearIcon"?: string;
        /**
          * The color to use from your application's color palette. Default options are: `"primary"`, `"secondary"`, `"tertiary"`, `"success"`, `"warning"`, `"danger"`, `"light"`, `"medium"`, and `"dark"`. For more information on colors, see [theming](/docs/theming/basics).
         */
        "color"?: Color;
        /**
          * Set the amount of time, in milliseconds, to wait to trigger the `ionInput` event after each keystroke.
         */
        "debounce"?: number;
        /**
          * If `true`, the user cannot interact with the input.
         */
        "disabled"?: boolean;
        /**
          * A hint to the browser for which enter key to display. Possible values: `"enter"`, `"done"`, `"go"`, `"next"`, `"previous"`, `"search"`, and `"send"`.
         */
        "enterkeyhint"?: 'enter' | 'done' | 'go' | 'next' | 'previous' | 'search' | 'send';
        /**
          * A hint to the browser for which keyboard to display. Possible values: `"none"`, `"text"`, `"tel"`, `"url"`, `"email"`, `"numeric"`, `"decimal"`, and `"search"`.
         */
        "inputmode"?: 'none' | 'text' | 'tel' | 'url' | 'email' | 'numeric' | 'decimal' | 'search';
        /**
          * This attribute specifies the maximum number of characters that the user can enter.
         */
        "maxlength"?: number;
        /**
          * This attribute specifies the minimum number of characters that the user can enter.
         */
        "minlength"?: number;
        /**
          * The mode determines which platform styles to use.
         */
        "mode"?: "ios" | "md";
        /**
          * If used in a form, set the name of the control, which is submitted with the form data.
         */
        "name"?: string;
        /**
          * Emitted when the input loses focus.
         */
        "onIonBlur"?: (event: IonSearchbarCustomEvent<void>) => void;
        /**
          * Emitted when the cancel button is clicked.
         */
        "onIonCancel"?: (event: IonSearchbarCustomEvent<void>) => void;
        /**
          * The `ionChange` event is fired for `<ion-searchbar>` elements when the user modifies the element's value. Unlike the `ionInput` event, the `ionChange` event is not necessarily fired for each alteration to an element's value.  The `ionChange` event is fired when the value has been committed by the user. This can happen when the element loses focus or when the "Enter" key is pressed. `ionChange` can also fire when clicking the clear or cancel buttons.
         */
        "onIonChange"?: (event: IonSearchbarCustomEvent<SearchbarChangeEventDetail>) => void;
        /**
          * Emitted when the clear input button is clicked.
         */
        "onIonClear"?: (event: IonSearchbarCustomEvent<void>) => void;
        /**
          * Emitted when the input has focus.
         */
        "onIonFocus"?: (event: IonSearchbarCustomEvent<void>) => void;
        /**
          * Emitted when the `value` of the `ion-searchbar` element has changed.
         */
        "onIonInput"?: (event: IonSearchbarCustomEvent<SearchbarInputEventDetail>) => void;
        /**
          * Set the input's placeholder. `placeholder` can accept either plaintext or HTML as a string. To display characters normally reserved for HTML, they must be escaped. For example `<Ionic>` would become `&lt;Ionic&gt;`  For more information: [Security Documentation](https://ionicframework.com/docs/faq/security)
         */
        "placeholder"?: string;
        /**
          * The icon to use as the search icon. Defaults to `search-outline` in `ios` mode and `search-sharp` in `md` mode.
         */
        "searchIcon"?: string;
        /**
          * Sets the behavior for the cancel button. Defaults to `"never"`. Setting to `"focus"` shows the cancel button on focus. Setting to `"never"` hides the cancel button. Setting to `"always"` shows the cancel button regardless of focus state.
         */
        "showCancelButton"?: 'never' | 'focus' | 'always';
        /**
          * Sets the behavior for the clear button. Defaults to `"focus"`. Setting to `"focus"` shows the clear button on focus if the input is not empty. Setting to `"never"` hides the clear button. Setting to `"always"` shows the clear button regardless of focus state, but only if the input is not empty.
         */
        "showClearButton"?: 'never' | 'focus' | 'always';
        /**
          * If `true`, enable spellcheck on the input.
         */
        "spellcheck"?: boolean;
        /**
          * Set the type of the input.
         */
        "type"?: 'text' | 'password' | 'email' | 'number' | 'search' | 'tel' | 'url';
        /**
          * the value of the searchbar.
         */
        "value"?: string | null;
    }
    interface IonSegment {
        /**
          * The color to use from your application's color palette. Default options are: `"primary"`, `"secondary"`, `"tertiary"`, `"success"`, `"warning"`, `"danger"`, `"light"`, `"medium"`, and `"dark"`. For more information on colors, see [theming](/docs/theming/basics).
         */
        "color"?: Color;
        /**
          * If `true`, the user cannot interact with the segment.
         */
        "disabled"?: boolean;
        /**
          * The mode determines which platform styles to use.
         */
        "mode"?: "ios" | "md";
        /**
          * Emitted when the value property has changed and any dragging pointer has been released from `ion-segment`.
         */
        "onIonChange"?: (event: IonSegmentCustomEvent<SegmentChangeEventDetail>) => void;
        /**
          * If `true`, the segment buttons will overflow and the user can swipe to see them. In addition, this will disable the gesture to drag the indicator between the buttons in order to swipe to see hidden buttons.
         */
        "scrollable"?: boolean;
        /**
          * If `true`, navigating to an `ion-segment-button` with the keyboard will focus and select the element. If `false`, keyboard navigation will only focus the `ion-segment-button` element.
         */
        "selectOnFocus"?: boolean;
        /**
          * If `true`, users will be able to swipe between segment buttons to activate them.
         */
        "swipeGesture"?: boolean;
        /**
          * the value of the segment.
         */
        "value"?: SegmentValue;
    }
    interface IonSegmentButton {
        /**
          * If `true`, the user cannot interact with the segment button.
         */
        "disabled"?: boolean;
        /**
          * Set the layout of the text and icon in the segment.
         */
        "layout"?: SegmentButtonLayout;
        /**
          * The mode determines which platform styles to use.
         */
        "mode"?: "ios" | "md";
        /**
          * The type of the button.
         */
        "type"?: 'submit' | 'reset' | 'button';
        /**
          * The value of the segment button.
         */
        "value"?: SegmentValue;
    }
    interface IonSelect {
        /**
          * The text to display on the cancel button.
         */
        "cancelText"?: string;
        /**
          * The color to use from your application's color palette. Default options are: `"primary"`, `"secondary"`, `"tertiary"`, `"success"`, `"warning"`, `"danger"`, `"light"`, `"medium"`, and `"dark"`. For more information on colors, see [theming](/docs/theming/basics).  This property is only available when using the modern select syntax.
         */
        "color"?: Color;
        /**
          * This property allows developers to specify a custom function or property name for comparing objects when determining the selected option in the ion-select. When not specified, the default behavior will use strict equality (===) for comparison.
         */
        "compareWith"?: string | SelectCompareFn | null;
        /**
          * If `true`, the user cannot interact with the select.
         */
        "disabled"?: boolean;
        /**
          * The toggle icon to show when the select is open. If defined, the icon rotation behavior in `md` mode will be disabled. If undefined, `toggleIcon` will be used for when the select is both open and closed.
         */
        "expandedIcon"?: string;
        /**
          * The fill for the item. If `"solid"` the item will have a background. If `"outline"` the item will be transparent with a border. Only available in `md` mode.
         */
        "fill"?: 'outline' | 'solid';
        /**
          * The interface the select should use: `action-sheet`, `popover` or `alert`.
         */
        "interface"?: SelectInterface;
        /**
          * Any additional options that the `alert`, `action-sheet` or `popover` interface can take. See the [ion-alert docs](./alert), the [ion-action-sheet docs](./action-sheet) and the [ion-popover docs](./popover) for the create options for each interface.  Note: `interfaceOptions` will not override `inputs` or `buttons` with the `alert` interface.
         */
        "interfaceOptions"?: any;
        /**
          * How to pack the label and select within a line. `justify` does not apply when the label and select are on different lines when `labelPlacement` is set to `"floating"` or `"stacked"`. `"start"`: The label and select will appear on the left in LTR and on the right in RTL. `"end"`: The label and select will appear on the right in LTR and on the left in RTL. `"space-between"`: The label and select will appear on opposite ends of the line with space between the two elements.
         */
        "justify"?: 'start' | 'end' | 'space-between';
        /**
          * The visible label associated with the select.  Use this if you need to render a plaintext label.  The `label` property will take priority over the `label` slot if both are used.
         */
        "label"?: string;
        /**
          * Where to place the label relative to the select. `"start"`: The label will appear to the left of the select in LTR and to the right in RTL. `"end"`: The label will appear to the right of the select in LTR and to the left in RTL. `"floating"`: The label will appear smaller and above the select when the select is focused or it has a value. Otherwise it will appear on top of the select. `"stacked"`: The label will appear smaller and above the select regardless even when the select is blurred or has no value. `"fixed"`: The label has the same behavior as `"start"` except it also has a fixed width. Long text will be truncated with ellipses ("..."). When using `"floating"` or `"stacked"` we recommend initializing the select with either a `value` or a `placeholder`.
         */
        "labelPlacement"?: 'start' | 'end' | 'floating' | 'stacked' | 'fixed';
        /**
          * Set the `legacy` property to `true` to forcibly use the legacy form control markup. Ionic will only opt components in to the modern form markup when they are using either the `aria-label` attribute or the `label` property. As a result, the `legacy` property should only be used as an escape hatch when you want to avoid this automatic opt-in behavior. Note that this property will be removed in an upcoming major release of Ionic, and all form components will be opted-in to using the modern form markup.
         */
        "legacy"?: boolean;
        /**
          * The mode determines which platform styles to use.
         */
        "mode"?: "ios" | "md";
        /**
          * If `true`, the select can accept multiple values.
         */
        "multiple"?: boolean;
        /**
          * The name of the control, which is submitted with the form data.
         */
        "name"?: string;
        /**
          * The text to display on the ok button.
         */
        "okText"?: string;
        /**
          * Emitted when the select loses focus.
         */
        "onIonBlur"?: (event: IonSelectCustomEvent<void>) => void;
        /**
          * Emitted when the selection is cancelled.
         */
        "onIonCancel"?: (event: IonSelectCustomEvent<void>) => void;
        /**
          * Emitted when the value has changed.
         */
        "onIonChange"?: (event: IonSelectCustomEvent<SelectChangeEventDetail>) => void;
        /**
          * Emitted when the overlay is dismissed.
         */
        "onIonDismiss"?: (event: IonSelectCustomEvent<void>) => void;
        /**
          * Emitted when the select has focus.
         */
        "onIonFocus"?: (event: IonSelectCustomEvent<void>) => void;
        /**
          * The text to display when the select is empty.
         */
        "placeholder"?: string;
        /**
          * The text to display instead of the selected option's value.
         */
        "selectedText"?: string | null;
        /**
          * The shape of the select. If "round" it will have an increased border radius.
         */
        "shape"?: 'round';
        /**
          * The toggle icon to use. Defaults to `chevronExpand` for `ios` mode, or `caretDownSharp` for `md` mode.
         */
        "toggleIcon"?: string;
        /**
          * The value of the select.
         */
        "value"?: any | null;
    }
    interface IonSelectOption {
        /**
          * If `true`, the user cannot interact with the select option. This property does not apply when `interface="action-sheet"` as `ion-action-sheet` does not allow for disabled buttons.
         */
        "disabled"?: boolean;
        /**
          * The text value of the option.
         */
        "value"?: any | null;
    }
    interface IonSelectPopover {
        /**
          * The header text of the popover
         */
        "header"?: string;
        /**
          * The text content of the popover body
         */
        "message"?: string;
        /**
          * If true, the select accepts multiple values
         */
        "multiple"?: boolean;
        /**
          * An array of options for the popover
         */
        "options"?: SelectPopoverOption[];
        /**
          * The subheader text of the popover
         */
        "subHeader"?: string;
    }
    interface IonSkeletonText {
        /**
          * If `true`, the skeleton text will animate.
         */
        "animated"?: boolean;
    }
    interface IonSpinner {
        /**
          * The color to use from your application's color palette. Default options are: `"primary"`, `"secondary"`, `"tertiary"`, `"success"`, `"warning"`, `"danger"`, `"light"`, `"medium"`, and `"dark"`. For more information on colors, see [theming](/docs/theming/basics).
         */
        "color"?: Color;
        /**
          * Duration of the spinner animation in milliseconds. The default varies based on the spinner.
         */
        "duration"?: number;
        /**
          * The name of the SVG spinner to use. If a name is not provided, the platform's default spinner will be used.
         */
        "name"?: SpinnerTypes;
        /**
          * If `true`, the spinner's animation will be paused.
         */
        "paused"?: boolean;
    }
    interface IonSplitPane {
        /**
          * The `id` of the main content. When using a router this is typically `ion-router-outlet`. When not using a router, this is typically your main view's `ion-content`. This is not the id of the `ion-content` inside of your `ion-menu`.
         */
        "contentId"?: string;
        /**
          * If `true`, the split pane will be hidden.
         */
        "disabled"?: boolean;
        /**
          * Expression to be called when the split-pane visibility has changed
         */
        "onIonSplitPaneVisible"?: (event: IonSplitPaneCustomEvent<{ visible: boolean }>) => void;
        /**
          * When the split-pane should be shown. Can be a CSS media query expression, or a shortcut expression. Can also be a boolean expression.
         */
        "when"?: string | boolean;
    }
    interface IonTab {
        /**
          * The component to display inside of the tab.
         */
        "component"?: ComponentRef;
        /**
          * A tab id must be provided for each `ion-tab`. It's used internally to reference the selected tab or by the router to switch between them.
         */
        "tab": string;
    }
    interface IonTabBar {
        /**
          * The color to use from your application's color palette. Default options are: `"primary"`, `"secondary"`, `"tertiary"`, `"success"`, `"warning"`, `"danger"`, `"light"`, `"medium"`, and `"dark"`. For more information on colors, see [theming](/docs/theming/basics).
         */
        "color"?: Color;
        /**
          * The mode determines which platform styles to use.
         */
        "mode"?: "ios" | "md";
        /**
          * The selected tab component
         */
        "selectedTab"?: string;
        /**
          * If `true`, the tab bar will be translucent. Only applies when the mode is `"ios"` and the device supports [`backdrop-filter`](https://developer.mozilla.org/en-US/docs/Web/CSS/backdrop-filter#Browser_compatibility).
         */
        "translucent"?: boolean;
    }
    interface IonTabButton {
        /**
          * If `true`, the user cannot interact with the tab button.
         */
        "disabled"?: boolean;
        /**
          * This attribute instructs browsers to download a URL instead of navigating to it, so the user will be prompted to save it as a local file. If the attribute has a value, it is used as the pre-filled file name in the Save prompt (the user can still change the file name if they want).
         */
        "download"?: string | undefined;
        /**
          * Contains a URL or a URL fragment that the hyperlink points to. If this property is set, an anchor tag will be rendered.
         */
        "href"?: string | undefined;
        /**
          * Set the layout of the text and icon in the tab bar. It defaults to `"icon-top"`.
         */
        "layout"?: TabButtonLayout;
        /**
          * The mode determines which platform styles to use.
         */
        "mode"?: "ios" | "md";
        /**
          * Specifies the relationship of the target object to the link object. The value is a space-separated list of [link types](https://developer.mozilla.org/en-US/docs/Web/HTML/Link_types).
         */
        "rel"?: string | undefined;
        /**
          * The selected tab component
         */
        "selected"?: boolean;
        /**
          * A tab id must be provided for each `ion-tab`. It's used internally to reference the selected tab or by the router to switch between them.
         */
        "tab"?: string;
        /**
          * Specifies where to display the linked URL. Only applies when an `href` is provided. Special keywords: `"_blank"`, `"_self"`, `"_parent"`, `"_top"`.
         */
        "target"?: string | undefined;
    }
    interface IonTabs {
        /**
          * Emitted when the navigation has finished transitioning to a new component.
         */
        "onIonTabsDidChange"?: (event: IonTabsCustomEvent<{ tab: string }>) => void;
        /**
          * Emitted when the navigation is about to transition to a new component.
         */
        "onIonTabsWillChange"?: (event: IonTabsCustomEvent<{ tab: string }>) => void;
    }
    interface IonText {
        /**
          * The color to use from your application's color palette. Default options are: `"primary"`, `"secondary"`, `"tertiary"`, `"success"`, `"warning"`, `"danger"`, `"light"`, `"medium"`, and `"dark"`. For more information on colors, see [theming](/docs/theming/basics).
         */
        "color"?: Color;
        /**
          * The mode determines which platform styles to use.
         */
        "mode"?: "ios" | "md";
    }
    interface IonTextarea {
        /**
          * If `true`, the textarea container will grow and shrink based on the contents of the textarea.
         */
        "autoGrow"?: boolean;
        /**
          * Indicates whether and how the text value should be automatically capitalized as it is entered/edited by the user. Available options: `"off"`, `"none"`, `"on"`, `"sentences"`, `"words"`, `"characters"`.
         */
        "autocapitalize"?: string;
        /**
          * Sets the [`autofocus` attribute](https://developer.mozilla.org/en-US/docs/Web/HTML/Global_attributes/autofocus) on the native input element.  This may not be sufficient for the element to be focused on page load. See [managing focus](/docs/developing/managing-focus) for more information.
         */
        "autofocus"?: boolean;
        /**
          * If `true`, the value will be cleared after focus upon edit.
         */
        "clearOnEdit"?: boolean;
        /**
          * The color to use from your application's color palette. Default options are: `"primary"`, `"secondary"`, `"tertiary"`, `"success"`, `"warning"`, `"danger"`, `"light"`, `"medium"`, and `"dark"`. For more information on colors, see [theming](/docs/theming/basics).
         */
        "color"?: Color;
        /**
          * The visible width of the text control, in average character widths. If it is specified, it must be a positive integer.
         */
        "cols"?: number;
        /**
          * If `true`, a character counter will display the ratio of characters used and the total character limit. Developers must also set the `maxlength` property for the counter to be calculated correctly.
         */
        "counter"?: boolean;
        /**
          * A callback used to format the counter text. By default the counter text is set to "itemLength / maxLength".  See https://ionicframework.com/docs/troubleshooting/runtime#accessing-this if you need to access `this` from within the callback.
         */
        "counterFormatter"?: (inputLength: number, maxLength: number) => string;
        /**
          * Set the amount of time, in milliseconds, to wait to trigger the `ionInput` event after each keystroke.
         */
        "debounce"?: number;
        /**
          * If `true`, the user cannot interact with the textarea.
         */
        "disabled"?: boolean;
        /**
          * A hint to the browser for which enter key to display. Possible values: `"enter"`, `"done"`, `"go"`, `"next"`, `"previous"`, `"search"`, and `"send"`.
         */
        "enterkeyhint"?: 'enter' | 'done' | 'go' | 'next' | 'previous' | 'search' | 'send';
        /**
          * Text that is placed under the textarea and displayed when an error is detected.
         */
        "errorText"?: string;
        /**
          * The fill for the item. If `"solid"` the item will have a background. If `"outline"` the item will be transparent with a border. Only available in `md` mode.
         */
        "fill"?: 'outline' | 'solid';
        /**
          * Text that is placed under the textarea and displayed when no error is detected.
         */
        "helperText"?: string;
        /**
          * A hint to the browser for which keyboard to display. Possible values: `"none"`, `"text"`, `"tel"`, `"url"`, `"email"`, `"numeric"`, `"decimal"`, and `"search"`.
         */
        "inputmode"?: 'none' | 'text' | 'tel' | 'url' | 'email' | 'numeric' | 'decimal' | 'search';
        /**
          * The visible label associated with the textarea.  Use this if you need to render a plaintext label.  The `label` property will take priority over the `label` slot if both are used.
         */
        "label"?: string;
        /**
          * Where to place the label relative to the textarea. `"start"`: The label will appear to the left of the textarea in LTR and to the right in RTL. `"end"`: The label will appear to the right of the textarea in LTR and to the left in RTL. `"floating"`: The label will appear smaller and above the textarea when the textarea is focused or it has a value. Otherwise it will appear on top of the textarea. `"stacked"`: The label will appear smaller and above the textarea regardless even when the textarea is blurred or has no value. `"fixed"`: The label has the same behavior as `"start"` except it also has a fixed width. Long text will be truncated with ellipses ("...").
         */
        "labelPlacement"?: 'start' | 'end' | 'floating' | 'stacked' | 'fixed';
        /**
          * Set the `legacy` property to `true` to forcibly use the legacy form control markup. Ionic will only opt components in to the modern form markup when they are using either the `aria-label` attribute or the default slot that contains the label text. As a result, the `legacy` property should only be used as an escape hatch when you want to avoid this automatic opt-in behavior. Note that this property will be removed in an upcoming major release of Ionic, and all form components will be opted-in to using the modern form markup.
         */
        "legacy"?: boolean;
        /**
          * This attribute specifies the maximum number of characters that the user can enter.
         */
        "maxlength"?: number;
        /**
          * This attribute specifies the minimum number of characters that the user can enter.
         */
        "minlength"?: number;
        /**
          * The mode determines which platform styles to use.
         */
        "mode"?: "ios" | "md";
        /**
          * The name of the control, which is submitted with the form data.
         */
        "name"?: string;
        /**
          * Emitted when the input loses focus.
         */
        "onIonBlur"?: (event: IonTextareaCustomEvent<FocusEvent>) => void;
        /**
          * The `ionChange` event is fired when the user modifies the textarea's value. Unlike the `ionInput` event, the `ionChange` event is fired when the element loses focus after its value has been modified.
         */
        "onIonChange"?: (event: IonTextareaCustomEvent<TextareaChangeEventDetail>) => void;
        /**
          * Emitted when the input has focus.
         */
        "onIonFocus"?: (event: IonTextareaCustomEvent<FocusEvent>) => void;
        /**
          * The `ionInput` event is fired each time the user modifies the textarea's value. Unlike the `ionChange` event, the `ionInput` event is fired for each alteration to the textarea's value. This typically happens for each keystroke as the user types.  When `clearOnEdit` is enabled, the `ionInput` event will be fired when the user clears the textarea by performing a keydown event.
         */
        "onIonInput"?: (event: IonTextareaCustomEvent<TextareaInputEventDetail>) => void;
        /**
          * Instructional text that shows before the input has a value.
         */
        "placeholder"?: string;
        /**
          * If `true`, the user cannot modify the value.
         */
        "readonly"?: boolean;
        /**
          * If `true`, the user must fill in a value before submitting a form.
         */
        "required"?: boolean;
        /**
          * The number of visible text lines for the control.
         */
        "rows"?: number;
        /**
          * The shape of the textarea. If "round" it will have an increased border radius.
         */
        "shape"?: 'round';
        /**
          * If `true`, the element will have its spelling and grammar checked.
         */
        "spellcheck"?: boolean;
        /**
          * The value of the textarea.
         */
        "value"?: string | null;
        /**
          * Indicates how the control wraps text.
         */
        "wrap"?: 'hard' | 'soft' | 'off';
    }
    interface IonThumbnail {
    }
    interface IonTitle {
        /**
          * The color to use from your application's color palette. Default options are: `"primary"`, `"secondary"`, `"tertiary"`, `"success"`, `"warning"`, `"danger"`, `"light"`, `"medium"`, and `"dark"`. For more information on colors, see [theming](/docs/theming/basics).
         */
        "color"?: Color;
        /**
          * The size of the toolbar title.
         */
        "size"?: 'large' | 'small';
    }
    interface IonToast {
        /**
          * If `true`, the toast will animate.
         */
        "animated"?: boolean;
        /**
          * An array of buttons for the toast.
         */
        "buttons"?: (ToastButton | string)[];
        /**
          * The color to use from your application's color palette. Default options are: `"primary"`, `"secondary"`, `"tertiary"`, `"success"`, `"warning"`, `"danger"`, `"light"`, `"medium"`, and `"dark"`. For more information on colors, see [theming](/docs/theming/basics).
         */
        "color"?: Color;
        /**
          * Additional classes to apply for custom CSS. If multiple classes are provided they should be separated by spaces.
         */
        "cssClass"?: string | string[];
        /**
          * How many milliseconds to wait before hiding the toast. By default, it will show until `dismiss()` is called.
         */
        "duration"?: number;
        /**
          * Animation to use when the toast is presented.
         */
        "enterAnimation"?: AnimationBuilder;
        /**
          * Header to be shown in the toast.
         */
        "header"?: string;
        /**
          * Additional attributes to pass to the toast.
         */
        "htmlAttributes"?: { [key: string]: any };
        /**
          * The name of the icon to display, or the path to a valid SVG file. See `ion-icon`. https://ionic.io/ionicons
         */
        "icon"?: string;
        /**
          * If `true`, the toast will open. If `false`, the toast will close. Use this if you need finer grained control over presentation, otherwise just use the toastController or the `trigger` property. Note: `isOpen` will not automatically be set back to `false` when the toast dismisses. You will need to do that in your code.
         */
        "isOpen"?: boolean;
        /**
          * If `true`, the keyboard will be automatically dismissed when the overlay is presented.
         */
        "keyboardClose"?: boolean;
        /**
          * Defines how the message and buttons are laid out in the toast. 'baseline': The message and the buttons will appear on the same line. Message text may wrap within the message container. 'stacked': The buttons containers and message will stack on top of each other. Use this if you have long text in your buttons.
         */
        "layout"?: ToastLayout;
        /**
          * Animation to use when the toast is dismissed.
         */
        "leaveAnimation"?: AnimationBuilder;
        /**
          * Message to be shown in the toast. This property accepts custom HTML as a string. Content is parsed as plaintext by default. `innerHTMLTemplatesEnabled` must be set to `true` in the Ionic config before custom HTML can be used.
         */
        "message"?: string | IonicSafeString;
        /**
          * The mode determines which platform styles to use.
         */
        "mode"?: "ios" | "md";
        /**
          * Emitted after the toast has dismissed. Shorthand for ionToastDidDismiss.
         */
        "onDidDismiss"?: (event: IonToastCustomEvent<OverlayEventDetail>) => void;
        /**
          * Emitted after the toast has presented. Shorthand for ionToastWillDismiss.
         */
        "onDidPresent"?: (event: IonToastCustomEvent<void>) => void;
        /**
          * Emitted after the toast has dismissed.
         */
        "onIonToastDidDismiss"?: (event: IonToastCustomEvent<OverlayEventDetail>) => void;
        /**
          * Emitted after the toast has presented.
         */
        "onIonToastDidPresent"?: (event: IonToastCustomEvent<void>) => void;
        /**
          * Emitted before the toast has dismissed.
         */
        "onIonToastWillDismiss"?: (event: IonToastCustomEvent<OverlayEventDetail>) => void;
        /**
          * Emitted before the toast has presented.
         */
        "onIonToastWillPresent"?: (event: IonToastCustomEvent<void>) => void;
        /**
          * Emitted before the toast has dismissed. Shorthand for ionToastWillDismiss.
         */
        "onWillDismiss"?: (event: IonToastCustomEvent<OverlayEventDetail>) => void;
        /**
          * Emitted before the toast has presented. Shorthand for ionToastWillPresent.
         */
        "onWillPresent"?: (event: IonToastCustomEvent<void>) => void;
        /**
          * The starting position of the toast on the screen. Can be tweaked further using the `positionAnchor` property.
         */
        "position"?: ToastPosition;
        /**
          * The element to anchor the toast's position to. Can be set as a direct reference or the ID of the element. With `position="bottom"`, the toast will sit above the chosen element. With `position="top"`, the toast will sit below the chosen element. With `position="middle"`, the value of `positionAnchor` is ignored.
         */
        "positionAnchor"?: HTMLElement | string;
        /**
          * If set to 'vertical', the Toast can be dismissed with a swipe gesture. The swipe direction is determined by the value of the `position` property: `top`: The Toast can be swiped up to dismiss. `bottom`: The Toast can be swiped down to dismiss. `middle`: The Toast can be swiped up or down to dismiss.
         */
        "swipeGesture"?: ToastSwipeGestureDirection;
        /**
          * If `true`, the toast will be translucent. Only applies when the mode is `"ios"` and the device supports [`backdrop-filter`](https://developer.mozilla.org/en-US/docs/Web/CSS/backdrop-filter#Browser_compatibility).
         */
        "translucent"?: boolean;
        /**
          * An ID corresponding to the trigger element that causes the toast to open when clicked.
         */
        "trigger"?: string | undefined;
    }
    interface IonToggle {
        /**
          * How to control the alignment of the toggle and label on the cross axis. `"start"`: The label and control will appear on the left of the cross axis in LTR, and on the right side in RTL. `"center"`: The label and control will appear at the center of the cross axis in both LTR and RTL.
         */
        "alignment"?: 'start' | 'center';
        /**
          * If `true`, the toggle is selected.
         */
        "checked"?: boolean;
        /**
          * The color to use from your application's color palette. Default options are: `"primary"`, `"secondary"`, `"tertiary"`, `"success"`, `"warning"`, `"danger"`, `"light"`, `"medium"`, and `"dark"`. For more information on colors, see [theming](/docs/theming/basics).
         */
        "color"?: Color;
        /**
          * If `true`, the user cannot interact with the toggle.
         */
        "disabled"?: boolean;
        /**
          * Enables the on/off accessibility switch labels within the toggle.
         */
        "enableOnOffLabels"?: boolean | undefined;
        /**
          * How to pack the label and toggle within a line. `"start"`: The label and toggle will appear on the left in LTR and on the right in RTL. `"end"`: The label and toggle will appear on the right in LTR and on the left in RTL. `"space-between"`: The label and toggle will appear on opposite ends of the line with space between the two elements.
         */
        "justify"?: 'start' | 'end' | 'space-between';
        /**
          * Where to place the label relative to the input. `"start"`: The label will appear to the left of the toggle in LTR and to the right in RTL. `"end"`: The label will appear to the right of the toggle in LTR and to the left in RTL. `"fixed"`: The label has the same behavior as `"start"` except it also has a fixed width. Long text will be truncated with ellipses ("..."). `"stacked"`: The label will appear above the toggle regardless of the direction. The alignment of the label can be controlled with the `alignment` property.
         */
        "labelPlacement"?: 'start' | 'end' | 'fixed' | 'stacked';
        /**
          * Set the `legacy` property to `true` to forcibly use the legacy form control markup. Ionic will only opt components in to the modern form markup when they are using either the `aria-label` attribute or the default slot that contains the label text. As a result, the `legacy` property should only be used as an escape hatch when you want to avoid this automatic opt-in behavior. Note that this property will be removed in an upcoming major release of Ionic, and all form components will be opted-in to using the modern form markup.
         */
        "legacy"?: boolean;
        /**
          * The mode determines which platform styles to use.
         */
        "mode"?: "ios" | "md";
        /**
          * The name of the control, which is submitted with the form data.
         */
        "name"?: string;
        /**
          * Emitted when the toggle loses focus.
         */
        "onIonBlur"?: (event: IonToggleCustomEvent<void>) => void;
        /**
          * Emitted when the user switches the toggle on or off. Does not emit when programmatically changing the value of the `checked` property.
         */
        "onIonChange"?: (event: IonToggleCustomEvent<ToggleChangeEventDetail>) => void;
        /**
          * Emitted when the toggle has focus.
         */
        "onIonFocus"?: (event: IonToggleCustomEvent<void>) => void;
        /**
          * The value of the toggle does not mean if it's checked or not, use the `checked` property for that.  The value of a toggle is analogous to the value of a `<input type="checkbox">`, it's only used when the toggle participates in a native `<form>`.
         */
        "value"?: string | null;
    }
    interface IonToolbar {
        /**
          * The color to use from your application's color palette. Default options are: `"primary"`, `"secondary"`, `"tertiary"`, `"success"`, `"warning"`, `"danger"`, `"light"`, `"medium"`, and `"dark"`. For more information on colors, see [theming](/docs/theming/basics).
         */
        "color"?: Color;
        /**
          * The mode determines which platform styles to use.
         */
        "mode"?: "ios" | "md";
    }
    interface IntrinsicElements {
        "ion-accordion": IonAccordion;
        "ion-accordion-group": IonAccordionGroup;
        "ion-action-sheet": IonActionSheet;
        "ion-alert": IonAlert;
        "ion-app": IonApp;
        "ion-avatar": IonAvatar;
        "ion-back-button": IonBackButton;
        "ion-backdrop": IonBackdrop;
        "ion-badge": IonBadge;
        "ion-breadcrumb": IonBreadcrumb;
        "ion-breadcrumbs": IonBreadcrumbs;
        "ion-button": IonButton;
        "ion-buttons": IonButtons;
        "ion-card": IonCard;
        "ion-card-content": IonCardContent;
        "ion-card-header": IonCardHeader;
        "ion-card-subtitle": IonCardSubtitle;
        "ion-card-title": IonCardTitle;
        "ion-checkbox": IonCheckbox;
        "ion-chip": IonChip;
        "ion-col": IonCol;
        "ion-content": IonContent;
        "ion-datetime": IonDatetime;
        "ion-datetime-button": IonDatetimeButton;
        "ion-fab": IonFab;
        "ion-fab-button": IonFabButton;
        "ion-fab-list": IonFabList;
        "ion-footer": IonFooter;
        "ion-grid": IonGrid;
        "ion-header": IonHeader;
        "ion-img": IonImg;
        "ion-infinite-scroll": IonInfiniteScroll;
        "ion-infinite-scroll-content": IonInfiniteScrollContent;
        "ion-input": IonInput;
        "ion-item": IonItem;
        "ion-item-divider": IonItemDivider;
        "ion-item-group": IonItemGroup;
        "ion-item-option": IonItemOption;
        "ion-item-options": IonItemOptions;
        "ion-item-sliding": IonItemSliding;
        "ion-label": IonLabel;
        "ion-list": IonList;
        "ion-list-header": IonListHeader;
        "ion-loading": IonLoading;
        "ion-menu": IonMenu;
        "ion-menu-button": IonMenuButton;
        "ion-menu-toggle": IonMenuToggle;
        "ion-modal": IonModal;
        "ion-nav": IonNav;
        "ion-nav-link": IonNavLink;
        "ion-note": IonNote;
        "ion-picker": IonPicker;
        "ion-picker-column": IonPickerColumn;
        "ion-picker-column-internal": IonPickerColumnInternal;
        "ion-picker-internal": IonPickerInternal;
        "ion-popover": IonPopover;
        "ion-progress-bar": IonProgressBar;
        "ion-radio": IonRadio;
        "ion-radio-group": IonRadioGroup;
        "ion-range": IonRange;
        "ion-refresher": IonRefresher;
        "ion-refresher-content": IonRefresherContent;
        "ion-reorder": IonReorder;
        "ion-reorder-group": IonReorderGroup;
        "ion-ripple-effect": IonRippleEffect;
        "ion-route": IonRoute;
        "ion-route-redirect": IonRouteRedirect;
        "ion-router": IonRouter;
        "ion-router-link": IonRouterLink;
        "ion-router-outlet": IonRouterOutlet;
        "ion-row": IonRow;
        "ion-searchbar": IonSearchbar;
        "ion-segment": IonSegment;
        "ion-segment-button": IonSegmentButton;
        "ion-select": IonSelect;
        "ion-select-option": IonSelectOption;
        "ion-select-popover": IonSelectPopover;
        "ion-skeleton-text": IonSkeletonText;
        "ion-spinner": IonSpinner;
        "ion-split-pane": IonSplitPane;
        "ion-tab": IonTab;
        "ion-tab-bar": IonTabBar;
        "ion-tab-button": IonTabButton;
        "ion-tabs": IonTabs;
        "ion-text": IonText;
        "ion-textarea": IonTextarea;
        "ion-thumbnail": IonThumbnail;
        "ion-title": IonTitle;
        "ion-toast": IonToast;
        "ion-toggle": IonToggle;
        "ion-toolbar": IonToolbar;
    }
}
export { LocalJSX as JSX };
declare module "@stencil/core" {
    export namespace JSX {
        interface IntrinsicElements {
            "ion-accordion": LocalJSX.IonAccordion & JSXBase.HTMLAttributes<HTMLIonAccordionElement>;
            "ion-accordion-group": LocalJSX.IonAccordionGroup & JSXBase.HTMLAttributes<HTMLIonAccordionGroupElement>;
            "ion-action-sheet": LocalJSX.IonActionSheet & JSXBase.HTMLAttributes<HTMLIonActionSheetElement>;
            "ion-alert": LocalJSX.IonAlert & JSXBase.HTMLAttributes<HTMLIonAlertElement>;
            "ion-app": LocalJSX.IonApp & JSXBase.HTMLAttributes<HTMLIonAppElement>;
            "ion-avatar": LocalJSX.IonAvatar & JSXBase.HTMLAttributes<HTMLIonAvatarElement>;
            "ion-back-button": LocalJSX.IonBackButton & JSXBase.HTMLAttributes<HTMLIonBackButtonElement>;
            "ion-backdrop": LocalJSX.IonBackdrop & JSXBase.HTMLAttributes<HTMLIonBackdropElement>;
            "ion-badge": LocalJSX.IonBadge & JSXBase.HTMLAttributes<HTMLIonBadgeElement>;
            "ion-breadcrumb": LocalJSX.IonBreadcrumb & JSXBase.HTMLAttributes<HTMLIonBreadcrumbElement>;
            "ion-breadcrumbs": LocalJSX.IonBreadcrumbs & JSXBase.HTMLAttributes<HTMLIonBreadcrumbsElement>;
            "ion-button": LocalJSX.IonButton & JSXBase.HTMLAttributes<HTMLIonButtonElement>;
            "ion-buttons": LocalJSX.IonButtons & JSXBase.HTMLAttributes<HTMLIonButtonsElement>;
            "ion-card": LocalJSX.IonCard & JSXBase.HTMLAttributes<HTMLIonCardElement>;
            "ion-card-content": LocalJSX.IonCardContent & JSXBase.HTMLAttributes<HTMLIonCardContentElement>;
            "ion-card-header": LocalJSX.IonCardHeader & JSXBase.HTMLAttributes<HTMLIonCardHeaderElement>;
            "ion-card-subtitle": LocalJSX.IonCardSubtitle & JSXBase.HTMLAttributes<HTMLIonCardSubtitleElement>;
            "ion-card-title": LocalJSX.IonCardTitle & JSXBase.HTMLAttributes<HTMLIonCardTitleElement>;
            "ion-checkbox": LocalJSX.IonCheckbox & JSXBase.HTMLAttributes<HTMLIonCheckboxElement>;
            "ion-chip": LocalJSX.IonChip & JSXBase.HTMLAttributes<HTMLIonChipElement>;
            "ion-col": LocalJSX.IonCol & JSXBase.HTMLAttributes<HTMLIonColElement>;
            "ion-content": LocalJSX.IonContent & JSXBase.HTMLAttributes<HTMLIonContentElement>;
            "ion-datetime": LocalJSX.IonDatetime & JSXBase.HTMLAttributes<HTMLIonDatetimeElement>;
            "ion-datetime-button": LocalJSX.IonDatetimeButton & JSXBase.HTMLAttributes<HTMLIonDatetimeButtonElement>;
            "ion-fab": LocalJSX.IonFab & JSXBase.HTMLAttributes<HTMLIonFabElement>;
            "ion-fab-button": LocalJSX.IonFabButton & JSXBase.HTMLAttributes<HTMLIonFabButtonElement>;
            "ion-fab-list": LocalJSX.IonFabList & JSXBase.HTMLAttributes<HTMLIonFabListElement>;
            "ion-footer": LocalJSX.IonFooter & JSXBase.HTMLAttributes<HTMLIonFooterElement>;
            "ion-grid": LocalJSX.IonGrid & JSXBase.HTMLAttributes<HTMLIonGridElement>;
            "ion-header": LocalJSX.IonHeader & JSXBase.HTMLAttributes<HTMLIonHeaderElement>;
            "ion-img": LocalJSX.IonImg & JSXBase.HTMLAttributes<HTMLIonImgElement>;
            "ion-infinite-scroll": LocalJSX.IonInfiniteScroll & JSXBase.HTMLAttributes<HTMLIonInfiniteScrollElement>;
            "ion-infinite-scroll-content": LocalJSX.IonInfiniteScrollContent & JSXBase.HTMLAttributes<HTMLIonInfiniteScrollContentElement>;
            "ion-input": LocalJSX.IonInput & JSXBase.HTMLAttributes<HTMLIonInputElement>;
            "ion-item": LocalJSX.IonItem & JSXBase.HTMLAttributes<HTMLIonItemElement>;
            "ion-item-divider": LocalJSX.IonItemDivider & JSXBase.HTMLAttributes<HTMLIonItemDividerElement>;
            "ion-item-group": LocalJSX.IonItemGroup & JSXBase.HTMLAttributes<HTMLIonItemGroupElement>;
            "ion-item-option": LocalJSX.IonItemOption & JSXBase.HTMLAttributes<HTMLIonItemOptionElement>;
            "ion-item-options": LocalJSX.IonItemOptions & JSXBase.HTMLAttributes<HTMLIonItemOptionsElement>;
            "ion-item-sliding": LocalJSX.IonItemSliding & JSXBase.HTMLAttributes<HTMLIonItemSlidingElement>;
            "ion-label": LocalJSX.IonLabel & JSXBase.HTMLAttributes<HTMLIonLabelElement>;
            "ion-list": LocalJSX.IonList & JSXBase.HTMLAttributes<HTMLIonListElement>;
            "ion-list-header": LocalJSX.IonListHeader & JSXBase.HTMLAttributes<HTMLIonListHeaderElement>;
            "ion-loading": LocalJSX.IonLoading & JSXBase.HTMLAttributes<HTMLIonLoadingElement>;
            "ion-menu": LocalJSX.IonMenu & JSXBase.HTMLAttributes<HTMLIonMenuElement>;
            "ion-menu-button": LocalJSX.IonMenuButton & JSXBase.HTMLAttributes<HTMLIonMenuButtonElement>;
            "ion-menu-toggle": LocalJSX.IonMenuToggle & JSXBase.HTMLAttributes<HTMLIonMenuToggleElement>;
            "ion-modal": LocalJSX.IonModal & JSXBase.HTMLAttributes<HTMLIonModalElement>;
            "ion-nav": LocalJSX.IonNav & JSXBase.HTMLAttributes<HTMLIonNavElement>;
            "ion-nav-link": LocalJSX.IonNavLink & JSXBase.HTMLAttributes<HTMLIonNavLinkElement>;
            "ion-note": LocalJSX.IonNote & JSXBase.HTMLAttributes<HTMLIonNoteElement>;
            "ion-picker": LocalJSX.IonPicker & JSXBase.HTMLAttributes<HTMLIonPickerElement>;
            "ion-picker-column": LocalJSX.IonPickerColumn & JSXBase.HTMLAttributes<HTMLIonPickerColumnElement>;
            "ion-picker-column-internal": LocalJSX.IonPickerColumnInternal & JSXBase.HTMLAttributes<HTMLIonPickerColumnInternalElement>;
            "ion-picker-internal": LocalJSX.IonPickerInternal & JSXBase.HTMLAttributes<HTMLIonPickerInternalElement>;
            "ion-popover": LocalJSX.IonPopover & JSXBase.HTMLAttributes<HTMLIonPopoverElement>;
            "ion-progress-bar": LocalJSX.IonProgressBar & JSXBase.HTMLAttributes<HTMLIonProgressBarElement>;
            "ion-radio": LocalJSX.IonRadio & JSXBase.HTMLAttributes<HTMLIonRadioElement>;
            "ion-radio-group": LocalJSX.IonRadioGroup & JSXBase.HTMLAttributes<HTMLIonRadioGroupElement>;
            "ion-range": LocalJSX.IonRange & JSXBase.HTMLAttributes<HTMLIonRangeElement>;
            "ion-refresher": LocalJSX.IonRefresher & JSXBase.HTMLAttributes<HTMLIonRefresherElement>;
            "ion-refresher-content": LocalJSX.IonRefresherContent & JSXBase.HTMLAttributes<HTMLIonRefresherContentElement>;
            "ion-reorder": LocalJSX.IonReorder & JSXBase.HTMLAttributes<HTMLIonReorderElement>;
            "ion-reorder-group": LocalJSX.IonReorderGroup & JSXBase.HTMLAttributes<HTMLIonReorderGroupElement>;
            "ion-ripple-effect": LocalJSX.IonRippleEffect & JSXBase.HTMLAttributes<HTMLIonRippleEffectElement>;
            "ion-route": LocalJSX.IonRoute & JSXBase.HTMLAttributes<HTMLIonRouteElement>;
            "ion-route-redirect": LocalJSX.IonRouteRedirect & JSXBase.HTMLAttributes<HTMLIonRouteRedirectElement>;
            "ion-router": LocalJSX.IonRouter & JSXBase.HTMLAttributes<HTMLIonRouterElement>;
            "ion-router-link": LocalJSX.IonRouterLink & JSXBase.HTMLAttributes<HTMLIonRouterLinkElement>;
            "ion-router-outlet": LocalJSX.IonRouterOutlet & JSXBase.HTMLAttributes<HTMLIonRouterOutletElement>;
            "ion-row": LocalJSX.IonRow & JSXBase.HTMLAttributes<HTMLIonRowElement>;
            "ion-searchbar": LocalJSX.IonSearchbar & JSXBase.HTMLAttributes<HTMLIonSearchbarElement>;
            "ion-segment": LocalJSX.IonSegment & JSXBase.HTMLAttributes<HTMLIonSegmentElement>;
            "ion-segment-button": LocalJSX.IonSegmentButton & JSXBase.HTMLAttributes<HTMLIonSegmentButtonElement>;
            "ion-select": LocalJSX.IonSelect & JSXBase.HTMLAttributes<HTMLIonSelectElement>;
            "ion-select-option": LocalJSX.IonSelectOption & JSXBase.HTMLAttributes<HTMLIonSelectOptionElement>;
            "ion-select-popover": LocalJSX.IonSelectPopover & JSXBase.HTMLAttributes<HTMLIonSelectPopoverElement>;
            "ion-skeleton-text": LocalJSX.IonSkeletonText & JSXBase.HTMLAttributes<HTMLIonSkeletonTextElement>;
            "ion-spinner": LocalJSX.IonSpinner & JSXBase.HTMLAttributes<HTMLIonSpinnerElement>;
            "ion-split-pane": LocalJSX.IonSplitPane & JSXBase.HTMLAttributes<HTMLIonSplitPaneElement>;
            "ion-tab": LocalJSX.IonTab & JSXBase.HTMLAttributes<HTMLIonTabElement>;
            "ion-tab-bar": LocalJSX.IonTabBar & JSXBase.HTMLAttributes<HTMLIonTabBarElement>;
            "ion-tab-button": LocalJSX.IonTabButton & JSXBase.HTMLAttributes<HTMLIonTabButtonElement>;
            "ion-tabs": LocalJSX.IonTabs & JSXBase.HTMLAttributes<HTMLIonTabsElement>;
            "ion-text": LocalJSX.IonText & JSXBase.HTMLAttributes<HTMLIonTextElement>;
            "ion-textarea": LocalJSX.IonTextarea & JSXBase.HTMLAttributes<HTMLIonTextareaElement>;
            "ion-thumbnail": LocalJSX.IonThumbnail & JSXBase.HTMLAttributes<HTMLIonThumbnailElement>;
            "ion-title": LocalJSX.IonTitle & JSXBase.HTMLAttributes<HTMLIonTitleElement>;
            "ion-toast": LocalJSX.IonToast & JSXBase.HTMLAttributes<HTMLIonToastElement>;
            "ion-toggle": LocalJSX.IonToggle & JSXBase.HTMLAttributes<HTMLIonToggleElement>;
            "ion-toolbar": LocalJSX.IonToolbar & JSXBase.HTMLAttributes<HTMLIonToolbarElement>;
        }
    }
}
