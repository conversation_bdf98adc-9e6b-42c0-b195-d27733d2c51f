{"ast": null, "code": "import { BrowserModule } from '@angular/platform-browser';\nimport { RouterOutlet } from '@angular/router';\nimport { AppRoutingModule } from './app-routing.module';\nimport { AppComponent } from './app.component';\nimport { HttpClientModule } from '@angular/common/http';\nimport { BrowserAnimationsModule } from '@angular/platform-browser/animations';\nimport { GeneratorComponent } from './GeneratorPage/generator/generator.component';\nimport { InputTextModule } from 'primeng/inputtext';\nimport { CheckboxModule } from 'primeng/checkbox';\nimport { RadioButtonModule } from 'primeng/radiobutton';\nimport { ButtonModule } from 'primeng/button';\nimport { ProgressBarModule } from 'primeng/progressbar';\nimport { ToastModule } from 'primeng/toast';\nimport { MessageService } from 'primeng/api';\nimport { DropdownModule } from 'primeng/dropdown';\nimport { FormsModule } from '@angular/forms';\nimport { VideosDialogComponent } from './GeneratorPage/videos-dialog/videos-dialog.component';\nimport { DialogModule } from 'primeng/dialog';\nimport * as i0 from \"@angular/core\";\nexport class AppModule {\n  static {\n    this.ɵfac = function AppModule_Factory(t) {\n      return new (t || AppModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: AppModule,\n      bootstrap: [AppComponent]\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      providers: [MessageService],\n      imports: [BrowserModule, AppRoutingModule, HttpClientModule, BrowserAnimationsModule, InputTextModule, CheckboxModule, RadioButtonModule, ButtonModule, ProgressBarModule, ToastModule, DropdownModule, FormsModule, DialogModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(AppModule, {\n    declarations: [AppComponent, GeneratorComponent, VideosDialogComponent],\n    imports: [BrowserModule, AppRoutingModule, HttpClientModule, BrowserAnimationsModule, RouterOutlet, InputTextModule, CheckboxModule, RadioButtonModule, ButtonModule, ProgressBarModule, ToastModule, DropdownModule, FormsModule, DialogModule]\n  });\n})();", "map": {"version": 3, "names": ["BrowserModule", "RouterOutlet", "AppRoutingModule", "AppComponent", "HttpClientModule", "BrowserAnimationsModule", "GeneratorComponent", "InputTextModule", "CheckboxModule", "RadioButtonModule", "ButtonModule", "ProgressBarModule", "ToastModule", "MessageService", "DropdownModule", "FormsModule", "VideosDialogComponent", "DialogModule", "AppModule", "bootstrap", "imports", "declarations"], "sources": ["C:\\Users\\<USER>\\Desktop\\تطبيق ناشر ايات قئانية\\QuranVidGen\\src\\app\\app.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { BrowserModule } from '@angular/platform-browser';\r\nimport { RouterOutlet } from '@angular/router';\r\nimport { AppRoutingModule } from './app-routing.module';\r\nimport { AppComponent } from './app.component';\r\nimport { HTTP_INTERCEPTORS, HttpClient, HttpClientModule, HttpHandler } from '@angular/common/http';\r\nimport { BrowserAnimationsModule } from '@angular/platform-browser/animations';\r\nimport { GeneratorComponent } from './GeneratorPage/generator/generator.component';\r\nimport { InputTextModule } from 'primeng/inputtext';\r\nimport { CheckboxModule } from 'primeng/checkbox';\r\nimport { RadioButtonModule } from 'primeng/radiobutton';\r\nimport {ButtonModule} from 'primeng/button'\r\nimport { ProgressBarModule } from 'primeng/progressbar';\r\nimport { ToastModule } from 'primeng/toast';\r\nimport { MessageService } from 'primeng/api';\r\nimport { DropdownModule } from 'primeng/dropdown';\r\nimport { FormsModule } from '@angular/forms';\r\nimport { VideosDialogComponent } from './GeneratorPage/videos-dialog/videos-dialog.component';\r\nimport { DialogModule } from 'primeng/dialog';\r\n\r\n\r\n@NgModule({\r\n  declarations: [\r\n    AppComponent,\r\n    GeneratorComponent,\r\n    VideosDialogComponent\r\n  ],\r\n  imports: [\r\n    BrowserModule,\r\n    AppRoutingModule,\r\n    HttpClientModule,\r\n    BrowserAnimationsModule,\r\n    RouterOutlet,\r\n    InputTextModule,\r\n    CheckboxModule,\r\n    RadioButtonModule,\r\n    ButtonModule,\r\n    ProgressBarModule,\r\n    ToastModule,\r\n    DropdownModule,\r\n    FormsModule,\r\n    DialogModule\r\n  ],\r\n  providers: [MessageService],\r\n  bootstrap: [AppComponent]\r\n})\r\nexport class AppModule { }\r\n"], "mappings": "AACA,SAASA,aAAa,QAAQ,2BAA2B;AACzD,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,gBAAgB,QAAQ,sBAAsB;AACvD,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAAwCC,gBAAgB,QAAqB,sBAAsB;AACnG,SAASC,uBAAuB,QAAQ,sCAAsC;AAC9E,SAASC,kBAAkB,QAAQ,+CAA+C;AAClF,SAASC,eAAe,QAAQ,mBAAmB;AACnD,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,iBAAiB,QAAQ,qBAAqB;AACvD,SAAQC,YAAY,QAAO,gBAAgB;AAC3C,SAASC,iBAAiB,QAAQ,qBAAqB;AACvD,SAASC,WAAW,QAAQ,eAAe;AAC3C,SAASC,cAAc,QAAQ,aAAa;AAC5C,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,qBAAqB,QAAQ,uDAAuD;AAC7F,SAASC,YAAY,QAAQ,gBAAgB;;AA4B7C,OAAM,MAAOC,SAAS;;;uBAATA,SAAS;IAAA;EAAA;;;YAATA,SAAS;MAAAC,SAAA,GAFRhB,YAAY;IAAA;EAAA;;;iBADb,CAACU,cAAc,CAAC;MAAAO,OAAA,GAfzBpB,aAAa,EACbE,gBAAgB,EAChBE,gBAAgB,EAChBC,uBAAuB,EAEvBE,eAAe,EACfC,cAAc,EACdC,iBAAiB,EACjBC,YAAY,EACZC,iBAAiB,EACjBC,WAAW,EACXE,cAAc,EACdC,WAAW,EACXE,YAAY;IAAA;EAAA;;;2EAKHC,SAAS;IAAAG,YAAA,GAvBlBlB,YAAY,EACZG,kBAAkB,EAClBU,qBAAqB;IAAAI,OAAA,GAGrBpB,aAAa,EACbE,gBAAgB,EAChBE,gBAAgB,EAChBC,uBAAuB,EACvBJ,YAAY,EACZM,eAAe,EACfC,cAAc,EACdC,iBAAiB,EACjBC,YAAY,EACZC,iBAAiB,EACjBC,WAAW,EACXE,cAAc,EACdC,WAAW,EACXE,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}