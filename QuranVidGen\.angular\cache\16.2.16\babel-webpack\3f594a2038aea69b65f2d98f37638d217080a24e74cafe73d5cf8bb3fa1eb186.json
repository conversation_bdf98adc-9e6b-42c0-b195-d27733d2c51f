{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Desktop/\\u062A\\u0637\\u0628\\u064A\\u0642 \\u0646\\u0627\\u0634\\u0631 \\u0627\\u064A\\u0627\\u062A \\u0642\\u0626\\u0627\\u0646\\u064A\\u0629/QuranVidGen/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { signal } from '@angular/core';\nimport { FFmpeg } from '@ffmpeg/ffmpeg';\nimport { fetchFile, toBlobURL } from '@ffmpeg/util';\nimport { Directory, Encoding, Filesystem } from '@capacitor/filesystem';\nimport { Dialog } from '@capacitor/dialog';\n// FFmpeg functionality will use @ffmpeg/ffmpeg instead\nimport { Capacitor } from '@capacitor/core';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/Services/quran.service\";\nimport * as i2 from \"src/app/Services/helper.service\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"primeng/inputtext\";\nimport * as i5 from \"primeng/api\";\nimport * as i6 from \"primeng/button\";\nimport * as i7 from \"primeng/progressbar\";\nimport * as i8 from \"primeng/toast\";\nimport * as i9 from \"primeng/dropdown\";\nimport * as i10 from \"@angular/forms\";\nimport * as i11 from \"primeng/dialog\";\nimport * as i12 from \"../videos-dialog/videos-dialog.component\";\nfunction GeneratorComponent_div_4_ng_template_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 30);\n  }\n}\nfunction GeneratorComponent_div_4_div_26_video_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"video\", 33);\n  }\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"src\", ctx_r9.videoURL, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction GeneratorComponent_div_4_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 31);\n    i0.ɵɵtemplate(1, GeneratorComponent_div_4_div_26_video_1_Template, 1, 1, \"video\", 32);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r8.videoURL != \"\");\n  }\n}\nfunction GeneratorComponent_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 8)(2, \"div\", 9)(3, \"p-dropdown\", 10);\n    i0.ɵɵlistener(\"ngModelChange\", function GeneratorComponent_div_4_Template_p_dropdown_ngModelChange_3_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r10 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r10.currentSurah = $event);\n    })(\"onChange\", function GeneratorComponent_div_4_Template_p_dropdown_onChange_3_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const _r4 = i0.ɵɵreference(9);\n      const _r5 = i0.ɵɵreference(13);\n      const ctx_r12 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r12.helper.SurahNumberRestrict(ctx_r12.GetCurrentSurahNumber(), _r4, _r5));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p-dropdown\", 11);\n    i0.ɵɵlistener(\"ngModelChange\", function GeneratorComponent_div_4_Template_p_dropdown_ngModelChange_4_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r13 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r13.currentReciterId = $event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 12)(6, \"span\", 13);\n    i0.ɵɵelement(7, \"i\", 14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"input\", 15, 16);\n    i0.ɵɵlistener(\"input\", function GeneratorComponent_div_4_Template_input_input_8_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const _r4 = i0.ɵɵreference(9);\n      const ctx_r14 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r14.helper.InputNumberRestrict(_r4));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"span\", 17);\n    i0.ɵɵelement(11, \"i\", 18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"input\", 19, 20);\n    i0.ɵɵlistener(\"input\", function GeneratorComponent_div_4_Template_input_input_12_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const _r5 = i0.ɵɵreference(13);\n      const ctx_r15 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r15.helper.InputNumberRestrict(_r5));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"div\", 12)(15, \"span\", 13);\n    i0.ɵɵelement(16, \"img\", 21);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"input\", 22, 20);\n    i0.ɵɵlistener(\"ngModelChange\", function GeneratorComponent_div_4_Template_input_ngModelChange_17_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r16 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r16.fontSize = $event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"span\", 17);\n    i0.ɵɵelement(20, \"i\", 23);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"div\", 24)(22, \"p-button\", 25);\n    i0.ɵɵlistener(\"onClick\", function GeneratorComponent_div_4_Template_p_button_onClick_22_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r17 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r17.videoPickerVisible = true);\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(23, \"div\", 26)(24, \"p-button\", 27);\n    i0.ɵɵlistener(\"onClick\", function GeneratorComponent_div_4_Template_p_button_onClick_24_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const _r4 = i0.ɵɵreference(9);\n      const _r5 = i0.ɵɵreference(13);\n      const ctx_r18 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r18.GetAyahsAndLoadThem(ctx_r18.GetCurrentSurahNumber(), ctx_r18.currentReciterId, _r4.value, _r5.value));\n    });\n    i0.ɵɵtemplate(25, GeneratorComponent_div_4_ng_template_25_Template, 1, 0, \"ng-template\", 28);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵtemplate(26, GeneratorComponent_div_4_div_26_Template, 2, 1, \"div\", 29);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const _r4 = i0.ɵɵreference(9);\n    const _r5 = i0.ɵɵreference(13);\n    const ctx_r0 = i0.ɵɵnextContext();\n    let tmp_16_0;\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"options\", ctx_r0.suras)(\"optionLabel\", \"surahName\")(\"ngModel\", ctx_r0.currentSurah)(\"dropdownIcon\", \"pi pi-book\")(\"optionValue\", \"surahName\")(\"filter\", true);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"options\", ctx_r0.reciters)(\"optionLabel\", \"name\")(\"ngModel\", ctx_r0.currentReciterId)(\"dropdownIcon\", \"pi pi-user\")(\"optionValue\", \"id\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"min\", 1);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"min\", 1);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"min\", 1)(\"max\", 46)(\"ngModel\", ctx_r0.fontSize);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"label\", (tmp_16_0 = ctx_r0.getPickedVideo()) !== null && tmp_16_0 !== undefined ? tmp_16_0 : \"Random Video\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r0.currentReciterId == \"\" || ctx_r0.currentSurah == \"\" || !_r4.value || !_r5.value)(\"iconPos\", \"right\")(\"rounded\", true);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", (ctx_r0.loadedAudio || ctx_r0.firstLoad) && !ctx_r0.ffmpegExecuting);\n  }\n}\nfunction GeneratorComponent_ng_template_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 34)(1, \"div\", 35)(2, \"span\", 2);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(4, \"p-toast\")(5, \"p-progressBar\", 36);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"Loading \", ctx_r2.currentLoading.name, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", ctx_r2.currentLoading.value);\n  }\n}\nfunction GeneratorComponent_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 31)(1, \"div\", 37)(2, \"div\", 38)(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"span\");\n    i0.ɵɵtext(6, \"Elapsed Time \");\n    i0.ɵɵelementStart(7, \"span\", 39);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelement(9, \"p-toast\")(10, \"p-progressBar\", 40);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r3.executingProgressLabel());\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r3.executingTime + \" s\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ariaValueMin\", 0)(\"ariaValueMax\", 100)(\"value\", ctx_r3.executingProgress());\n  }\n}\nconst _c0 = function () {\n  return {\n    width: \"50vw\"\n  };\n};\nconst _c1 = function () {\n  return {\n    \"960px\": \"75vw\"\n  };\n};\nconst baseURL = \"https://unpkg.com/@ffmpeg/core@0.12.6/dist/esm\";\nexport class GeneratorComponent {\n  constructor(quranService, helper) {\n    this.quranService = quranService;\n    this.helper = helper;\n    this.loaded = false;\n    this.loadedAudio = false;\n    this.ffmpeg = new FFmpeg();\n    this.videoURL = \"\";\n    this.message = \"\";\n    this.currentLoading = {\n      name: '',\n      value: 0\n    };\n    this.firstLoad = true;\n    this.ayatTexts = [];\n    this.suras = [];\n    this.reciters = [];\n    this.currentSurah = '';\n    this.currentReciterId = '';\n    this.ffmpegExecuting = false;\n    this.videoPickerVisible = false;\n    this.executingProgress = signal(0);\n    this.executingTime = 0;\n    this.ayahtTextAndAudio = [];\n    this.executingProgressLabel = signal('');\n    this.fontSize = 18;\n    this.clock = 0;\n    this.previousPercentage = -1;\n  }\n  getPickedVideo() {\n    if (this.pickedVideo) {\n      return `Video ${this.pickedVideo}`;\n    }\n    return undefined;\n  }\n  ngAfterViewInit() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      yield _this.load();\n      _this.quranService.GetAllSuras().subscribe(suras => {\n        _this.suras = suras;\n      });\n      _this.quranService.GetReciters()?.subscribe(reciters => {\n        _this.reciters = reciters;\n      });\n    })();\n  }\n  GetAyahsAndLoadThem(surahNumber, reciter, startAyah, endAyah) {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      _this2.ayahtTextAndAudio = [];\n      _this2.ayatTexts = [];\n      let reciterId = Number.parseInt(reciter);\n      let start = Number.parseInt(startAyah);\n      let end = Number.parseInt(endAyah);\n      _this2.ayatTexts = (yield _this2.quranService.GetAyatTexts(surahNumber, start, end, 'arabic').toPromise()) ?? [];\n      let blobs = (yield _this2.quranService.GetAyahsAudio(reciterId, surahNumber, start, end).toPromise()) ?? [];\n      yield _this2.transcode(blobs);\n    })();\n  }\n  GetCurrentSurahNumber() {\n    this.currentSurah;\n    this.suras;\n    return this.suras.findIndex(x => x.surahName == this.currentSurah) + 1;\n  }\n  GetProgressText(url, name, recieved) {\n    url = url.toString();\n    let percentage = this.helper.getDownloadProgress(url, recieved);\n    if (percentage != this.previousPercentage) {\n      this.currentLoading = {\n        name: name,\n        value: percentage\n      };\n      this.previousPercentage = percentage;\n    }\n  }\n  load() {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      _this3.loaded = false;\n      if (Capacitor.getPlatform() != 'android') {\n        _this3.ffmpeg.on(\"log\", ({\n          message\n        }) => {\n          _this3.message = message;\n        });\n        _this3.ffmpeg.on(\"progress\", ({\n          progress,\n          time\n        }) => {\n          _this3.executingProgress.set(Math.floor(progress * 100));\n          _this3.executingTime = Math.floor(time / 1000000);\n        });\n        yield _this3.ffmpeg.load({\n          coreURL: yield toBlobURL(`assets/ffmpeg/ffmpeg-core.js`, 'text/javascript', true, ev => _this3.GetProgressText(ev.url, 'Core Script', ev.received)),\n          wasmURL: yield toBlobURL(`assets/ffmpeg/ffmpeg-core.wasm`, 'application/wasm', true, ev => _this3.GetProgressText(ev.url, 'Web Worker', ev.received)),\n          classWorkerURL: `${window.location.href}assets/ffmpeg/worker.js`\n        });\n      }\n      _this3.loaded = true;\n    })();\n  }\n  transcode(audios) {\n    var _this4 = this;\n    return _asyncToGenerator(function* () {\n      if (Capacitor.getPlatform() == 'android') {\n        yield _this4.transcodeAndroid(audios);\n        return;\n      }\n      _this4.firstLoad = true;\n      _this4.loadedAudio = true;\n      let audioNames = [];\n      for (let index = 0; index < audios.length; index++) {\n        let audioData = yield fetchFile(audios[index]);\n        let duration = yield _this4.helper.getDuration(audioData);\n        yield _this4.ffmpeg.writeFile(index + '.mp3', audioData);\n        _this4.ayahtTextAndAudio.push({\n          text: _this4.ayatTexts[index],\n          duration: duration ?? 0\n        });\n        // let duration = await this.ffmpeg.\n        audioNames.push(`file ${index}.mp3`);\n        if (index < audios.length - 1) {// Don't add silence after the last audio file\n          // audioNames.push(`file 'silence.mp3'`);\n        }\n      }\n      // let silenceCommand = ['-f', 'lavfi', '-i', 'anullsrc', '-t', '0.5', 'silence.mp3'];\n      // await this.ffmpeg.exec(silenceCommand);\n      // Create a text file with the names of the audio files to be concatenated\n      _this4.executingProgressLabel.set('Generating Subtitles');\n      let filelist = audioNames.join('\\n');\n      yield _this4.ffmpeg.writeFile('filelist.txt', filelist);\n      _this4.executingProgressLabel.set('Generating Audio');\n      // Use the concat demuxer in ffmpeg\n      let commands = ['-f', 'concat', '-safe', '0', '-i', 'filelist.txt', '-c', 'copy', 'output.mp3'];\n      let randomVideo = Math.max(Math.round(Math.random() * 15), 1);\n      let finalVideoName = _this4.pickedVideo ? _this4.pickedVideo : randomVideo;\n      yield _this4.ffmpeg.writeFile('video.mp4', yield fetchFile(`/assets/videos/${finalVideoName}.mp4`));\n      yield _this4.ffmpeg.exec(commands);\n      _this4.executingProgressLabel.set('Merging Audio with Video');\n      // let subtitleFile = new TextEncoder().encode(this.getSubTitles());\n      let subtitleFile = _this4.getSubtitlesAsAss('center', 'Al-QuranAlKareem', _this4.fontSize.toString());\n      yield _this4.ffmpeg.writeFile('subtitles.ass', subtitleFile);\n      yield _this4.ffmpeg.writeFile('/tmp/Al-QuranAlKareem', yield fetchFile('/assets/fonts/Al-QuranAlKareem.ttf'));\n      // await this.ffmpeg.writeFile('subtitles.ass',await fetchFile('/assets/subs/test.ass'));\n      _this4.ffmpegExecuting = true;\n      _this4.executingProgress.set(0);\n      yield _this4.ffmpeg.exec(['-stream_loop', '-1', '-i', 'video.mp4', '-i', 'output.mp3', '-c:v', 'copy', '-c:a', 'aac', '-map', '0:v:0', '-map', '1:a:0', '-shortest', 'output.mp4']);\n      //:fontsdir=/tmp:force_style='Fontname=Arimo,Fontsize=24,PrimaryColour=&H00FFFFFF,OutlineColour=&H000000FF,BackColour=&H00000000,Bold=1,Italic=0,Alignment=2,MarginV=40\n      let command = ['-i', 'output.mp4', \"-vf\", \"ass=subtitles.ass:fontsdir=tmp\", \"-c:v\", \"libx264\", \"-preset\", \"ultrafast\", \"-crf\", \"32\", \"-c:a\", \"copy\", 'outputsub.mp4'];\n      yield _this4.ffmpeg.exec(command);\n      const fileData = yield _this4.ffmpeg.readFile('outputsub.mp4');\n      const data = new Uint8Array(fileData);\n      _this4.videoURL = URL.createObjectURL(new Blob([data.buffer], {\n        type: 'video/mp4'\n      }));\n      // let result:number = await this.ffmpeg.exec(commands);\n      // if(result != 0)return;\n      // const fileData = await this.ffmpeg.readFile('output.mp3');\n      // const data = new Uint8Array(fileData as ArrayBuffer);\n      // this.videoURL = URL.createObjectURL(\n      //   new Blob([data.buffer], { type: 'audio/mpeg' })\n      // );\n      _this4.loadedAudio = true;\n      _this4.ffmpegExecuting = false;\n    })();\n  }\n  getSubtitlesAsAss(alignment, fontName, fontsize = '16') {\n    let assContent = `[Script Info]\n; Script generated by Ebby.co\nScriptType: v4.00+\nScaledBorderAndShadow: yes\n\n[V4+ Styles]\nFormat: Name, Fontname, Fontsize, PrimaryColour, SecondaryColour, OutlineColour, BackColour, Bold, Italic, Underline, StrikeOut, ScaleX, ScaleY, Spacing, Angle, BorderStyle, Outline, Shadow, Alignment, MarginL, MarginR, MarginV, Encoding\nStyle: Default,${fontName},${fontsize},&Hffffff,&Hffffff,&H000000,&H0,0,0,0,0,100,100,0,0,0,1,1,5,0,0,0,0,UTF-8\n\n[Events]\nFormat: Layer, Start, End, Style, Name, MarginL, MarginR, MarginV, Effect, Text\\n`;\n    // Set alignment based on the provided parameter\n    let alignmentCode = \"10\"; // Default: left alignment\n    if (alignment === \"center\") {\n      alignmentCode = \"2\";\n    } else if (alignment === \"right\") {\n      alignmentCode = \"8\";\n    }\n    let startTime = 0;\n    this.ayahtTextAndAudio.forEach((subtitle, index) => {\n      let endTime = startTime + subtitle.duration;\n      let start_time_str = this.formatTime(startTime);\n      let end_time_str = this.formatTime(endTime);\n      // let end = new Date(endTime * 1000).toISOString().substr(11, 12);\n      assContent += `Dialogue: 0,${start_time_str},${end_time_str},Default,,0,0,0,,${subtitle['text']}\\n`;\n      startTime = endTime;\n    });\n    return assContent;\n  }\n  formatTime(seconds) {\n    const hours = Math.floor(seconds / 3600);\n    const minutes = Math.floor(seconds % 3600 / 60);\n    const remainingSeconds = Math.fround(seconds % 60);\n    const hundredths = Math.floor(remainingSeconds % 1 * 100).toFixed(2);\n    return `${hours}:${minutes.toString().padStart(2, '0')}:${remainingSeconds.toFixed(2).toString().padStart(2, '0')}:${hundredths.toString().padStart(2, '0')}`;\n  }\n  ArrayToBase64(Array) {\n    var binary = '';\n    var len = Array.byteLength;\n    for (var i = 0; i < len; i++) {\n      binary += String.fromCharCode(Array[i]);\n    }\n    return window.btoa(binary);\n  }\n  transcodeAndroid(audios) {\n    var _this5 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        // if(!((await Filesystem.checkPermissions()).publicStorage == 'granted')){\n        //   await Filesystem.requestPermissions()\n        // }\n        yield Filesystem.requestPermissions();\n      } catch (error) {}\n      try {\n        let dirRes = yield Filesystem.readdir({\n          directory: Directory.Documents,\n          path: 'quranvideos'\n        });\n        for (let file of dirRes.files) {\n          yield Filesystem.deleteFile({\n            path: file.uri\n          });\n        }\n        yield Filesystem.rmdir({\n          directory: Directory.Documents,\n          path: 'quranvideos',\n          recursive: true\n        });\n      } catch (error) {}\n      try {\n        yield Filesystem.mkdir({\n          path: 'quranvideos',\n          directory: Directory.Documents\n        });\n      } catch (error) {}\n      let cachePath = '/storage/emulated/0/Documents/quranvideos';\n      let OutputcachePath = 'storage/emulated/0/Documents/quranvideos';\n      _this5.firstLoad = true;\n      _this5.loadedAudio = true;\n      let audioInfos = [];\n      let audioNames = [];\n      for (let index = 0; index < audios.length; index++) {\n        let audioData = yield fetchFile(audios[index]);\n        let duration = yield _this5.helper.getDuration(audioData);\n        // await this.ffmpeg.writeFile(index + '.mp3', audioData);\n        yield Filesystem.writeFile({\n          directory: Directory.Documents,\n          path: `quranvideos/${index + 1}.mp3`,\n          data: _this5.ArrayToBase64(audioData)\n        });\n        audioInfos.push({\n          name: `${index + 1}.mp3`,\n          duration: duration,\n          data: audioData\n        });\n        _this5.ayahtTextAndAudio.push({\n          text: _this5.ayatTexts[index],\n          duration: duration ?? 0\n        });\n        // let duration = await this.ffmpeg.\n        audioNames.push(`file ${cachePath}/${index + 1}.mp3`);\n        if (index < audios.length - 1) {// Don't add silence after the last audio file\n          // audioNames.push(`file 'silence.mp3'`);\n        }\n      }\n      // let silenceCommand = ['-f', 'lavfi', '-i', 'anullsrc', '-t', '0.5', 'silence.mp3'];\n      // await this.ffmpeg.exec(silenceCommand);\n      // Create a text file with the names of the audio files to be concatenated\n      _this5.executingProgressLabel.set('Generating Subtitles');\n      let filelist = audioNames.join('\\n');\n      // await this.ffmpeg.writeFile('filelist.txt', filelist);\n      _this5.executingProgressLabel.set('Generating Audio');\n      // Use the concat demuxer in ffmpeg\n      yield Filesystem.writeFile({\n        data: btoa(filelist),\n        directory: Directory.Documents,\n        path: 'quranvideos/filelist.txt'\n      });\n      // fmp.exec()\n      // await FFmpegKitPlugin.scanFile({\n      //   directory: 'DOCUMENTS',\n      //   path: 'quranvideos'\n      // })\n      let commands = ['-f', 'concat', '-safe', '0', '-i', `${cachePath}/filelist.txt`, '-c', 'copy', `${OutputcachePath}/output.mp3`];\n      // // let commands = ['-f', 'concat', '-i', `${cachePath}/filelist.txt`, '-c', 'copy', `${OutputcachePath}/output.mp3`];\n      // let res = await FFmpegKitPlugin.execute({command:commands.join(' ')});\n      // console.warn(res,res.returnCode);\n      let randomVideo = Math.max(Math.round(Math.random() * 15), 1);\n      let finalVideoName = _this5.pickedVideo ? _this5.pickedVideo : randomVideo;\n      let videoResult = yield Filesystem.writeFile({\n        data: _this5.ArrayToBase64(yield fetchFile(`/assets/videos/${finalVideoName}.mp4`)),\n        directory: Directory.Documents,\n        path: 'quranvideos/video.mp4'\n      });\n      // let mp3Result = await FFmpegKitPlugin.execute({command:commands.join(' ')});\n      // console.warn('mp3Result',mp3Result.returnCode);\n      _this5.executingProgressLabel.set('Merging Audio with Video');\n      // let subtitleFile = new TextEncoder().encode(this.getSubTitles());\n      let subtitleFile = _this5.getSubtitlesAsAss('center', 'Al-QuranAlKareem', _this5.fontSize.toString());\n      // await this.ffmpeg.writeFile('subtitles.ass',subtitleFile);\n      let subtitleResult = yield Filesystem.writeFile({\n        data: subtitleFile,\n        directory: Directory.Documents,\n        path: 'quranvideos/subtitles.txt',\n        encoding: Encoding.UTF8\n      });\n      yield Filesystem.rename({\n        directory: Directory.Documents,\n        from: 'quranvideos/subtitles.txt',\n        to: 'quranvideos/subtitles.ass'\n      });\n      let fontResult = yield Filesystem.writeFile({\n        data: _this5.ArrayToBase64(yield fetchFile('/assets/fonts/Al-QuranAlKareem.ttf')),\n        directory: Directory.Documents,\n        path: 'quranvideos/Al-QuranAlKareem'\n      });\n      // await this.ffmpeg.writeFile('subtitles.ass',await fetchFile('/assets/subs/test.ass'));\n      _this5.ffmpegExecuting = true;\n      _this5.executingProgress.set(0);\n      // await this.ffmpeg.exec(['-stream_loop', '-1', '-i', 'video.mp4', '-i', `${cachePath}/output.mp3`, '-c:v', 'copy', '-c:a', 'aac', '-map', '0:v:0', '-map', '1:a:0', '-shortest',`${cachePath}/output.mp4`]);\n      // let res2 = await FFmpegKitPlugin.execute({command:['-fflags','+genpts','-stream_loop', '-1', '-i', `${cachePath}/video.mp4`,'-i', `${cachePath}/output.mp3`, '-c:v', 'copy','-fflags','+shortest', '-c:a', 'aac', '-map', '0:v:0', '-map', '1:a:0', '-shortest',`${OutputcachePath}/output.mp4`].join(' ')});\n      // ['-stream_loop', '-1', '-i', 'video.mp4', '-i', 'output.mp3', '-c:v', 'copy', '-c:a', 'aac', '-map', '0:v:0', '-map', '1:a:0', '-shortest','output.mp4']\n      console.log('----- start loop -----');\n      // let res2 = await FFmpegKitPlugin.execute({command:['-stream_loop', '-1', '-i', `${cachePath}/video.mp4`,'-i', `${cachePath}/output.mp3`, '-shortest', '-map','0:v:0', '-map', '1:a:0', '-y',`${OutputcachePath}/output.mp4`].join(' ')});\n      // let res2 = await FFmpegKitPlugin.execute({command:['-i', `${cachePath}/video.mp4`, '-i', `${cachePath}/output.mp3`, 'filter_complex', '[1:v]colorkey=0xFFFFFF:0.01:0.0[KeyedOverlay];[0:v][KeyedOverlay]overlay=shortest=1:format=auto[Composite]', '-map', '[Composite]', `${OutputcachePath}/output.mp4`].join(' ')});\n      // console.warn('loop result',res2.returnCode);\n      //:fontsdir=/tmp:force_style='Fontname=Arimo,Fontsize=24,PrimaryColour=&H00FFFFFF,OutlineColour=&H000000FF,BackColour=&H00000000,Bold=1,Italic=0,Alignment=2,MarginV=40\n      // let command = ['-i',`${cachePath}/output.mp4`,\"-vf\",`ass=${cachePath}/subtitles.ass:fontsdir=${cachePath}`,\"-c:v\",\"libx264\",\"-preset\",\"ultrafast\",\"-crf\",\"32\",\"-c:a\",\"copy\",`${OutputcachePath}/outputsub.mp4`];\n      let command = ['-i', `${cachePath}/output.mp4`, \"-vf\", `ass=${cachePath}/subtitles.ass:fontsdir=${cachePath}`, \"-c:a\", \"copy\", `${OutputcachePath}/outputsub.mp4`];\n      // let res3 = await FFmpegKitPlugin.execute({command:command.join(' ')})\n      console.warn('res3', res3.returnCode);\n      // await this.ffmpeg.exec(command);\n      const fileData = yield Filesystem.readFile({\n        directory: Directory.Documents,\n        path: 'quranvideos/outputsub.mp4',\n        encoding: Encoding.UTF8\n      });\n      _this5.videoURL = _this5.ArrayToBase64(new Uint8Array(fileData.data));\n      try {\n        try {\n          yield Filesystem.mkdir({\n            directory: Directory.Documents,\n            path: 'videos',\n            recursive: false\n          });\n        } catch (error) {}\n        yield Filesystem.writeFile({\n          data: _this5.videoURL,\n          path: `videos/generated-video-${Date.now()}.mp4`,\n          directory: Directory.Documents\n        });\n        yield Dialog.alert({\n          title: 'Video Saved!',\n          message: `Video has been saved to your device`,\n          buttonTitle: 'Ok'\n        });\n      } catch (error) {}\n      // let result:number = await this.ffmpeg.exec(commands);\n      // if(result != 0)return;\n      // const fileData = await this.ffmpeg.readFile('output.mp3');\n      // const data = new Uint8Array(fileData as ArrayBuffer);\n      // this.videoURL = URL.createObjectURL(\n      //   new Blob([data.buffer], { type: 'audio/mpeg' })\n      // );\n      _this5.loadedAudio = true;\n      _this5.ffmpegExecuting = false;\n    })();\n  }\n  static {\n    this.ɵfac = function GeneratorComponent_Factory(t) {\n      return new (t || GeneratorComponent)(i0.ɵɵdirectiveInject(i1.QuranService), i0.ɵɵdirectiveInject(i2.HelperService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: GeneratorComponent,\n      selectors: [[\"app-generator\"]],\n      decls: 10,\n      vars: 10,\n      consts: [[1, \"main-container\"], [1, \"title\", \"d-flex\", \"flex-column\", \"my-3\", \"text-light\", \"text-center\"], [1, \"app-title\"], [4, \"ngIf\", \"ngIfElse\"], [\"progress\", \"\"], [\"class\", \"row my-5 justify-content-center mx-auto\", \"style\", \"width: 90%\", 4, \"ngIf\"], [\"header\", \"Background Video Gallery\", 3, \"visible\", \"resizable\", \"breakpoints\", \"visibleChange\"], [3, \"pickedVideo\"], [1, \"row\", \"mx-auto\"], [1, \"d-flex\", \"col-md-10\", \"col-lg-8\", \"col-xl-6\", \"col-xxl-4\", \"justify-content-center\", \"align-items-center\", \"flex-column\", \"mx-auto\", \"mt-3\", \"gap-3\"], [\"styleClass\", \"w-100\", \"placeholder\", \"Select a Surah\", 1, \"w-100\", 3, \"options\", \"optionLabel\", \"ngModel\", \"dropdownIcon\", \"optionValue\", \"filter\", \"ngModelChange\", \"onChange\"], [\"styleClass\", \"w-100\", \"placeholder\", \"Select a Reciter\", 1, \"w-100\", 3, \"options\", \"optionLabel\", \"ngModel\", \"dropdownIcon\", \"optionValue\", \"ngModelChange\"], [1, \"p-inputgroup\"], [1, \"p-inputgroup-addon\"], [1, \"pi\", \"pi-play\"], [\"type\", \"number\", \"pInputText\", \"\", \"placeholder\", \"Start Ayah\", 3, \"min\", \"input\"], [\"start\", \"\"], [1, \"ms-2\", \"rounded-start\", \"p-inputgroup-addon\"], [1, \"pi\", \"pi-circle-on\"], [\"type\", \"number\", \"pInputText\", \"\", \"placeholder\", \"End Ayah\", 3, \"min\", \"input\"], [\"end\", \"\"], [\"src\", \"assets/images/font.svg\", \"alt\", \"\"], [\"type\", \"number\", \"pInputText\", \"\", \"placeholder\", \"Font Size\", 3, \"min\", \"max\", \"ngModel\", \"ngModelChange\"], [1, \"pi\", \"pi-video\"], [1, \"w-50\"], [\"styleClass\", \"rounded-end w-100\", \"severity\", \"primary\", 1, \"w-100\", 3, \"label\", \"onClick\"], [1, \"p-inputgroup\", \"my-3\", \"w-auto\"], [\"label\", \"Generate\", \"severity\", \"primary\", 3, \"disabled\", \"iconPos\", \"rounded\", \"onClick\"], [\"pTemplate\", \"icon\"], [\"class\", \"row my-5 justify-content-center mx-auto\", \"style\", \"width: 90%;\", 4, \"ngIf\"], [\"src\", \"assets/images/generate.svg\", \"alt\", \"\"], [1, \"row\", \"my-5\", \"justify-content-center\", \"mx-auto\", 2, \"width\", \"90%\"], [\"class\", \"col-10 col-md-6 col-lg-4\", \"autoplay\", \"\", \"controls\", \"\", 3, \"src\", 4, \"ngIf\"], [\"autoplay\", \"\", \"controls\", \"\", 1, \"col-10\", \"col-md-6\", \"col-lg-4\", 3, \"src\"], [1, \"d-flex\", \"justify-content-center\", \"align-items-center\", 2, \"height\", \"85dvh\", \"width\", \"100dvw\"], [1, \"mx-auto\", \"w-50\"], [3, \"value\"], [1, \"col-10\", \"col-md-6\", \"col-lg-4\"], [1, \"d-flex\", \"justify-content-between\"], [1, \"fw-bold\", \"app-title\"], [3, \"ariaValueMin\", \"ariaValueMax\", \"value\"]],\n      template: function GeneratorComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h3\", 2);\n          i0.ɵɵtext(3, \"Quran Video Generator\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(4, GeneratorComponent_div_4_Template, 27, 21, \"div\", 3);\n          i0.ɵɵtemplate(5, GeneratorComponent_ng_template_5_Template, 6, 2, \"ng-template\", null, 4, i0.ɵɵtemplateRefExtractor);\n          i0.ɵɵtemplate(7, GeneratorComponent_div_7_Template, 11, 5, \"div\", 5);\n          i0.ɵɵelementStart(8, \"p-dialog\", 6);\n          i0.ɵɵlistener(\"visibleChange\", function GeneratorComponent_Template_p_dialog_visibleChange_8_listener($event) {\n            return ctx.videoPickerVisible = $event;\n          });\n          i0.ɵɵelementStart(9, \"app-videos-dialog\", 7);\n          i0.ɵɵlistener(\"pickedVideo\", function GeneratorComponent_Template_app_videos_dialog_pickedVideo_9_listener($event) {\n            ctx.pickedVideo = $event;\n            return ctx.videoPickerVisible = false;\n          });\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          const _r1 = i0.ɵɵreference(6);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.loaded)(\"ngIfElse\", _r1);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.ffmpegExecuting);\n          i0.ɵɵadvance(1);\n          i0.ɵɵstyleMap(i0.ɵɵpureFunction0(8, _c0));\n          i0.ɵɵproperty(\"visible\", ctx.videoPickerVisible)(\"resizable\", true)(\"breakpoints\", i0.ɵɵpureFunction0(9, _c1));\n        }\n      },\n      dependencies: [i3.NgIf, i4.InputText, i5.PrimeTemplate, i6.Button, i7.ProgressBar, i8.Toast, i9.Dropdown, i10.DefaultValueAccessor, i10.NumberValueAccessor, i10.NgControlStatus, i10.MinValidator, i10.MaxValidator, i10.NgModel, i11.Dialog, i12.VideosDialogComponent],\n      styles: [\"@media (prefers-color-scheme: dark) {\\n    body {\\n    background-color: #333;\\n    color: white;\\n  }\\n  .app-title[_ngcontent-%COMP%] {\\n    color: white;\\n  }\\n}\\n@media (prefers-color-scheme: light) {\\n    body {\\n    color: #333;\\n  }\\n  .app-title[_ngcontent-%COMP%] {\\n    color: #333;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvR2VuZXJhdG9yUGFnZS9nZW5lcmF0b3IvZ2VuZXJhdG9yLmNvbXBvbmVudC5zY3NzIiwid2VicGFjazovLy4vLi4vLi4vJUQ4JUFBJUQ4JUI3JUQ4JUE4JUQ5JThBJUQ5JTgyJTIwJUQ5JTg2JUQ4JUE3JUQ4JUI0JUQ4JUIxJTIwJUQ4JUE3JUQ5JThBJUQ4JUE3JUQ4JUFBJTIwJUQ5JTgyJUQ4JUE2JUQ4JUE3JUQ5JTg2JUQ5JThBJUQ4JUE5L1F1cmFuVmlkR2VuL3NyYy9hcHAvR2VuZXJhdG9yUGFnZS9nZW5lcmF0b3IvZ2VuZXJhdG9yLmNvbXBvbmVudC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUNBO0VBQ0U7SUFDRSxzQkFBQTtJQUNBLFlBQUE7RUNBRjtFREVBO0lBQ0UsWUFBQTtFQ0FGO0FBQ0Y7QURFQTtFQUNFO0lBQ0UsV0FBQTtFQ0FGO0VERUE7SUFDRSxXQUFBO0VDQUY7QUFDRiIsInNvdXJjZXNDb250ZW50IjpbIlxyXG5AbWVkaWEgKHByZWZlcnMtY29sb3Itc2NoZW1lOmRhcmspe1xyXG4gIDo6bmctZGVlcCBib2R5e1xyXG4gICAgYmFja2dyb3VuZC1jb2xvcjogIzMzMztcclxuICAgIGNvbG9yOndoaXRlO1xyXG4gIH1cclxuICAuYXBwLXRpdGxle1xyXG4gICAgY29sb3I6IHdoaXRlO1xyXG4gIH1cclxufVxyXG5AbWVkaWEgKHByZWZlcnMtY29sb3Itc2NoZW1lOmxpZ2h0KXtcclxuICA6Om5nLWRlZXAgYm9keXtcclxuICAgIGNvbG9yOiMzMzM7XHJcbiAgfVxyXG4gIC5hcHAtdGl0bGV7XHJcbiAgICBjb2xvcjogIzMzMztcclxuICB9XHJcbn1cclxuIiwiQG1lZGlhIChwcmVmZXJzLWNvbG9yLXNjaGVtZTogZGFyaykge1xuICA6Om5nLWRlZXAgYm9keSB7XG4gICAgYmFja2dyb3VuZC1jb2xvcjogIzMzMztcbiAgICBjb2xvcjogd2hpdGU7XG4gIH1cbiAgLmFwcC10aXRsZSB7XG4gICAgY29sb3I6IHdoaXRlO1xuICB9XG59XG5AbWVkaWEgKHByZWZlcnMtY29sb3Itc2NoZW1lOiBsaWdodCkge1xuICA6Om5nLWRlZXAgYm9keSB7XG4gICAgY29sb3I6ICMzMzM7XG4gIH1cbiAgLmFwcC10aXRsZSB7XG4gICAgY29sb3I6ICMzMzM7XG4gIH1cbn0iXSwic291cmNlUm9vdCI6IiJ9 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["signal", "FFmpeg", "fetchFile", "toBlobURL", "Directory", "Encoding", "Filesystem", "Dialog", "Capacitor", "i0", "ɵɵelement", "ɵɵproperty", "ctx_r9", "videoURL", "ɵɵsanitizeUrl", "ɵɵelementStart", "ɵɵtemplate", "GeneratorComponent_div_4_div_26_video_1_Template", "ɵɵelementEnd", "ɵɵadvance", "ctx_r8", "ɵɵlistener", "GeneratorComponent_div_4_Template_p_dropdown_ngModelChange_3_listener", "$event", "ɵɵrestoreView", "_r11", "ctx_r10", "ɵɵnextContext", "ɵɵresetView", "<PERSON><PERSON><PERSON><PERSON>", "GeneratorComponent_div_4_Template_p_dropdown_onChange_3_listener", "_r4", "ɵɵreference", "_r5", "ctx_r12", "helper", "SurahNumberRestrict", "GetCurrentSurahNumber", "GeneratorComponent_div_4_Template_p_dropdown_ngModelChange_4_listener", "ctx_r13", "currentReciterId", "GeneratorComponent_div_4_Template_input_input_8_listener", "ctx_r14", "InputNumberRestrict", "GeneratorComponent_div_4_Template_input_input_12_listener", "ctx_r15", "GeneratorComponent_div_4_Template_input_ngModelChange_17_listener", "ctx_r16", "fontSize", "GeneratorComponent_div_4_Template_p_button_onClick_22_listener", "ctx_r17", "videoPickerVisible", "GeneratorComponent_div_4_Template_p_button_onClick_24_listener", "ctx_r18", "GetAyahsAndLoadThem", "value", "GeneratorComponent_div_4_ng_template_25_Template", "GeneratorComponent_div_4_div_26_Template", "ctx_r0", "suras", "reciters", "tmp_16_0", "getPickedVideo", "undefined", "loadedAudio", "firstLoad", "ffmpegExecuting", "ɵɵtext", "ɵɵtextInterpolate1", "ctx_r2", "currentLoading", "name", "ɵɵtextInterpolate", "ctx_r3", "executingProgressLabel", "executingTime", "executingProgress", "baseURL", "GeneratorComponent", "constructor", "quranService", "loaded", "ffmpeg", "message", "ayatTexts", "ayahtTextAndAudio", "clock", "previousPercentage", "pickedVideo", "ngAfterViewInit", "_this", "_asyncToGenerator", "load", "GetAllSuras", "subscribe", "GetReciters", "surah<PERSON>umber", "reciter", "startAyah", "endAyah", "_this2", "reciterId", "Number", "parseInt", "start", "end", "GetAyatTexts", "to<PERSON>romise", "blobs", "GetAyahsAudio", "transcode", "findIndex", "x", "surahName", "GetProgressText", "url", "recieved", "toString", "percentage", "getDownloadProgress", "_this3", "getPlatform", "on", "progress", "time", "set", "Math", "floor", "coreURL", "ev", "received", "wasmURL", "classWorkerURL", "window", "location", "href", "audios", "_this4", "transcodeAndroid", "audioNames", "index", "length", "audioData", "duration", "getDuration", "writeFile", "push", "text", "filelist", "join", "commands", "randomVideo", "max", "round", "random", "finalVideoName", "exec", "subtitleFile", "getSubtitlesAsAss", "command", "fileData", "readFile", "data", "Uint8Array", "URL", "createObjectURL", "Blob", "buffer", "type", "alignment", "fontName", "fontsize", "assContent", "alignmentCode", "startTime", "for<PERSON>ach", "subtitle", "endTime", "start_time_str", "formatTime", "end_time_str", "seconds", "hours", "minutes", "remainingSeconds", "fround", "hundredths", "toFixed", "padStart", "ArrayToBase64", "Array", "binary", "len", "byteLength", "i", "String", "fromCharCode", "btoa", "_this5", "requestPermissions", "error", "dirRes", "readdir", "directory", "Documents", "path", "file", "files", "deleteFile", "uri", "rmdir", "recursive", "mkdir", "cachePath", "OutputcachePath", "audioInfos", "videoResult", "subtitleResult", "encoding", "UTF8", "rename", "from", "to", "fontResult", "console", "log", "warn", "res3", "returnCode", "Date", "now", "alert", "title", "buttonTitle", "ɵɵdirectiveInject", "i1", "QuranService", "i2", "HelperService", "selectors", "decls", "vars", "consts", "template", "GeneratorComponent_Template", "rf", "ctx", "GeneratorComponent_div_4_Template", "GeneratorComponent_ng_template_5_Template", "ɵɵtemplateRefExtractor", "GeneratorComponent_div_7_Template", "GeneratorComponent_Template_p_dialog_visibleChange_8_listener", "GeneratorComponent_Template_app_videos_dialog_pickedVideo_9_listener", "_r1", "ɵɵstyleMap", "ɵɵpureFunction0", "_c0", "_c1"], "sources": ["C:\\Users\\<USER>\\Desktop\\تطبيق ناشر ايات قئانية\\QuranVidGen\\src\\app\\GeneratorPage\\generator\\generator.component.ts", "C:\\Users\\<USER>\\Desktop\\تطبيق ناشر ايات قئانية\\QuranVidGen\\src\\app\\GeneratorPage\\generator\\generator.component.html"], "sourcesContent": ["import { AfterViewInit, Component, signal } from '@angular/core';\r\nimport { FFmpeg } from '@ffmpeg/ffmpeg';\r\nimport { fetchFile, toBlobURL } from '@ffmpeg/util';\r\nimport { ProgressCallback } from '@ffmpeg/util/dist/cjs/types';\r\nimport { Reciter } from 'src/app/Interfaces/reciter';\r\nimport { Surah } from 'src/app/Interfaces/surah';\r\nimport { HelperService } from 'src/app/Services/helper.service';\r\nimport { QuranService } from 'src/app/Services/quran.service';\r\nimport { Directory, Encoding, Filesystem } from '@capacitor/filesystem';\r\nimport { Dialog } from '@capacitor/dialog';\r\n// FFmpeg functionality will use @ffmpeg/ffmpeg instead\r\nimport { Capacitor } from '@capacitor/core';\r\nconst baseURL = \"https://unpkg.com/@ffmpeg/core@0.12.6/dist/esm\";\r\nexport interface currentLoading{\r\n  name:string;\r\n  value:number;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-generator',\r\n  templateUrl: './generator.component.html',\r\n  styleUrls: ['./generator.component.scss']\r\n})\r\nexport class GeneratorComponent {\r\n  constructor(public quranService:QuranService,public helper:HelperService){}\r\n  loaded = false;\r\n  loadedAudio = false;\r\n  ffmpeg = new FFmpeg();\r\n  videoURL = \"\";\r\n  message = \"\";\r\n  currentLoading:currentLoading = {name:'',value:0};\r\n  progress:ProgressCallback | undefined;\r\n  firstLoad = true;\r\n  ayatTexts:string[] = [];\r\n  suras:Surah[] = [];\r\n  reciters:Reciter[] = [];\r\n  currentSurah:string = '';\r\n  currentReciterId:string = '';\r\n  ffmpegExecuting = false;\r\n  videoPickerVisible = false;\r\n  executingProgress = signal(0);\r\n  executingTime = 0;\r\n  ayahtTextAndAudio:{text:string,duration:number}[] = [];\r\n  executingProgressLabel = signal('');\r\n  fontSize:number = 18;\r\n  pickedVideo:number | undefined;\r\n  clock:number = 0;\r\n  getPickedVideo():string | undefined{\r\n    if(this.pickedVideo){\r\n      return `Video ${this.pickedVideo}`;\r\n    }\r\n    return undefined;\r\n  }\r\n  async ngAfterViewInit(){\r\n    await this.load();\r\n    this.quranService.GetAllSuras().subscribe(suras =>{\r\n      this.suras = suras;\r\n    })\r\n    this.quranService.GetReciters()?.subscribe(reciters =>{\r\n      this.reciters = reciters;\r\n    })\r\n\r\n  }\r\n  async GetAyahsAndLoadThem(surahNumber:number,reciter:string,startAyah:string,endAyah:string){\r\n    this.ayahtTextAndAudio = [];\r\n    this.ayatTexts = [];\r\n    let reciterId = Number.parseInt(reciter)\r\n    let start = Number.parseInt(startAyah)\r\n    let end = Number.parseInt(endAyah)\r\n    this.ayatTexts = await this.quranService.GetAyatTexts(surahNumber,start,end,'arabic').toPromise() ?? [];\r\n\r\n    let blobs = await this.quranService.GetAyahsAudio(reciterId,surahNumber,start,end).toPromise() ?? [];\r\n    await this.transcode(blobs);\r\n  }\r\n\r\n  GetCurrentSurahNumber():number{\r\n    this.currentSurah;\r\n    this.suras;\r\n    return this.suras.findIndex(x => x.surahName == this.currentSurah)+1;\r\n  }\r\n\r\n   previousPercentage = -1;\r\n  GetProgressText(url:string | URL,name:string,recieved:number){\r\n    url = url.toString();\r\n      let percentage = this.helper.getDownloadProgress(url,recieved);\r\n      if(percentage != this.previousPercentage){\r\n        this.currentLoading = {name:name,value:percentage}\r\n        this.previousPercentage = percentage;\r\n      }\r\n\r\n  }\r\n\r\n\r\n  async load() {\r\n    this.loaded = false;\r\n\r\n\r\n    if(Capacitor.getPlatform() != 'android'){\r\n      this.ffmpeg.on(\"log\", ({ message }) => {\r\n        this.message = message;\r\n      });\r\n      this.ffmpeg.on(\"progress\",({progress,time}) =>{\r\n        this.executingProgress.set(Math.floor(progress * 100));\r\n        this.executingTime = Math.floor(time / 1000000);\r\n      })\r\n\r\n      await this.ffmpeg.load({\r\n        coreURL: await toBlobURL(`assets/ffmpeg/ffmpeg-core.js`, 'text/javascript',true,((ev) => this.GetProgressText(ev.url,'Core Script',ev.received))),\r\n        wasmURL: await toBlobURL(`assets/ffmpeg/ffmpeg-core.wasm`, 'application/wasm',true,(ev => this.GetProgressText(ev.url,'Web Worker',ev.received))),\r\n        classWorkerURL: `${window.location.href}assets/ffmpeg/worker.js`\r\n    });\r\n    }\r\n  this.loaded = true;\r\n  };\r\n  async transcode(audios:Blob[]) {\r\n    if(Capacitor.getPlatform() == 'android'){\r\n      await this.transcodeAndroid(audios);\r\n      return;\r\n    }\r\n    this.firstLoad = true;\r\n    this.loadedAudio = true;\r\n\r\n    let audioNames: string[] = [];\r\n    for (let index = 0; index < audios.length; index++) {\r\n      let audioData = await fetchFile(audios[index]);\r\n      let duration = await this.helper.getDuration(audioData);\r\n      await this.ffmpeg.writeFile(index + '.mp3', audioData);\r\n      this.ayahtTextAndAudio.push({text:this.ayatTexts[index],duration:duration ?? 0})\r\n      // let duration = await this.ffmpeg.\r\n      audioNames.push(`file ${index}.mp3`);\r\n      if (index < audios.length - 1) { // Don't add silence after the last audio file\r\n        // audioNames.push(`file 'silence.mp3'`);\r\n      }\r\n    }\r\n    // let silenceCommand = ['-f', 'lavfi', '-i', 'anullsrc', '-t', '0.5', 'silence.mp3'];\r\n    // await this.ffmpeg.exec(silenceCommand);\r\n    // Create a text file with the names of the audio files to be concatenated\r\n    this.executingProgressLabel.set('Generating Subtitles');\r\n    let filelist = audioNames.join('\\n');\r\n    await this.ffmpeg.writeFile('filelist.txt', filelist);\r\n    this.executingProgressLabel.set('Generating Audio');\r\n    // Use the concat demuxer in ffmpeg\r\n    let commands = ['-f', 'concat', '-safe', '0', '-i', 'filelist.txt', '-c', 'copy', 'output.mp3'];\r\n    let randomVideo = Math.max(Math.round((Math.random() * 15)),1)\r\n    let finalVideoName = this.pickedVideo ? this.pickedVideo : randomVideo;\r\n    await this.ffmpeg.writeFile('video.mp4',await fetchFile(`/assets/videos/${finalVideoName}.mp4`));\r\n    await this.ffmpeg.exec(commands);\r\n    this.executingProgressLabel.set('Merging Audio with Video');\r\n    // let subtitleFile = new TextEncoder().encode(this.getSubTitles());\r\n    let subtitleFile = this.getSubtitlesAsAss('center','Al-QuranAlKareem',this.fontSize.toString());\r\n    await this.ffmpeg.writeFile('subtitles.ass',subtitleFile);\r\n    await this.ffmpeg.writeFile('/tmp/Al-QuranAlKareem',await fetchFile('/assets/fonts/Al-QuranAlKareem.ttf'));\r\n    // await this.ffmpeg.writeFile('subtitles.ass',await fetchFile('/assets/subs/test.ass'));\r\n    this.ffmpegExecuting = true;\r\n    this.executingProgress.set(0);\r\n    await this.ffmpeg.exec(['-stream_loop', '-1', '-i', 'video.mp4', '-i', 'output.mp3', '-c:v', 'copy', '-c:a', 'aac', '-map', '0:v:0', '-map', '1:a:0', '-shortest','output.mp4']);\r\n    //:fontsdir=/tmp:force_style='Fontname=Arimo,Fontsize=24,PrimaryColour=&H00FFFFFF,OutlineColour=&H000000FF,BackColour=&H00000000,Bold=1,Italic=0,Alignment=2,MarginV=40\r\n    let command = ['-i','output.mp4',\"-vf\",\"ass=subtitles.ass:fontsdir=tmp\",\"-c:v\",\"libx264\",\"-preset\",\"ultrafast\",\"-crf\",\"32\",\"-c:a\",\"copy\",'outputsub.mp4'];\r\n    await this.ffmpeg.exec(command);\r\n    const fileData = await this.ffmpeg.readFile('outputsub.mp4');\r\n    const data = new Uint8Array(fileData as ArrayBuffer);\r\n    this.videoURL = URL.createObjectURL(new Blob([data.buffer],{type:'video/mp4'}))\r\n\r\n        // let result:number = await this.ffmpeg.exec(commands);\r\n        // if(result != 0)return;\r\n        // const fileData = await this.ffmpeg.readFile('output.mp3');\r\n        // const data = new Uint8Array(fileData as ArrayBuffer);\r\n        // this.videoURL = URL.createObjectURL(\r\n        //   new Blob([data.buffer], { type: 'audio/mpeg' })\r\n        // );\r\n\r\n        this.loadedAudio = true;\r\n        this.ffmpegExecuting = false;\r\n\r\n      };\r\n\r\n      getSubtitlesAsAss(alignment: string,fontName:string,fontsize:string='16'): string {\r\n        let assContent = `[Script Info]\r\n; Script generated by Ebby.co\r\nScriptType: v4.00+\r\nScaledBorderAndShadow: yes\r\n\r\n[V4+ Styles]\r\nFormat: Name, Fontname, Fontsize, PrimaryColour, SecondaryColour, OutlineColour, BackColour, Bold, Italic, Underline, StrikeOut, ScaleX, ScaleY, Spacing, Angle, BorderStyle, Outline, Shadow, Alignment, MarginL, MarginR, MarginV, Encoding\r\nStyle: Default,${fontName},${fontsize},&Hffffff,&Hffffff,&H000000,&H0,0,0,0,0,100,100,0,0,0,1,1,5,0,0,0,0,UTF-8\r\n\r\n[Events]\r\nFormat: Layer, Start, End, Style, Name, MarginL, MarginR, MarginV, Effect, Text\\n`;\r\n\r\n        // Set alignment based on the provided parameter\r\n        let alignmentCode = \"10\"; // Default: left alignment\r\n        if (alignment === \"center\") {\r\n            alignmentCode = \"2\";\r\n        } else if (alignment === \"right\") {\r\n            alignmentCode = \"8\";\r\n        }\r\n\r\n        let startTime = 0;\r\n        this.ayahtTextAndAudio.forEach((subtitle, index) => {\r\n            let endTime = startTime + subtitle.duration;\r\n            let start_time_str = this.formatTime(startTime);\r\n            let end_time_str = this.formatTime(endTime);\r\n\r\n            // let end = new Date(endTime * 1000).toISOString().substr(11, 12);\r\n\r\n            assContent += `Dialogue: 0,${start_time_str},${end_time_str},Default,,0,0,0,,${subtitle['text']}\\n`;\r\n            startTime = endTime;\r\n        });\r\n\r\n        return assContent;\r\n    }\r\n\r\n    formatTime(seconds: number): string {\r\n      const hours = Math.floor(seconds / 3600);\r\n      const minutes = Math.floor((seconds % 3600) / 60);\r\n      const remainingSeconds = Math.fround(seconds % 60);\r\n      const hundredths = Math.floor((remainingSeconds % 1) * 100).toFixed(2);\r\n\r\n      return `${hours}:${minutes.toString().padStart(2, '0')}:${remainingSeconds.toFixed(2).toString().padStart(2, '0')}:${hundredths.toString().padStart(2, '0')}`;\r\n  }\r\n\r\n  ArrayToBase64( Array:Uint8Array ) {\r\n    var binary = '';\r\n    var len = Array.byteLength;\r\n    for (var i = 0; i < len; i++) {\r\n        binary += String.fromCharCode( Array[ i ] );\r\n    }\r\n    return window.btoa( binary );\r\n}\r\n\r\nasync transcodeAndroid(audios:Blob[]) {\r\n  try {\r\n    // if(!((await Filesystem.checkPermissions()).publicStorage == 'granted')){\r\n    //   await Filesystem.requestPermissions()\r\n    // }\r\n    await Filesystem.requestPermissions();\r\n\r\n  } catch (error) {\r\n\r\n  }\r\n  try {\r\n    let dirRes = await Filesystem.readdir({directory:Directory.Documents,path:'quranvideos'});\r\n    for(let file of dirRes.files){\r\n      await Filesystem.deleteFile({path:file.uri});\r\n    }\r\n    await Filesystem.rmdir({directory:Directory.Documents,path:'quranvideos',recursive:true});\r\n  } catch (error) {\r\n\r\n  }\r\n  try {\r\n    await Filesystem.mkdir({path:'quranvideos',directory:Directory.Documents});\r\n  } catch (error) {\r\n\r\n  }\r\n  let cachePath = '/storage/emulated/0/Documents/quranvideos'\r\n  let OutputcachePath = 'storage/emulated/0/Documents/quranvideos'\r\n  this.firstLoad = true;\r\n  this.loadedAudio = true;\r\n  let audioInfos:{name:string,duration:number | undefined,data:Uint8Array}[] = [];\r\n  let audioNames: string[] = [];\r\n  for (let index = 0; index < audios.length; index++) {\r\n    let audioData = await fetchFile(audios[index]);\r\n    let duration = await this.helper.getDuration(audioData);\r\n    // await this.ffmpeg.writeFile(index + '.mp3', audioData);\r\n    await Filesystem.writeFile({\r\n      directory:Directory.Documents,\r\n      path:`quranvideos/${index+1}.mp3`,\r\n      data:this.ArrayToBase64(audioData)\r\n    })\r\n    audioInfos.push({name:`${index+1}.mp3`,duration:duration,data:audioData})\r\n    this.ayahtTextAndAudio.push({text:this.ayatTexts[index],duration:duration ?? 0})\r\n    // let duration = await this.ffmpeg.\r\n    audioNames.push(`file ${cachePath}/${index+1}.mp3`);\r\n    if (index < audios.length - 1) { // Don't add silence after the last audio file\r\n      // audioNames.push(`file 'silence.mp3'`);\r\n    }\r\n  }\r\n  // let silenceCommand = ['-f', 'lavfi', '-i', 'anullsrc', '-t', '0.5', 'silence.mp3'];\r\n  // await this.ffmpeg.exec(silenceCommand);\r\n  // Create a text file with the names of the audio files to be concatenated\r\n  this.executingProgressLabel.set('Generating Subtitles');\r\n  let filelist = audioNames.join('\\n');\r\n  // await this.ffmpeg.writeFile('filelist.txt', filelist);\r\n  this.executingProgressLabel.set('Generating Audio');\r\n  // Use the concat demuxer in ffmpeg\r\n  await Filesystem.writeFile({\r\n    data: btoa(filelist),\r\n    directory:Directory.Documents,\r\n    path: 'quranvideos/filelist.txt'\r\n  });\r\n  // fmp.exec()\r\n  // await FFmpegKitPlugin.scanFile({\r\n  //   directory: 'DOCUMENTS',\r\n  //   path: 'quranvideos'\r\n  // })\r\n  let commands = ['-f', 'concat', '-safe', '0', '-i', `${cachePath}/filelist.txt`, '-c', 'copy', `${OutputcachePath}/output.mp3`];\r\n  // // let commands = ['-f', 'concat', '-i', `${cachePath}/filelist.txt`, '-c', 'copy', `${OutputcachePath}/output.mp3`];\r\n  // let res = await FFmpegKitPlugin.execute({command:commands.join(' ')});\r\n  // console.warn(res,res.returnCode);\r\n\r\n  let randomVideo = Math.max(Math.round((Math.random() * 15)),1)\r\n  let finalVideoName = this.pickedVideo ? this.pickedVideo : randomVideo;\r\n  let videoResult = await Filesystem.writeFile({\r\n    data: this.ArrayToBase64(await fetchFile(`/assets/videos/${finalVideoName}.mp4`)),\r\n    directory: Directory.Documents,\r\n    path: 'quranvideos/video.mp4'\r\n  });\r\n  // let mp3Result = await FFmpegKitPlugin.execute({command:commands.join(' ')});\r\n  // console.warn('mp3Result',mp3Result.returnCode);\r\n\r\n  this.executingProgressLabel.set('Merging Audio with Video');\r\n  // let subtitleFile = new TextEncoder().encode(this.getSubTitles());\r\n  let subtitleFile = this.getSubtitlesAsAss('center','Al-QuranAlKareem',this.fontSize.toString());\r\n  // await this.ffmpeg.writeFile('subtitles.ass',subtitleFile);\r\n\r\n  let subtitleResult = await Filesystem.writeFile({\r\n    data:subtitleFile,\r\n    directory: Directory.Documents,\r\n    path: 'quranvideos/subtitles.txt',\r\n    encoding: Encoding.UTF8\r\n  })\r\n  await Filesystem.rename({\r\n    directory:Directory.Documents,\r\n    from: 'quranvideos/subtitles.txt',\r\n    to: 'quranvideos/subtitles.ass'\r\n  })\r\n  let fontResult = await Filesystem.writeFile({\r\n    data: this.ArrayToBase64(await fetchFile('/assets/fonts/Al-QuranAlKareem.ttf')),\r\n    directory: Directory.Documents,\r\n    path: 'quranvideos/Al-QuranAlKareem'\r\n  })\r\n  // await this.ffmpeg.writeFile('subtitles.ass',await fetchFile('/assets/subs/test.ass'));\r\n  this.ffmpegExecuting = true;\r\n  this.executingProgress.set(0);\r\n  // await this.ffmpeg.exec(['-stream_loop', '-1', '-i', 'video.mp4', '-i', `${cachePath}/output.mp3`, '-c:v', 'copy', '-c:a', 'aac', '-map', '0:v:0', '-map', '1:a:0', '-shortest',`${cachePath}/output.mp4`]);\r\n  // let res2 = await FFmpegKitPlugin.execute({command:['-fflags','+genpts','-stream_loop', '-1', '-i', `${cachePath}/video.mp4`,'-i', `${cachePath}/output.mp3`, '-c:v', 'copy','-fflags','+shortest', '-c:a', 'aac', '-map', '0:v:0', '-map', '1:a:0', '-shortest',`${OutputcachePath}/output.mp4`].join(' ')});\r\n  // ['-stream_loop', '-1', '-i', 'video.mp4', '-i', 'output.mp3', '-c:v', 'copy', '-c:a', 'aac', '-map', '0:v:0', '-map', '1:a:0', '-shortest','output.mp4']\r\n  console.log('----- start loop -----')\r\n  // let res2 = await FFmpegKitPlugin.execute({command:['-stream_loop', '-1', '-i', `${cachePath}/video.mp4`,'-i', `${cachePath}/output.mp3`, '-shortest', '-map','0:v:0', '-map', '1:a:0', '-y',`${OutputcachePath}/output.mp4`].join(' ')});\r\n  // let res2 = await FFmpegKitPlugin.execute({command:['-i', `${cachePath}/video.mp4`, '-i', `${cachePath}/output.mp3`, 'filter_complex', '[1:v]colorkey=0xFFFFFF:0.01:0.0[KeyedOverlay];[0:v][KeyedOverlay]overlay=shortest=1:format=auto[Composite]', '-map', '[Composite]', `${OutputcachePath}/output.mp4`].join(' ')});\r\n  // console.warn('loop result',res2.returnCode);\r\n\r\n\r\n  //:fontsdir=/tmp:force_style='Fontname=Arimo,Fontsize=24,PrimaryColour=&H00FFFFFF,OutlineColour=&H000000FF,BackColour=&H00000000,Bold=1,Italic=0,Alignment=2,MarginV=40\r\n  // let command = ['-i',`${cachePath}/output.mp4`,\"-vf\",`ass=${cachePath}/subtitles.ass:fontsdir=${cachePath}`,\"-c:v\",\"libx264\",\"-preset\",\"ultrafast\",\"-crf\",\"32\",\"-c:a\",\"copy\",`${OutputcachePath}/outputsub.mp4`];\r\n  let command = ['-i',`${cachePath}/output.mp4`,\"-vf\",`ass=${cachePath}/subtitles.ass:fontsdir=${cachePath}`,\"-c:a\",\"copy\",`${OutputcachePath}/outputsub.mp4`];\r\n  // let res3 = await FFmpegKitPlugin.execute({command:command.join(' ')})\r\n  console.warn('res3',res3.returnCode);\r\n\r\n  // await this.ffmpeg.exec(command);\r\n\r\n\r\n\r\n\r\n  const fileData = await Filesystem.readFile({directory:Directory.Documents,path:'quranvideos/outputsub.mp4',encoding:Encoding.UTF8});\r\n  this.videoURL =  this.ArrayToBase64(new Uint8Array(fileData.data as any))\r\n  try {\r\n    try {\r\n      await Filesystem.mkdir({\r\n        directory: Directory.Documents,\r\n        path: 'videos',\r\n        recursive:false\r\n\r\n      });\r\n    } catch (error) {\r\n\r\n    }\r\n    await Filesystem.writeFile({\r\n      data: this.videoURL,\r\n      path:`videos/generated-video-${Date.now()}.mp4`,\r\n      directory: Directory.Documents\r\n    });\r\n\r\n    await Dialog.alert({\r\n      title:'Video Saved!',\r\n      message: `Video has been saved to your device`,\r\n      buttonTitle: 'Ok'\r\n    })\r\n  } catch (error) {\r\n\r\n  }\r\n      // let result:number = await this.ffmpeg.exec(commands);\r\n      // if(result != 0)return;\r\n      // const fileData = await this.ffmpeg.readFile('output.mp3');\r\n      // const data = new Uint8Array(fileData as ArrayBuffer);\r\n      // this.videoURL = URL.createObjectURL(\r\n      //   new Blob([data.buffer], { type: 'audio/mpeg' })\r\n      // );\r\n\r\n      this.loadedAudio = true;\r\n      this.ffmpegExecuting = false;\r\n\r\n    };\r\n\r\n\r\n}\r\n", "<div class=\"main-container\">\r\n  <div class=\"title d-flex flex-column my-3 text-light text-center\">\r\n    <h3 class=\"app-title\">Quran Video Generator</h3>\r\n  </div>\r\n<div *ngIf=\"loaded;else progress\">\r\n  <div class=\"row  mx-auto\">\r\n    <div class=\"d-flex col-md-10 col-lg-8 col-xl-6 col-xxl-4 justify-content-center align-items-center flex-column mx-auto  mt-3 gap-3\">\r\n\r\n\r\n      <p-dropdown [options]=\"suras\" [optionLabel]=\"'surahName'\" class=\"w-100\" styleClass=\"w-100\" [(ngModel)]=\"currentSurah\" placeholder=\"Select a Surah\" (onChange)=\"helper.SurahNumberRestrict(GetCurrentSurahNumber(),start,end)\" [dropdownIcon]=\"'pi pi-book'\" [optionValue]=\"'surahName'\" [filter]=\"true\" ></p-dropdown>\r\n      <p-dropdown [options]=\"reciters\" [optionLabel]=\"'name'\" class=\"w-100\" styleClass=\"w-100\" [(ngModel)]=\"currentReciterId\" placeholder=\"Select a Reciter\" [dropdownIcon]=\"'pi pi-user'\" [optionValue]=\"'id'\"  ></p-dropdown>\r\n\r\n      <div class=\"p-inputgroup\">\r\n        <span class=\"p-inputgroup-addon\">\r\n          <i class=\"pi pi-play\"></i>\r\n      </span>\r\n        <input #start type=\"number\" [min]=\"1\" (input)=\"helper.InputNumberRestrict(start)\" pInputText placeholder=\"Start Ayah\" />\r\n        <span class=\"ms-2 rounded-start p-inputgroup-addon\">\r\n          <i class=\"pi pi-circle-on\"></i>\r\n        </span>\r\n        <input #end type=\"number\" [min]=\"1\" (input)=\"helper.InputNumberRestrict(end)\" pInputText placeholder=\"End Ayah\" />\r\n      </div>\r\n      <div class=\"p-inputgroup\">\r\n        <span class=\"p-inputgroup-addon\">\r\n          <img src=\"assets/images/font.svg\" alt=\"\">\r\n        </span>\r\n        <input #end type=\"number\" [min]=\"1\" [max]=\"46\" [(ngModel)]=\"fontSize\" pInputText placeholder=\"Font Size\" />\r\n        <span class=\"ms-2 rounded-start p-inputgroup-addon\">\r\n          <i class=\"pi pi-video\"></i>\r\n        </span>\r\n        <div class=\"w-50\">\r\n          <p-button [label]=\"getPickedVideo() ?? 'Random Video'\" styleClass=\"rounded-end w-100\" class=\"w-100\"  severity=\"primary\" (onClick)=\"videoPickerVisible = true\"></p-button>\r\n        </div>\r\n      </div>\r\n\r\n\r\n\r\n      <div class=\"p-inputgroup my-3 w-auto\">\r\n\r\n        <p-button label=\"Generate\" [disabled]=\"currentReciterId == '' || currentSurah == '' || !start.value || !end.value\" [iconPos]=\"'right'\"  [rounded]=\"true\" severity=\"primary\"  (onClick)=\"GetAyahsAndLoadThem(GetCurrentSurahNumber(),currentReciterId,start.value,end.value)\">\r\n\r\n          <ng-template pTemplate=\"icon\">\r\n\r\n            <img src=\"assets/images/generate.svg\" alt=\"\">\r\n          </ng-template>\r\n        </p-button>\r\n      </div>\r\n    </div>\r\n  </div>\r\n\r\n\r\n  <div *ngIf=\"(loadedAudio || firstLoad) && !ffmpegExecuting;\" class=\"row my-5 justify-content-center mx-auto\" style=\"width: 90%;\">\r\n\r\n    <video *ngIf=\"videoURL != ''\" [src]=\"videoURL\" class=\"col-10 col-md-6 col-lg-4\" autoplay controls></video>\r\n  </div>\r\n\r\n</div>\r\n<ng-template #progress>\r\n  <div class=\"d-flex justify-content-center align-items-center\" style=\"height: 85dvh;width: 100dvw;\">\r\n\r\n    <div class=\"mx-auto w-50\">\r\n      <span class=\"app-title\">Loading {{currentLoading.name}} </span>\r\n      <p-toast></p-toast>\r\n      <p-progressBar  [value]=\"currentLoading.value\"></p-progressBar>\r\n  </div>\r\n  </div>\r\n</ng-template>\r\n\r\n\r\n<div *ngIf=\"ffmpegExecuting\" class=\"row my-5 justify-content-center mx-auto\" style=\"width: 90%\">\r\n\r\n  <div class=\"col-10 col-md-6 col-lg-4\">\r\n    <div class=\"d-flex justify-content-between\"><span>{{this.executingProgressLabel()}}</span> <span>Elapsed Time <span class=\"fw-bold app-title\">{{this.executingTime + ' s'}}</span></span></div>\r\n    <p-toast></p-toast>\r\n    <p-progressBar [ariaValueMin]=\"0\" [ariaValueMax]=\"100\" [value]=\"executingProgress()\"></p-progressBar>\r\n</div>\r\n</div>\r\n\r\n\r\n\r\n<p-dialog header=\"Background Video Gallery\" [(visible)]=\"videoPickerVisible\" [resizable]=\"true\" [breakpoints]=\"{ '960px': '75vw' }\" [style]=\"{width: '50vw'}\">\r\n  <app-videos-dialog (pickedVideo)=\"pickedVideo = $event;videoPickerVisible=false\"></app-videos-dialog>\r\n</p-dialog>\r\n</div>\r\n"], "mappings": ";AAAA,SAAmCA,MAAM,QAAQ,eAAe;AAChE,SAASC,MAAM,QAAQ,gBAAgB;AACvC,SAASC,SAAS,EAAEC,SAAS,QAAQ,cAAc;AAMnD,SAASC,SAAS,EAAEC,QAAQ,EAAEC,UAAU,QAAQ,uBAAuB;AACvE,SAASC,MAAM,QAAQ,mBAAmB;AAC1C;AACA,SAASC,SAAS,QAAQ,iBAAiB;;;;;;;;;;;;;;;;ICgC/BC,EAAA,CAAAC,SAAA,cAA6C;;;;;IAUrDD,EAAA,CAAAC,SAAA,gBAA0G;;;;IAA5ED,EAAA,CAAAE,UAAA,QAAAC,MAAA,CAAAC,QAAA,EAAAJ,EAAA,CAAAK,aAAA,CAAgB;;;;;IAFhDL,EAAA,CAAAM,cAAA,cAAiI;IAE/HN,EAAA,CAAAO,UAAA,IAAAC,gDAAA,oBAA0G;IAC5GR,EAAA,CAAAS,YAAA,EAAM;;;;IADIT,EAAA,CAAAU,SAAA,GAAoB;IAApBV,EAAA,CAAAE,UAAA,SAAAS,MAAA,CAAAP,QAAA,OAAoB;;;;;;IAjDhCJ,EAAA,CAAAM,cAAA,UAAkC;IAK+DN,EAAA,CAAAY,UAAA,2BAAAC,sEAAAC,MAAA;MAAAd,EAAA,CAAAe,aAAA,CAAAC,IAAA;MAAA,MAAAC,OAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAA,OAAAlB,EAAA,CAAAmB,WAAA,CAAAF,OAAA,CAAAG,YAAA,GAAAN,MAAA;IAAA,EAA0B,sBAAAO,iEAAA;MAAArB,EAAA,CAAAe,aAAA,CAAAC,IAAA;MAAA,MAAAM,GAAA,GAAAtB,EAAA,CAAAuB,WAAA;MAAA,MAAAC,GAAA,GAAAxB,EAAA,CAAAuB,WAAA;MAAA,MAAAE,OAAA,GAAAzB,EAAA,CAAAkB,aAAA;MAAA,OAA0ClB,EAAA,CAAAmB,WAAA,CAAAM,OAAA,CAAAC,MAAA,CAAAC,mBAAA,CAA2BF,OAAA,CAAAG,qBAAA,EAAuB,EAAAN,GAAA,EAAAE,GAAA,CAAW;IAAA,EAAvG;IAAoLxB,EAAA,CAAAS,YAAA,EAAa;IACtTT,EAAA,CAAAM,cAAA,qBAA4M;IAAnHN,EAAA,CAAAY,UAAA,2BAAAiB,sEAAAf,MAAA;MAAAd,EAAA,CAAAe,aAAA,CAAAC,IAAA;MAAA,MAAAc,OAAA,GAAA9B,EAAA,CAAAkB,aAAA;MAAA,OAAAlB,EAAA,CAAAmB,WAAA,CAAAW,OAAA,CAAAC,gBAAA,GAAAjB,MAAA;IAAA,EAA8B;IAAqFd,EAAA,CAAAS,YAAA,EAAa;IAEzNT,EAAA,CAAAM,cAAA,cAA0B;IAEtBN,EAAA,CAAAC,SAAA,YAA0B;IAC9BD,EAAA,CAAAS,YAAA,EAAO;IACLT,EAAA,CAAAM,cAAA,oBAAwH;IAAlFN,EAAA,CAAAY,UAAA,mBAAAoB,yDAAA;MAAAhC,EAAA,CAAAe,aAAA,CAAAC,IAAA;MAAA,MAAAM,GAAA,GAAAtB,EAAA,CAAAuB,WAAA;MAAA,MAAAU,OAAA,GAAAjC,EAAA,CAAAkB,aAAA;MAAA,OAASlB,EAAA,CAAAmB,WAAA,CAAAc,OAAA,CAAAP,MAAA,CAAAQ,mBAAA,CAAAZ,GAAA,CAAiC;IAAA,EAAC;IAAjFtB,EAAA,CAAAS,YAAA,EAAwH;IACxHT,EAAA,CAAAM,cAAA,gBAAoD;IAClDN,EAAA,CAAAC,SAAA,aAA+B;IACjCD,EAAA,CAAAS,YAAA,EAAO;IACPT,EAAA,CAAAM,cAAA,qBAAkH;IAA9EN,EAAA,CAAAY,UAAA,mBAAAuB,0DAAA;MAAAnC,EAAA,CAAAe,aAAA,CAAAC,IAAA;MAAA,MAAAQ,GAAA,GAAAxB,EAAA,CAAAuB,WAAA;MAAA,MAAAa,OAAA,GAAApC,EAAA,CAAAkB,aAAA;MAAA,OAASlB,EAAA,CAAAmB,WAAA,CAAAiB,OAAA,CAAAV,MAAA,CAAAQ,mBAAA,CAAAV,GAAA,CAA+B;IAAA,EAAC;IAA7ExB,EAAA,CAAAS,YAAA,EAAkH;IAEpHT,EAAA,CAAAM,cAAA,eAA0B;IAEtBN,EAAA,CAAAC,SAAA,eAAyC;IAC3CD,EAAA,CAAAS,YAAA,EAAO;IACPT,EAAA,CAAAM,cAAA,qBAA2G;IAA5DN,EAAA,CAAAY,UAAA,2BAAAyB,kEAAAvB,MAAA;MAAAd,EAAA,CAAAe,aAAA,CAAAC,IAAA;MAAA,MAAAsB,OAAA,GAAAtC,EAAA,CAAAkB,aAAA;MAAA,OAAAlB,EAAA,CAAAmB,WAAA,CAAAmB,OAAA,CAAAC,QAAA,GAAAzB,MAAA;IAAA,EAAsB;IAArEd,EAAA,CAAAS,YAAA,EAA2G;IAC3GT,EAAA,CAAAM,cAAA,gBAAoD;IAClDN,EAAA,CAAAC,SAAA,aAA2B;IAC7BD,EAAA,CAAAS,YAAA,EAAO;IACPT,EAAA,CAAAM,cAAA,eAAkB;IACwGN,EAAA,CAAAY,UAAA,qBAAA4B,+DAAA;MAAAxC,EAAA,CAAAe,aAAA,CAAAC,IAAA;MAAA,MAAAyB,OAAA,GAAAzC,EAAA,CAAAkB,aAAA;MAAA,OAAAlB,EAAA,CAAAmB,WAAA,CAAAsB,OAAA,CAAAC,kBAAA,GAAgC,IAAI;IAAA,EAAC;IAAC1C,EAAA,CAAAS,YAAA,EAAW;IAM7KT,EAAA,CAAAM,cAAA,eAAsC;IAEyIN,EAAA,CAAAY,UAAA,qBAAA+B,+DAAA;MAAA3C,EAAA,CAAAe,aAAA,CAAAC,IAAA;MAAA,MAAAM,GAAA,GAAAtB,EAAA,CAAAuB,WAAA;MAAA,MAAAC,GAAA,GAAAxB,EAAA,CAAAuB,WAAA;MAAA,MAAAqB,OAAA,GAAA5C,EAAA,CAAAkB,aAAA;MAAA,OAAWlB,EAAA,CAAAmB,WAAA,CAAAyB,OAAA,CAAAC,mBAAA,CAAoBD,OAAA,CAAAhB,qBAAA,EAAuB,EAAAgB,OAAA,CAAAb,gBAAA,EAAAT,GAAA,CAAAwB,KAAA,EAAAtB,GAAA,CAAAsB,KAAA,CAAwC;IAAA,EAAC;IAE1Q9C,EAAA,CAAAO,UAAA,KAAAwC,gDAAA,0BAGc;IAChB/C,EAAA,CAAAS,YAAA,EAAW;IAMjBT,EAAA,CAAAO,UAAA,KAAAyC,wCAAA,kBAGM;IAERhD,EAAA,CAAAS,YAAA,EAAM;;;;;;;IA/CYT,EAAA,CAAAU,SAAA,GAAiB;IAAjBV,EAAA,CAAAE,UAAA,YAAA+C,MAAA,CAAAC,KAAA,CAAiB,wCAAAD,MAAA,CAAA7B,YAAA;IACjBpB,EAAA,CAAAU,SAAA,GAAoB;IAApBV,EAAA,CAAAE,UAAA,YAAA+C,MAAA,CAAAE,QAAA,CAAoB,mCAAAF,MAAA,CAAAlB,gBAAA;IAMF/B,EAAA,CAAAU,SAAA,GAAS;IAATV,EAAA,CAAAE,UAAA,UAAS;IAIXF,EAAA,CAAAU,SAAA,GAAS;IAATV,EAAA,CAAAE,UAAA,UAAS;IAMTF,EAAA,CAAAU,SAAA,GAAS;IAATV,EAAA,CAAAE,UAAA,UAAS,uBAAA+C,MAAA,CAAAV,QAAA;IAKvBvC,EAAA,CAAAU,SAAA,GAA4C;IAA5CV,EAAA,CAAAE,UAAA,WAAAkD,QAAA,GAAAH,MAAA,CAAAI,cAAA,gBAAAD,QAAA,KAAAE,SAAA,GAAAF,QAAA,kBAA4C;IAQ7BpD,EAAA,CAAAU,SAAA,GAAuF;IAAvFV,EAAA,CAAAE,UAAA,aAAA+C,MAAA,CAAAlB,gBAAA,UAAAkB,MAAA,CAAA7B,YAAA,WAAAE,GAAA,CAAAwB,KAAA,KAAAtB,GAAA,CAAAsB,KAAA,CAAuF;IAYlH9C,EAAA,CAAAU,SAAA,GAAqD;IAArDV,EAAA,CAAAE,UAAA,UAAA+C,MAAA,CAAAM,WAAA,IAAAN,MAAA,CAAAO,SAAA,MAAAP,MAAA,CAAAQ,eAAA,CAAqD;;;;;IAO3DzD,EAAA,CAAAM,cAAA,cAAmG;IAGvEN,EAAA,CAAA0D,MAAA,GAAgC;IAAA1D,EAAA,CAAAS,YAAA,EAAO;IAC/DT,EAAA,CAAAC,SAAA,cAAmB;IAEvBD,EAAA,CAAAS,YAAA,EAAM;;;;IAHsBT,EAAA,CAAAU,SAAA,GAAgC;IAAhCV,EAAA,CAAA2D,kBAAA,aAAAC,MAAA,CAAAC,cAAA,CAAAC,IAAA,MAAgC;IAExC9D,EAAA,CAAAU,SAAA,GAA8B;IAA9BV,EAAA,CAAAE,UAAA,UAAA0D,MAAA,CAAAC,cAAA,CAAAf,KAAA,CAA8B;;;;;IAMpD9C,EAAA,CAAAM,cAAA,cAAgG;IAG1CN,EAAA,CAAA0D,MAAA,GAAiC;IAAA1D,EAAA,CAAAS,YAAA,EAAO;IAACT,EAAA,CAAAM,cAAA,WAAM;IAAAN,EAAA,CAAA0D,MAAA,oBAAa;IAAA1D,EAAA,CAAAM,cAAA,eAAgC;IAAAN,EAAA,CAAA0D,MAAA,GAA6B;IAAA1D,EAAA,CAAAS,YAAA,EAAO;IAClLT,EAAA,CAAAC,SAAA,cAAmB;IAEvBD,EAAA,CAAAS,YAAA,EAAM;;;;IAHgDT,EAAA,CAAAU,SAAA,GAAiC;IAAjCV,EAAA,CAAA+D,iBAAA,CAAAC,MAAA,CAAAC,sBAAA,GAAiC;IAA2DjE,EAAA,CAAAU,SAAA,GAA6B;IAA7BV,EAAA,CAAA+D,iBAAA,CAAAC,MAAA,CAAAE,aAAA,QAA6B;IAE5JlE,EAAA,CAAAU,SAAA,GAAkB;IAAlBV,EAAA,CAAAE,UAAA,mBAAkB,+BAAA8D,MAAA,CAAAG,iBAAA;;;;;;;;;;;;;AD9DrC,MAAMC,OAAO,GAAG,gDAAgD;AAWhE,OAAM,MAAOC,kBAAkB;EAC7BC,YAAmBC,YAAyB,EAAQ7C,MAAoB;IAArD,KAAA6C,YAAY,GAAZA,YAAY;IAAqB,KAAA7C,MAAM,GAANA,MAAM;IAC1D,KAAA8C,MAAM,GAAG,KAAK;IACd,KAAAjB,WAAW,GAAG,KAAK;IACnB,KAAAkB,MAAM,GAAG,IAAIjF,MAAM,EAAE;IACrB,KAAAY,QAAQ,GAAG,EAAE;IACb,KAAAsE,OAAO,GAAG,EAAE;IACZ,KAAAb,cAAc,GAAkB;MAACC,IAAI,EAAC,EAAE;MAAChB,KAAK,EAAC;IAAC,CAAC;IAEjD,KAAAU,SAAS,GAAG,IAAI;IAChB,KAAAmB,SAAS,GAAY,EAAE;IACvB,KAAAzB,KAAK,GAAW,EAAE;IAClB,KAAAC,QAAQ,GAAa,EAAE;IACvB,KAAA/B,YAAY,GAAU,EAAE;IACxB,KAAAW,gBAAgB,GAAU,EAAE;IAC5B,KAAA0B,eAAe,GAAG,KAAK;IACvB,KAAAf,kBAAkB,GAAG,KAAK;IAC1B,KAAAyB,iBAAiB,GAAG5E,MAAM,CAAC,CAAC,CAAC;IAC7B,KAAA2E,aAAa,GAAG,CAAC;IACjB,KAAAU,iBAAiB,GAAmC,EAAE;IACtD,KAAAX,sBAAsB,GAAG1E,MAAM,CAAC,EAAE,CAAC;IACnC,KAAAgD,QAAQ,GAAU,EAAE;IAEpB,KAAAsC,KAAK,GAAU,CAAC;IAmCf,KAAAC,kBAAkB,GAAG,CAAC,CAAC;EAzDkD;EAuB1EzB,cAAcA,CAAA;IACZ,IAAG,IAAI,CAAC0B,WAAW,EAAC;MAClB,OAAO,SAAS,IAAI,CAACA,WAAW,EAAE;;IAEpC,OAAOzB,SAAS;EAClB;EACM0B,eAAeA,CAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MACnB,MAAMD,KAAI,CAACE,IAAI,EAAE;MACjBF,KAAI,CAACV,YAAY,CAACa,WAAW,EAAE,CAACC,SAAS,CAACnC,KAAK,IAAG;QAChD+B,KAAI,CAAC/B,KAAK,GAAGA,KAAK;MACpB,CAAC,CAAC;MACF+B,KAAI,CAACV,YAAY,CAACe,WAAW,EAAE,EAAED,SAAS,CAAClC,QAAQ,IAAG;QACpD8B,KAAI,CAAC9B,QAAQ,GAAGA,QAAQ;MAC1B,CAAC,CAAC;IAAA;EAEJ;EACMN,mBAAmBA,CAAC0C,WAAkB,EAACC,OAAc,EAACC,SAAgB,EAACC,OAAc;IAAA,IAAAC,MAAA;IAAA,OAAAT,iBAAA;MACzFS,MAAI,CAACf,iBAAiB,GAAG,EAAE;MAC3Be,MAAI,CAAChB,SAAS,GAAG,EAAE;MACnB,IAAIiB,SAAS,GAAGC,MAAM,CAACC,QAAQ,CAACN,OAAO,CAAC;MACxC,IAAIO,KAAK,GAAGF,MAAM,CAACC,QAAQ,CAACL,SAAS,CAAC;MACtC,IAAIO,GAAG,GAAGH,MAAM,CAACC,QAAQ,CAACJ,OAAO,CAAC;MAClCC,MAAI,CAAChB,SAAS,GAAG,OAAMgB,MAAI,CAACpB,YAAY,CAAC0B,YAAY,CAACV,WAAW,EAACQ,KAAK,EAACC,GAAG,EAAC,QAAQ,CAAC,CAACE,SAAS,EAAE,KAAI,EAAE;MAEvG,IAAIC,KAAK,GAAG,OAAMR,MAAI,CAACpB,YAAY,CAAC6B,aAAa,CAACR,SAAS,EAACL,WAAW,EAACQ,KAAK,EAACC,GAAG,CAAC,CAACE,SAAS,EAAE,KAAI,EAAE;MACpG,MAAMP,MAAI,CAACU,SAAS,CAACF,KAAK,CAAC;IAAC;EAC9B;EAEAvE,qBAAqBA,CAAA;IACnB,IAAI,CAACR,YAAY;IACjB,IAAI,CAAC8B,KAAK;IACV,OAAO,IAAI,CAACA,KAAK,CAACoD,SAAS,CAACC,CAAC,IAAIA,CAAC,CAACC,SAAS,IAAI,IAAI,CAACpF,YAAY,CAAC,GAAC,CAAC;EACtE;EAGAqF,eAAeA,CAACC,GAAgB,EAAC5C,IAAW,EAAC6C,QAAe;IAC1DD,GAAG,GAAGA,GAAG,CAACE,QAAQ,EAAE;IAClB,IAAIC,UAAU,GAAG,IAAI,CAACnF,MAAM,CAACoF,mBAAmB,CAACJ,GAAG,EAACC,QAAQ,CAAC;IAC9D,IAAGE,UAAU,IAAI,IAAI,CAAC/B,kBAAkB,EAAC;MACvC,IAAI,CAACjB,cAAc,GAAG;QAACC,IAAI,EAACA,IAAI;QAAChB,KAAK,EAAC+D;MAAU,CAAC;MAClD,IAAI,CAAC/B,kBAAkB,GAAG+B,UAAU;;EAG1C;EAGM1B,IAAIA,CAAA;IAAA,IAAA4B,MAAA;IAAA,OAAA7B,iBAAA;MACR6B,MAAI,CAACvC,MAAM,GAAG,KAAK;MAGnB,IAAGzE,SAAS,CAACiH,WAAW,EAAE,IAAI,SAAS,EAAC;QACtCD,MAAI,CAACtC,MAAM,CAACwC,EAAE,CAAC,KAAK,EAAE,CAAC;UAAEvC;QAAO,CAAE,KAAI;UACpCqC,MAAI,CAACrC,OAAO,GAAGA,OAAO;QACxB,CAAC,CAAC;QACFqC,MAAI,CAACtC,MAAM,CAACwC,EAAE,CAAC,UAAU,EAAC,CAAC;UAACC,QAAQ;UAACC;QAAI,CAAC,KAAI;UAC5CJ,MAAI,CAAC5C,iBAAiB,CAACiD,GAAG,CAACC,IAAI,CAACC,KAAK,CAACJ,QAAQ,GAAG,GAAG,CAAC,CAAC;UACtDH,MAAI,CAAC7C,aAAa,GAAGmD,IAAI,CAACC,KAAK,CAACH,IAAI,GAAG,OAAO,CAAC;QACjD,CAAC,CAAC;QAEF,MAAMJ,MAAI,CAACtC,MAAM,CAACU,IAAI,CAAC;UACrBoC,OAAO,QAAQ7H,SAAS,CAAC,8BAA8B,EAAE,iBAAiB,EAAC,IAAI,EAAG8H,EAAE,IAAKT,MAAI,CAACN,eAAe,CAACe,EAAE,CAACd,GAAG,EAAC,aAAa,EAACc,EAAE,CAACC,QAAQ,CAAE,CAAC;UACjJC,OAAO,QAAQhI,SAAS,CAAC,gCAAgC,EAAE,kBAAkB,EAAC,IAAI,EAAE8H,EAAE,IAAIT,MAAI,CAACN,eAAe,CAACe,EAAE,CAACd,GAAG,EAAC,YAAY,EAACc,EAAE,CAACC,QAAQ,CAAE,CAAC;UACjJE,cAAc,EAAE,GAAGC,MAAM,CAACC,QAAQ,CAACC,IAAI;SAC1C,CAAC;;MAEJf,MAAI,CAACvC,MAAM,GAAG,IAAI;IAAC;EACnB;EACM6B,SAASA,CAAC0B,MAAa;IAAA,IAAAC,MAAA;IAAA,OAAA9C,iBAAA;MAC3B,IAAGnF,SAAS,CAACiH,WAAW,EAAE,IAAI,SAAS,EAAC;QACtC,MAAMgB,MAAI,CAACC,gBAAgB,CAACF,MAAM,CAAC;QACnC;;MAEFC,MAAI,CAACxE,SAAS,GAAG,IAAI;MACrBwE,MAAI,CAACzE,WAAW,GAAG,IAAI;MAEvB,IAAI2E,UAAU,GAAa,EAAE;MAC7B,KAAK,IAAIC,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGJ,MAAM,CAACK,MAAM,EAAED,KAAK,EAAE,EAAE;QAClD,IAAIE,SAAS,SAAS5I,SAAS,CAACsI,MAAM,CAACI,KAAK,CAAC,CAAC;QAC9C,IAAIG,QAAQ,SAASN,MAAI,CAACtG,MAAM,CAAC6G,WAAW,CAACF,SAAS,CAAC;QACvD,MAAML,MAAI,CAACvD,MAAM,CAAC+D,SAAS,CAACL,KAAK,GAAG,MAAM,EAAEE,SAAS,CAAC;QACtDL,MAAI,CAACpD,iBAAiB,CAAC6D,IAAI,CAAC;UAACC,IAAI,EAACV,MAAI,CAACrD,SAAS,CAACwD,KAAK,CAAC;UAACG,QAAQ,EAACA,QAAQ,IAAI;QAAC,CAAC,CAAC;QAChF;QACAJ,UAAU,CAACO,IAAI,CAAC,QAAQN,KAAK,MAAM,CAAC;QACpC,IAAIA,KAAK,GAAGJ,MAAM,CAACK,MAAM,GAAG,CAAC,EAAE,CAAE;UAC/B;QAAA;;MAGJ;MACA;MACA;MACAJ,MAAI,CAAC/D,sBAAsB,CAACmD,GAAG,CAAC,sBAAsB,CAAC;MACvD,IAAIuB,QAAQ,GAAGT,UAAU,CAACU,IAAI,CAAC,IAAI,CAAC;MACpC,MAAMZ,MAAI,CAACvD,MAAM,CAAC+D,SAAS,CAAC,cAAc,EAAEG,QAAQ,CAAC;MACrDX,MAAI,CAAC/D,sBAAsB,CAACmD,GAAG,CAAC,kBAAkB,CAAC;MACnD;MACA,IAAIyB,QAAQ,GAAG,CAAC,IAAI,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG,EAAE,IAAI,EAAE,cAAc,EAAE,IAAI,EAAE,MAAM,EAAE,YAAY,CAAC;MAC/F,IAAIC,WAAW,GAAGzB,IAAI,CAAC0B,GAAG,CAAC1B,IAAI,CAAC2B,KAAK,CAAE3B,IAAI,CAAC4B,MAAM,EAAE,GAAG,EAAG,CAAC,EAAC,CAAC,CAAC;MAC9D,IAAIC,cAAc,GAAGlB,MAAI,CAACjD,WAAW,GAAGiD,MAAI,CAACjD,WAAW,GAAG+D,WAAW;MACtE,MAAMd,MAAI,CAACvD,MAAM,CAAC+D,SAAS,CAAC,WAAW,QAAO/I,SAAS,CAAC,kBAAkByJ,cAAc,MAAM,CAAC,CAAC;MAChG,MAAMlB,MAAI,CAACvD,MAAM,CAAC0E,IAAI,CAACN,QAAQ,CAAC;MAChCb,MAAI,CAAC/D,sBAAsB,CAACmD,GAAG,CAAC,0BAA0B,CAAC;MAC3D;MACA,IAAIgC,YAAY,GAAGpB,MAAI,CAACqB,iBAAiB,CAAC,QAAQ,EAAC,kBAAkB,EAACrB,MAAI,CAACzF,QAAQ,CAACqE,QAAQ,EAAE,CAAC;MAC/F,MAAMoB,MAAI,CAACvD,MAAM,CAAC+D,SAAS,CAAC,eAAe,EAACY,YAAY,CAAC;MACzD,MAAMpB,MAAI,CAACvD,MAAM,CAAC+D,SAAS,CAAC,uBAAuB,QAAO/I,SAAS,CAAC,oCAAoC,CAAC,CAAC;MAC1G;MACAuI,MAAI,CAACvE,eAAe,GAAG,IAAI;MAC3BuE,MAAI,CAAC7D,iBAAiB,CAACiD,GAAG,CAAC,CAAC,CAAC;MAC7B,MAAMY,MAAI,CAACvD,MAAM,CAAC0E,IAAI,CAAC,CAAC,cAAc,EAAE,IAAI,EAAE,IAAI,EAAE,WAAW,EAAE,IAAI,EAAE,YAAY,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,WAAW,EAAC,YAAY,CAAC,CAAC;MAChL;MACA,IAAIG,OAAO,GAAG,CAAC,IAAI,EAAC,YAAY,EAAC,KAAK,EAAC,gCAAgC,EAAC,MAAM,EAAC,SAAS,EAAC,SAAS,EAAC,WAAW,EAAC,MAAM,EAAC,IAAI,EAAC,MAAM,EAAC,MAAM,EAAC,eAAe,CAAC;MACzJ,MAAMtB,MAAI,CAACvD,MAAM,CAAC0E,IAAI,CAACG,OAAO,CAAC;MAC/B,MAAMC,QAAQ,SAASvB,MAAI,CAACvD,MAAM,CAAC+E,QAAQ,CAAC,eAAe,CAAC;MAC5D,MAAMC,IAAI,GAAG,IAAIC,UAAU,CAACH,QAAuB,CAAC;MACpDvB,MAAI,CAAC5H,QAAQ,GAAGuJ,GAAG,CAACC,eAAe,CAAC,IAAIC,IAAI,CAAC,CAACJ,IAAI,CAACK,MAAM,CAAC,EAAC;QAACC,IAAI,EAAC;MAAW,CAAC,CAAC,CAAC;MAE3E;MACA;MACA;MACA;MACA;MACA;MACA;MAEA/B,MAAI,CAACzE,WAAW,GAAG,IAAI;MACvByE,MAAI,CAACvE,eAAe,GAAG,KAAK;IAAC;EAE/B;EAEA4F,iBAAiBA,CAACW,SAAiB,EAACC,QAAe,EAACC,QAAA,GAAgB,IAAI;IACtE,IAAIC,UAAU,GAAG;;;;;;;iBAORF,QAAQ,IAAIC,QAAQ;;;kFAG6C;IAE1E;IACA,IAAIE,aAAa,GAAG,IAAI,CAAC,CAAC;IAC1B,IAAIJ,SAAS,KAAK,QAAQ,EAAE;MACxBI,aAAa,GAAG,GAAG;KACtB,MAAM,IAAIJ,SAAS,KAAK,OAAO,EAAE;MAC9BI,aAAa,GAAG,GAAG;;IAGvB,IAAIC,SAAS,GAAG,CAAC;IACjB,IAAI,CAACzF,iBAAiB,CAAC0F,OAAO,CAAC,CAACC,QAAQ,EAAEpC,KAAK,KAAI;MAC/C,IAAIqC,OAAO,GAAGH,SAAS,GAAGE,QAAQ,CAACjC,QAAQ;MAC3C,IAAImC,cAAc,GAAG,IAAI,CAACC,UAAU,CAACL,SAAS,CAAC;MAC/C,IAAIM,YAAY,GAAG,IAAI,CAACD,UAAU,CAACF,OAAO,CAAC;MAE3C;MAEAL,UAAU,IAAI,eAAeM,cAAc,IAAIE,YAAY,oBAAoBJ,QAAQ,CAAC,MAAM,CAAC,IAAI;MACnGF,SAAS,GAAGG,OAAO;IACvB,CAAC,CAAC;IAEF,OAAOL,UAAU;EACrB;EAEAO,UAAUA,CAACE,OAAe;IACxB,MAAMC,KAAK,GAAGxD,IAAI,CAACC,KAAK,CAACsD,OAAO,GAAG,IAAI,CAAC;IACxC,MAAME,OAAO,GAAGzD,IAAI,CAACC,KAAK,CAAEsD,OAAO,GAAG,IAAI,GAAI,EAAE,CAAC;IACjD,MAAMG,gBAAgB,GAAG1D,IAAI,CAAC2D,MAAM,CAACJ,OAAO,GAAG,EAAE,CAAC;IAClD,MAAMK,UAAU,GAAG5D,IAAI,CAACC,KAAK,CAAEyD,gBAAgB,GAAG,CAAC,GAAI,GAAG,CAAC,CAACG,OAAO,CAAC,CAAC,CAAC;IAEtE,OAAO,GAAGL,KAAK,IAAIC,OAAO,CAAClE,QAAQ,EAAE,CAACuE,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAIJ,gBAAgB,CAACG,OAAO,CAAC,CAAC,CAAC,CAACtE,QAAQ,EAAE,CAACuE,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAIF,UAAU,CAACrE,QAAQ,EAAE,CAACuE,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;EACjK;EAEAC,aAAaA,CAAEC,KAAgB;IAC7B,IAAIC,MAAM,GAAG,EAAE;IACf,IAAIC,GAAG,GAAGF,KAAK,CAACG,UAAU;IAC1B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,GAAG,EAAEE,CAAC,EAAE,EAAE;MAC1BH,MAAM,IAAII,MAAM,CAACC,YAAY,CAAEN,KAAK,CAAEI,CAAC,CAAE,CAAE;;IAE/C,OAAO7D,MAAM,CAACgE,IAAI,CAAEN,MAAM,CAAE;EAChC;EAEMrD,gBAAgBA,CAACF,MAAa;IAAA,IAAA8D,MAAA;IAAA,OAAA3G,iBAAA;MAClC,IAAI;QACF;QACA;QACA;QACA,MAAMrF,UAAU,CAACiM,kBAAkB,EAAE;OAEtC,CAAC,OAAOC,KAAK,EAAE,C;MAGhB,IAAI;QACF,IAAIC,MAAM,SAASnM,UAAU,CAACoM,OAAO,CAAC;UAACC,SAAS,EAACvM,SAAS,CAACwM,SAAS;UAACC,IAAI,EAAC;QAAa,CAAC,CAAC;QACzF,KAAI,IAAIC,IAAI,IAAIL,MAAM,CAACM,KAAK,EAAC;UAC3B,MAAMzM,UAAU,CAAC0M,UAAU,CAAC;YAACH,IAAI,EAACC,IAAI,CAACG;UAAG,CAAC,CAAC;;QAE9C,MAAM3M,UAAU,CAAC4M,KAAK,CAAC;UAACP,SAAS,EAACvM,SAAS,CAACwM,SAAS;UAACC,IAAI,EAAC,aAAa;UAACM,SAAS,EAAC;QAAI,CAAC,CAAC;OAC1F,CAAC,OAAOX,KAAK,EAAE,C;MAGhB,IAAI;QACF,MAAMlM,UAAU,CAAC8M,KAAK,CAAC;UAACP,IAAI,EAAC,aAAa;UAACF,SAAS,EAACvM,SAAS,CAACwM;QAAS,CAAC,CAAC;OAC3E,CAAC,OAAOJ,KAAK,EAAE,C;MAGhB,IAAIa,SAAS,GAAG,2CAA2C;MAC3D,IAAIC,eAAe,GAAG,0CAA0C;MAChEhB,MAAI,CAACrI,SAAS,GAAG,IAAI;MACrBqI,MAAI,CAACtI,WAAW,GAAG,IAAI;MACvB,IAAIuJ,UAAU,GAA+D,EAAE;MAC/E,IAAI5E,UAAU,GAAa,EAAE;MAC7B,KAAK,IAAIC,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGJ,MAAM,CAACK,MAAM,EAAED,KAAK,EAAE,EAAE;QAClD,IAAIE,SAAS,SAAS5I,SAAS,CAACsI,MAAM,CAACI,KAAK,CAAC,CAAC;QAC9C,IAAIG,QAAQ,SAASuD,MAAI,CAACnK,MAAM,CAAC6G,WAAW,CAACF,SAAS,CAAC;QACvD;QACA,MAAMxI,UAAU,CAAC2I,SAAS,CAAC;UACzB0D,SAAS,EAACvM,SAAS,CAACwM,SAAS;UAC7BC,IAAI,EAAC,eAAejE,KAAK,GAAC,CAAC,MAAM;UACjCsB,IAAI,EAACoC,MAAI,CAACT,aAAa,CAAC/C,SAAS;SAClC,CAAC;QACFyE,UAAU,CAACrE,IAAI,CAAC;UAAC3E,IAAI,EAAC,GAAGqE,KAAK,GAAC,CAAC,MAAM;UAACG,QAAQ,EAACA,QAAQ;UAACmB,IAAI,EAACpB;QAAS,CAAC,CAAC;QACzEwD,MAAI,CAACjH,iBAAiB,CAAC6D,IAAI,CAAC;UAACC,IAAI,EAACmD,MAAI,CAAClH,SAAS,CAACwD,KAAK,CAAC;UAACG,QAAQ,EAACA,QAAQ,IAAI;QAAC,CAAC,CAAC;QAChF;QACAJ,UAAU,CAACO,IAAI,CAAC,QAAQmE,SAAS,IAAIzE,KAAK,GAAC,CAAC,MAAM,CAAC;QACnD,IAAIA,KAAK,GAAGJ,MAAM,CAACK,MAAM,GAAG,CAAC,EAAE,CAAE;UAC/B;QAAA;;MAGJ;MACA;MACA;MACAyD,MAAI,CAAC5H,sBAAsB,CAACmD,GAAG,CAAC,sBAAsB,CAAC;MACvD,IAAIuB,QAAQ,GAAGT,UAAU,CAACU,IAAI,CAAC,IAAI,CAAC;MACpC;MACAiD,MAAI,CAAC5H,sBAAsB,CAACmD,GAAG,CAAC,kBAAkB,CAAC;MACnD;MACA,MAAMvH,UAAU,CAAC2I,SAAS,CAAC;QACzBiB,IAAI,EAAEmC,IAAI,CAACjD,QAAQ,CAAC;QACpBuD,SAAS,EAACvM,SAAS,CAACwM,SAAS;QAC7BC,IAAI,EAAE;OACP,CAAC;MACF;MACA;MACA;MACA;MACA;MACA,IAAIvD,QAAQ,GAAG,CAAC,IAAI,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG+D,SAAS,eAAe,EAAE,IAAI,EAAE,MAAM,EAAE,GAAGC,eAAe,aAAa,CAAC;MAC/H;MACA;MACA;MAEA,IAAI/D,WAAW,GAAGzB,IAAI,CAAC0B,GAAG,CAAC1B,IAAI,CAAC2B,KAAK,CAAE3B,IAAI,CAAC4B,MAAM,EAAE,GAAG,EAAG,CAAC,EAAC,CAAC,CAAC;MAC9D,IAAIC,cAAc,GAAG2C,MAAI,CAAC9G,WAAW,GAAG8G,MAAI,CAAC9G,WAAW,GAAG+D,WAAW;MACtE,IAAIiE,WAAW,SAASlN,UAAU,CAAC2I,SAAS,CAAC;QAC3CiB,IAAI,EAAEoC,MAAI,CAACT,aAAa,OAAO3L,SAAS,CAAC,kBAAkByJ,cAAc,MAAM,CAAC,CAAC;QACjFgD,SAAS,EAAEvM,SAAS,CAACwM,SAAS;QAC9BC,IAAI,EAAE;OACP,CAAC;MACF;MACA;MAEAP,MAAI,CAAC5H,sBAAsB,CAACmD,GAAG,CAAC,0BAA0B,CAAC;MAC3D;MACA,IAAIgC,YAAY,GAAGyC,MAAI,CAACxC,iBAAiB,CAAC,QAAQ,EAAC,kBAAkB,EAACwC,MAAI,CAACtJ,QAAQ,CAACqE,QAAQ,EAAE,CAAC;MAC/F;MAEA,IAAIoG,cAAc,SAASnN,UAAU,CAAC2I,SAAS,CAAC;QAC9CiB,IAAI,EAACL,YAAY;QACjB8C,SAAS,EAAEvM,SAAS,CAACwM,SAAS;QAC9BC,IAAI,EAAE,2BAA2B;QACjCa,QAAQ,EAAErN,QAAQ,CAACsN;OACpB,CAAC;MACF,MAAMrN,UAAU,CAACsN,MAAM,CAAC;QACtBjB,SAAS,EAACvM,SAAS,CAACwM,SAAS;QAC7BiB,IAAI,EAAE,2BAA2B;QACjCC,EAAE,EAAE;OACL,CAAC;MACF,IAAIC,UAAU,SAASzN,UAAU,CAAC2I,SAAS,CAAC;QAC1CiB,IAAI,EAAEoC,MAAI,CAACT,aAAa,OAAO3L,SAAS,CAAC,oCAAoC,CAAC,CAAC;QAC/EyM,SAAS,EAAEvM,SAAS,CAACwM,SAAS;QAC9BC,IAAI,EAAE;OACP,CAAC;MACF;MACAP,MAAI,CAACpI,eAAe,GAAG,IAAI;MAC3BoI,MAAI,CAAC1H,iBAAiB,CAACiD,GAAG,CAAC,CAAC,CAAC;MAC7B;MACA;MACA;MACAmG,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC;MACrC;MACA;MACA;MAGA;MACA;MACA,IAAIlE,OAAO,GAAG,CAAC,IAAI,EAAC,GAAGsD,SAAS,aAAa,EAAC,KAAK,EAAC,OAAOA,SAAS,2BAA2BA,SAAS,EAAE,EAAC,MAAM,EAAC,MAAM,EAAC,GAAGC,eAAe,gBAAgB,CAAC;MAC5J;MACAU,OAAO,CAACE,IAAI,CAAC,MAAM,EAACC,IAAI,CAACC,UAAU,CAAC;MAEpC;MAKA,MAAMpE,QAAQ,SAAS1J,UAAU,CAAC2J,QAAQ,CAAC;QAAC0C,SAAS,EAACvM,SAAS,CAACwM,SAAS;QAACC,IAAI,EAAC,2BAA2B;QAACa,QAAQ,EAACrN,QAAQ,CAACsN;MAAI,CAAC,CAAC;MACnIrB,MAAI,CAACzL,QAAQ,GAAIyL,MAAI,CAACT,aAAa,CAAC,IAAI1B,UAAU,CAACH,QAAQ,CAACE,IAAW,CAAC,CAAC;MACzE,IAAI;QACF,IAAI;UACF,MAAM5J,UAAU,CAAC8M,KAAK,CAAC;YACrBT,SAAS,EAAEvM,SAAS,CAACwM,SAAS;YAC9BC,IAAI,EAAE,QAAQ;YACdM,SAAS,EAAC;WAEX,CAAC;SACH,CAAC,OAAOX,KAAK,EAAE,C;QAGhB,MAAMlM,UAAU,CAAC2I,SAAS,CAAC;UACzBiB,IAAI,EAAEoC,MAAI,CAACzL,QAAQ;UACnBgM,IAAI,EAAC,0BAA0BwB,IAAI,CAACC,GAAG,EAAE,MAAM;UAC/C3B,SAAS,EAAEvM,SAAS,CAACwM;SACtB,CAAC;QAEF,MAAMrM,MAAM,CAACgO,KAAK,CAAC;UACjBC,KAAK,EAAC,cAAc;UACpBrJ,OAAO,EAAE,qCAAqC;UAC9CsJ,WAAW,EAAE;SACd,CAAC;OACH,CAAC,OAAOjC,KAAK,EAAE,C;MAGZ;MACA;MACA;MACA;MACA;MACA;MACA;MAEAF,MAAI,CAACtI,WAAW,GAAG,IAAI;MACvBsI,MAAI,CAACpI,eAAe,GAAG,KAAK;IAAC;EAE/B;;;uBAjXSY,kBAAkB,EAAArE,EAAA,CAAAiO,iBAAA,CAAAC,EAAA,CAAAC,YAAA,GAAAnO,EAAA,CAAAiO,iBAAA,CAAAG,EAAA,CAAAC,aAAA;IAAA;EAAA;;;YAAlBhK,kBAAkB;MAAAiK,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,4BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCvB/B5O,EAAA,CAAAM,cAAA,aAA4B;UAEFN,EAAA,CAAA0D,MAAA,4BAAqB;UAAA1D,EAAA,CAAAS,YAAA,EAAK;UAEpDT,EAAA,CAAAO,UAAA,IAAAuO,iCAAA,mBAoDM;UACN9O,EAAA,CAAAO,UAAA,IAAAwO,yCAAA,gCAAA/O,EAAA,CAAAgP,sBAAA,CASc;UAGdhP,EAAA,CAAAO,UAAA,IAAA0O,iCAAA,kBAOM;UAINjP,EAAA,CAAAM,cAAA,kBAA8J;UAAlHN,EAAA,CAAAY,UAAA,2BAAAsO,8DAAApO,MAAA;YAAA,OAAA+N,GAAA,CAAAnM,kBAAA,GAAA5B,MAAA;UAAA,EAAgC;UAC1Ed,EAAA,CAAAM,cAAA,2BAAiF;UAA9DN,EAAA,CAAAY,UAAA,yBAAAuO,qEAAArO,MAAA;YAAA+N,GAAA,CAAA9J,WAAA,GAAAjE,MAAA;YAAA,OAAA+N,GAAA,CAAAnM,kBAAA,GAAuD,KAAK;UAAA,EAAC;UAAC1C,EAAA,CAAAS,YAAA,EAAoB;;;;UA7EjGT,EAAA,CAAAU,SAAA,GAAa;UAAbV,EAAA,CAAAE,UAAA,SAAA2O,GAAA,CAAArK,MAAA,CAAa,aAAA4K,GAAA;UAiEbpP,EAAA,CAAAU,SAAA,GAAqB;UAArBV,EAAA,CAAAE,UAAA,SAAA2O,GAAA,CAAApL,eAAA,CAAqB;UAWyGzD,EAAA,CAAAU,SAAA,GAAyB;UAAzBV,EAAA,CAAAqP,UAAA,CAAArP,EAAA,CAAAsP,eAAA,IAAAC,GAAA,EAAyB;UAAjHvP,EAAA,CAAAE,UAAA,YAAA2O,GAAA,CAAAnM,kBAAA,CAAgC,mCAAA1C,EAAA,CAAAsP,eAAA,IAAAE,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}